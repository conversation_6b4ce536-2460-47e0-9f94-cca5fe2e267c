package com.puree.hospital.common.mysql.utils;

import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/26 17:38
 * @description mysql批量操作工具类
 */
@Component
public class BatchUpdateUtil<T> {

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    /**
     * 根据记录id批量更新数据
     * 为什么要编写该批量更新的工具类而不使用MyBatis在xml中提供的<foreach>标签来进行批量更新？
     * 1、在MyBatis中，不管更新多少条数据，结果只返回0或1，当你想明确知道在这批更新操作中成功更新了多少条、失败更新了多少条时，是无法满足的。
     * 2、在Mybatis中，当我们想一次性批量更新上千条或上万条数据时，没办法使用批处理执行器ExecutorType.BATCH来提高执行效率。而该工具类可以控制每次提交批次的大小。
     * 注意：需要保证每个Mapper接口中都有一个 int updateById(xxx)的方法。
     * 调用示例：比如我们想批量更新user表中的多条记录，可以这么写：BatchUpdateUtil.updateBatchById(List<User> users, UserMapper.class, 1000);
     *
     * @param entityList 要更新的数据实体的List集合
     * @param claMap Mapper接口的类型
     * @param batchSize 更新批次数量，建议默认为1000（MySQL官方建议每次批量操作的记录数默认在1000左右，如果批次数量太高或过低都可能会使效率降低）
     * @return 该批次中成功更新的条数（可以与entityList集合中的元素个数作比较，确认是否全部更新成功）
     */
    public int updateBatchById(List<T> entityList, Class claMap, int batchSize) {
        //ExecutorType：指定执行器的类型，有如下三种类型可供选择
        //SIMPLE：简单执行器。每次执行SQL语句时，都会创建一个新的Statement对象。默认为SIMPLE。
        //REUSE：可重用执行器。在执行SQL语句时，会先尝试从缓存中获取Statement对象，如果缓存中不存在，则会创建一个新的Statement对象。这种执行器类型适用于执行频繁的SQL语句，可以减少Statement对象的创建次数，提高性能。
        //BATCH：批处理执行器。在执行批处理语句时，会先尝试从缓存中获取Statement对象，如果缓存中不存在，则会创建一个新的Statement对象。这种执行器类型适用于执行批处理语句，可以提高批处理语句的执行效率。
        //这里我们选择批处理执行器BATCH。
        SqlSession sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(ExecutorType.BATCH, false);

        //记录成功更新的条数
        int count = 0;

        //获取sqlStatement（要保证每个mapper接口都有一个updateById()方法，方法的返回值是int类型，方法的参数是entityList集合中的元素类型）
        String sqlStatement = claMap.getName() + ".updateById";
        try {
            count = batchExecute(entityList, sqlSession, sqlStatement, batchSize);
            //手动提交
            sqlSession.commit();
        } catch (Exception e) {
            sqlSession.rollback();
        } finally {
            sqlSession.close();
        }
        return count;
    }


    /**
     * 批量插入：其功能特点及用法和上面类似
     *
     * @param entityList 要插入的数据实体的List集合
     * @param claMap Mapper接口的类型
     * @param batchSize 每次插入的批次数量，建议默认为1000
     * @return 该批次中成功插入的条数（可以与entityList集合中的元素个数作比较，确认是否全部插入成功）
     */
    public int saveBatch(List<T> entityList, Class claMap, int batchSize) {
        SqlSession sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(ExecutorType.BATCH, false);

        //记录成功插入的条数
        int count = 0;

        //获取sqlStatement（要保证每个mapper接口都有一个savaBatch()方法，方法的返回值是int类型，方法的参数是entityList集合中的元素类型）
        String sqlStatement = claMap.getName() + ".savaBatch";
        try {
            count = batchExecute(entityList, sqlSession, sqlStatement, batchSize);
            //手动提交
            sqlSession.commit();
        } catch (Exception e) {
            sqlSession.rollback();
        } finally {
            sqlSession.close();
        }
        return count;
    }

    /**
     * 批量更新记录列表（适用于不是根据id来批量更新的场景）
     * @param entityList 要更新的数据实体的List集合
     * @param claMap Mapper接口的类型
     * @param batchSize 更新批次数量，建议默认为1000（MySQL官方建议每次批量操作的记录数默认在1000左右，如果批次数量太高或过低都可能会使效率降低）
     * @return 该批次中成功更新的条数（可以与entityList集合中的元素个数作比较，确认是否全部更新成功）
     */
    public int updateBatch(List<T> entityList, Class claMap, int batchSize) {
        //BATCH：批处理执行器。在执行批处理语句时，会先尝试从缓存中获取Statement对象，如果缓存中不存在，则会创建一个新的Statement对象。这种执行器类型适用于执行批处理语句，可以提高批处理语句的执行效率。
        SqlSession sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(ExecutorType.BATCH, false);
        //记录成功更新的条数
        int count = 0;
        //获取sqlStatement（要保证每个mapper接口都有一个updateEntity()方法，方法的返回值是int类型，方法的参数是entityList集合中的元素类型）
        String sqlStatement = claMap.getName() + ".updateEntity";
        try {
            count = batchExecute(entityList, sqlSession, sqlStatement, batchSize);
            //手动提交
            sqlSession.commit();
        } catch (Exception e) {
            sqlSession.rollback();
        } finally {
            sqlSession.close();
        }
        return count;
    }

    /**
     * 批量执行
     * @param entityList 要更新的数据实体的List集合
     * @param sqlSession
     * @param sqlStatement
     * @param batchSize 更新批次数量
     * @return 该批次中成功更新的条数
     */
    private int batchExecute(List<T> entityList, SqlSession sqlSession, String sqlStatement, int batchSize){
        int count = 0;
        for (int i = 1; i <= entityList.size(); i++) {
            sqlSession.update(sqlStatement, entityList.get(i-1));
            //达到每批次的数量后，执行批处理语句
            if((i % batchSize == 0) || i == entityList.size()){
                //获取批处理语句执行结果，只对ExecutorType.BATCH的时候才生效
                BatchResult batchResults = sqlSession.flushStatements().get(0);
                //updateCounts表示批量更新中，每条记录的更新结果：更新成功返回1，更新失败返回0
                count += Arrays.stream(batchResults.getUpdateCounts()).sum();
            }
        }
        return count;
    }

}