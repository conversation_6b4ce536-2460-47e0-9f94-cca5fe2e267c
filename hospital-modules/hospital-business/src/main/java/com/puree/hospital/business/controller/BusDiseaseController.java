package com.puree.hospital.business.controller;

import com.puree.hospital.business.domain.BusDisease;
import com.puree.hospital.business.service.IBusDiseaseService;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.TableDataInfo;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.operate.api.model.enums.OperatorType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 西医病种信息Controller
 *
 * <AUTHOR>
 * @date 2021-10-15
 */
@RestController
@RequestMapping("/disease")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BusDiseaseController extends BaseController {
    private final IBusDiseaseService busDiseaseService;

    /**
     * 查询西医病种信息列表
     */
    @PreAuthorize(hasPermi = "business:disease:list")
    @GetMapping("/list")
    @Log(title = "查询西医病种信息列表", businessType = BusinessType.QUERY)
    public TableDataInfo list(BusDisease busDisease) {
        startPage();
        List<BusDisease> list = busDiseaseService.selectBusDiseaseList(busDisease);
        return getDataTable(list);
    }

    /**
     * 获取西医病种信息详细信息
     */
    @PreAuthorize(hasPermi = "business:disease:query")
    @GetMapping(value = "/{id}")
    @Log(title = "西医病种信息", businessType = BusinessType.QUERY)
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(busDiseaseService.selectBusDiseaseById(id));
    }

    /**
     * 新增西医病种信息
     */
    @PreAuthorize(hasPermi = "business:disease:add")
    @Log(title = "西医病种信息", businessType = BusinessType.INSERT,operatorType = OperatorType.MANAGE)
    @PostMapping
    public AjaxResult add(@RequestBody BusDisease busDisease) {
        busDisease.setCreateBy(SecurityUtils.getUsername());
        return toAjax(busDiseaseService.insertBusDisease(busDisease));
    }

    /**
     * 修改西医病种信息
     */
    @PreAuthorize(hasPermi = "business:disease:edit")
    @Log(title = "西医病种信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PutMapping
    public AjaxResult edit(@RequestBody BusDisease busDisease) {
        busDisease.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(busDiseaseService.updateBusDisease(busDisease));
    }

    /**
     * 删除西医病种信息
     */
    @PreAuthorize(hasPermi = "business:disease:remove")
    @Log(title = "西医病种信息", businessType = BusinessType.DELETE,operatorType = OperatorType.MANAGE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(busDiseaseService.deleteBusDiseaseByIds(ids));
    }

    /**
     * 校验病种名称是否重复
     *
     * @param id
     * @param diseaseName
     * @return
     */
    @GetMapping(value = "/checkDuplicateName")
    @Log(title = "校验西医病种名称是否重复", businessType = BusinessType.OTHER,operatorType = OperatorType.MANAGE)
    public AjaxResult checkDuplicateName(Long id, String diseaseName) {
        return AjaxResult.success(busDiseaseService.checkDuplicateName(id, diseaseName));
    }

    /**
     * 校验ICD-10编码是否重复
     *
     * @param id
     * @param icdCode
     * @return
     */
    @GetMapping(value = "/checkDuplicateIcd")
    @Log(title = "校验ICD-10编码是否重复", businessType = BusinessType.OTHER,operatorType = OperatorType.MANAGE)
    public AjaxResult checkDuplicateIcd(Long id, String icdCode) {
        return AjaxResult.success(busDiseaseService.checkDuplicateIcd(id, icdCode));
    }

    /**
     * 西医病种停用启用操作
     *
     * @param busDisease
     * @return
     */
    @PreAuthorize(hasPermi = "business:disease:lock")
    @Log(title = "西医病种信息", businessType = BusinessType.UPDATE,operatorType = OperatorType.MANAGE)
    @PostMapping("/lock")
    public AjaxResult lock(@RequestBody BusDisease busDisease) {
        busDisease.setCreateBy(SecurityUtils.getUsername());
        return toAjax(busDiseaseService.lock(busDisease));
    }

}
