package com.puree.hospital.app.service.impl;

// import com.alibaba.fastjson.JSON; // 旧实现：数组解析使用，现按 String 透传，保留注释
// import com.alibaba.fastjson.TypeReference; // 旧实现：数组解析使用，现按 String 透传，保留注释
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.puree.hospital.app.domain.BusDoctorCaseTemplate;
import com.puree.hospital.app.domain.dto.CaseTemplateDto;
import com.puree.hospital.app.domain.vo.CaseTemplateVo;
import com.puree.hospital.app.mapper.BusDoctorCaseTemplateMapper;
import com.puree.hospital.app.service.IBusDoctorCaseTemplateService;
import com.puree.hospital.app.service.IDiagnosisValidationService;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
// import com.puree.hospital.setting.api.RemoteHospitalSettingApi; // 已停用：字段可见性过滤改为透传
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
// import org.springframework.util.CollectionUtils; // 旧实现：List 判空使用，现改为 String 判空
// import org.springframework.util.StringUtils; // 已不使用

import java.util.*;
import java.util.stream.Collectors;

/**
 * 医生病历模板服务实现
 * <AUTHOR>
 * @date 2025-08-08
 */
@Slf4j
@Service
public class BusDoctorCaseTemplateServiceImpl implements IBusDoctorCaseTemplateService {

    @Autowired
    private BusDoctorCaseTemplateMapper templateMapper;

    @Autowired
    private IDiagnosisValidationService diagnosisValidationService;

    // 旧实现依赖（医院配置开关）：已按“透传”策略停用，保留以便回滚
    // @Autowired
    // private RemoteHospitalSettingApi remoteHospitalSettingApi;

    @Override
    public List<CaseTemplateVo> getTemplateList(Long doctorId, Long hospitalId) {
        List<BusDoctorCaseTemplate> templates = templateMapper.selectByDoctorId(doctorId, hospitalId);
        return templates.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    @Override
    public CaseTemplateVo getTemplateById(Long templateId, Long hospitalId) {
        LambdaQueryWrapper<BusDoctorCaseTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusDoctorCaseTemplate::getId, templateId)
               .eq(BusDoctorCaseTemplate::getHospitalId, hospitalId)
               .eq(BusDoctorCaseTemplate::getIsDelete, 0);
        
        BusDoctorCaseTemplate template = templateMapper.selectOne(wrapper);
        if (template == null) {
            throw new ServiceException("模板不存在或已删除");
        }
        
        return convertToVo(template);
    }

    @Override
    public int insertTemplate(CaseTemplateDto templateDto) {
        // 模板名称允许重复（按用户需求）

        // 校验诊断是否失效
        List<String> invalidDiagnosis = diagnosisValidationService.validateAllDiagnosis(
                templateDto.getDiagnosis(), templateDto.getTcmDiagnosis());
        if (!invalidDiagnosis.isEmpty()) {
            throw new ServiceException("模板中包含失效的诊断：" + String.join("、", invalidDiagnosis));
        }

        BusDoctorCaseTemplate template = new BusDoctorCaseTemplate();
        BeanUtils.copyProperties(templateDto, template);
        
        // 设置基础信息
        template.setIsDelete(0);
        template.setCreateBy(SecurityUtils.getUsername());
        template.setCreateTime(DateUtils.getNowDate());
        
        return templateMapper.insert(template);
    }

    @Override
    public int updateTemplate(CaseTemplateDto templateDto) {
        if (templateDto.getId() == null) {
            throw new ServiceException("模板ID不能为空");
        }

        // 模板名称允许重复（按用户需求）

        // 校验诊断是否失效
        List<String> invalidDiagnosis = diagnosisValidationService.validateAllDiagnosis(
                templateDto.getDiagnosis(), templateDto.getTcmDiagnosis());
        if (!invalidDiagnosis.isEmpty()) {
            throw new ServiceException("模板中包含失效的诊断：" + String.join("、", invalidDiagnosis));
        }

        BusDoctorCaseTemplate template = new BusDoctorCaseTemplate();
        BeanUtils.copyProperties(templateDto, template);
        
        // 设置更新信息
        template.setUpdateBy(SecurityUtils.getUsername());
        template.setUpdateTime(new Date());
        
        return templateMapper.updateById(template);
    }

    @Override
    public int deleteTemplate(Long templateId, Long doctorId) {
        LambdaQueryWrapper<BusDoctorCaseTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusDoctorCaseTemplate::getId, templateId)
               .eq(BusDoctorCaseTemplate::getDoctorId, doctorId)
               .eq(BusDoctorCaseTemplate::getIsDelete, 0);
        
        BusDoctorCaseTemplate template = templateMapper.selectOne(wrapper);
        if (template == null) {
            throw new ServiceException("模板不存在或无权限删除");
        }
        
        // 逻辑删除
        template.setIsDelete(1);
        template.setUpdateBy(SecurityUtils.getUsername());
        template.setUpdateTime(new Date());
        
        return templateMapper.updateById(template);
    }

    @Override
    public Map<String, Object> applyTemplate(Long templateId, Long hospitalId) {
        // 获取模板详情
        CaseTemplateVo template = getTemplateById(templateId, hospitalId);
        
        // 已取消“使用次数”统计
        
        // 对齐电子病历做法：不做医院配置过滤，整体透传
        Map<String, Object> result = new HashMap<>();

        // 标准字段直接返回
        result.put("chiefComplaint", template.getChiefComplaint());
        result.put("historyOfPresentIllness", template.getHistoryOfPresentIllness());
        result.put("pastHistory", template.getPastHistory());
        result.put("personalHistory", template.getPersonalHistory());
        result.put("diagnosis", template.getDiagnosis());
        result.put("tcmDiagnosis", template.getTcmDiagnosis());
        result.put("management", template.getManagement());
        result.put("allergicDrugs", template.getAllergicDrugs());
        result.put("advice", template.getAdvice());

        return result;
    }

    /**
     * 获取医院的字段显示配置
     * @param hospitalId 医院ID
     * @return 字段显示配置Map
     */
    // 旧实现：根据医院配置获取字段可见性（已停用，保留以便回滚）
    // private Map<String, Boolean> getHospitalFieldConfig(Long hospitalId) {
    //     Map<String, Boolean> visibility = new HashMap<>();
    //     try {
    //         R<String> result = remoteHospitalSettingApi.getSettingValue(caseFieldVisibilityKey, hospitalId);
    //         if (result != null && result.isSuccess() && StringUtils.hasText(result.getData())) {
    //             Map<String, Object> raw = JSON.parseObject(result.getData(), new TypeReference<Map<String, Object>>(){});
    //             if (raw != null) {
    //                 for (Map.Entry<String, Object> e : raw.entrySet()) {
    //                     Object v = e.getValue();
    //                     boolean show;
    //                     if (v instanceof Boolean) {
    //                         show = (Boolean) v;
    //                     } else {
    //                         show = "1".equals(String.valueOf(v));
    //                     }
    //                     visibility.put(e.getKey(), show);
    //                 }
    //             }
    //         }
    //     } catch (Exception ex) {
    //         log.warn("读取医院字段显示配置失败，key：{}，hospitalId：{}", caseFieldVisibilityKey, hospitalId, ex);
    //     }
    //     return visibility;
    // }

    /**
     * 实体转换为VO
     * @param template 实体对象
     * @return VO对象
     */
    private CaseTemplateVo convertToVo(BusDoctorCaseTemplate template) {
        CaseTemplateVo vo = new CaseTemplateVo();
        BeanUtils.copyProperties(template, vo);

        // 自定义字段：字符串透传到 VO
        // 旧实现：解析为 List<Map<String,Object>>
//         if (StringUtils.hasText(template.getCustomFields())) {
//             try {
//                 List<Map<String, Object>> customFields = JSON.parseObject(
//                         template.getCustomFields(), new TypeReference<List<Map<String, Object>>>(){}
//                 );
//                 vo.setCustomFields(customFields);
//             } catch (Exception e) {
//                 log.warn("解析自定义字段失败，模板ID：{}", template.getId(), e);
//             }
//         }
        // 校验诊断是否失效
        List<String> invalidDiagnosis = diagnosisValidationService.validateAllDiagnosis(
                template.getDiagnosis(), template.getTcmDiagnosis());
        vo.setHasInvalidDiagnosis(!invalidDiagnosis.isEmpty());
        if (!invalidDiagnosis.isEmpty()) {
            vo.setInvalidDiagnosisList(String.join("、", invalidDiagnosis));
        }

        return vo;
    }
}
