package com.puree.hospital.app.service;

import com.puree.hospital.app.domain.BusDoctorCaseTemplate;
import com.puree.hospital.app.domain.dto.CaseTemplateDto;
import com.puree.hospital.app.domain.vo.CaseTemplateVo;

import java.util.List;
import java.util.Map;

/**
 * 医生病历模板服务接口
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface IBusDoctorCaseTemplateService {

    /**
     * 获取医生的模板列表
     * @param doctorId 医生ID
     * @param hospitalId 医院ID
     * @return 模板列表
     */
    List<CaseTemplateVo> getTemplateList(Long doctorId, Long hospitalId);

    // 热门模板与使用次数功能已取消
    // List<CaseTemplateVo> getHotTemplateList(Long doctorId, Long hospitalId);
    // int updateTemplatePopularity(Long templateId, Long doctorId);

    /**
     * 根据ID获取模板详情
     * @param templateId 模板ID
     * @param hospitalId 医院ID
     * @return 模板详情
     */
    CaseTemplateVo getTemplateById(Long templateId, Long hospitalId);

    /**
     * 新增模板
     * @param templateDto 模板数据
     * @return 操作结果
     */
    int insertTemplate(CaseTemplateDto templateDto);

    /**
     * 更新模板
     * @param templateDto 模板数据
     * @return 操作结果
     */
    int updateTemplate(CaseTemplateDto templateDto);

    // 不再统计使用次数
    // int updateTemplateUsageCount(Long templateId, Long doctorId);

    /**
     * 删除模板
     * @param templateId 模板ID
     * @param doctorId 医生ID
     * @return 操作结果
     */
    int deleteTemplate(Long templateId, Long doctorId);

    /**
     * 应用模板（内容透传，不做医院配置过滤）
     * @param templateId 模板ID
     * @param hospitalId 医院ID
     * @return 模板内容
     */
    Map<String, Object> applyTemplate(Long templateId, Long hospitalId);

}
