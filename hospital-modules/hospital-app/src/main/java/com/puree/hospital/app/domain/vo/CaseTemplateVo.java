package com.puree.hospital.app.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 病历模板VO
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
public class CaseTemplateVo {

    /** 模板ID */
    private Long id;

    /** 模板名称 */
    private String templateName;

    /** 医生ID */
    private Long doctorId;

    /** 医院ID */
    private Long hospitalId;

    /** 主诉 */
    private String chiefComplaint;

    /** 现病史 */
    private String historyOfPresentIllness;

    /** 既往史 */
    private String pastHistory;

    /** 个人史 */
    private String personalHistory;

    /** 西医诊断 */
    private String diagnosis;

    /** 中医诊断 */
    private String tcmDiagnosis;

    /** 处置 */
    private String management;

    /** 过敏史 */
    private String allergicDrugs;

    /** 医嘱 */
    private String advice;

    /** 模板描述 */
    private String templateDescription;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 是否有失效的诊断 */
    private Boolean hasInvalidDiagnosis;

    /** 失效的诊断列表 */
    private String invalidDiagnosisList;
}
