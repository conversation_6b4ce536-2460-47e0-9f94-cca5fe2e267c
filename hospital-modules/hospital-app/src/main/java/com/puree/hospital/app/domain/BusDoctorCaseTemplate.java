package com.puree.hospital.app.domain;

// import com.alibaba.fastjson.JSON; // 旧实现：Map 解析使用，现按 String 透传，保留注释
// import com.alibaba.fastjson.TypeReference; // 旧实现：Map 解析使用，现按 String 透传，保留注释
import com.baomidou.mybatisplus.annotation.TableName;
import com.puree.hospital.common.api.domain.entity.Entity;
import lombok.Data;

/**
 * 医生病历模板实体
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
@TableName("bus_doctor_case_template")
public class BusDoctorCaseTemplate extends Entity {

    /** 模板名称 */
    private String templateName;

    /** 医生ID */
    private Long doctorId;

    /** 医院ID */
    private Long hospitalId;

    /** 主诉 */
    private String chiefComplaint;

    /** 现病史 */
    private String historyOfPresentIllness;

    /** 既往史 */
    private String pastHistory;

    /** 个人史 */
    private String personalHistory;

    /** 西医诊断 */
    private String diagnosis;

    /** 中医诊断 */
    private String tcmDiagnosis;

    /** 处置 */
    private String management;

    /** 过敏史 */
    private String allergicDrugs;

    /** 医嘱 */
    private String advice;

    /** 模板描述 */
    private String templateDescription;

    /** 删除标记 */
    private Integer isDelete;

}
