package com.puree.hospital.app.service;

import java.util.List;

/**
 * 诊断校验服务接口
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface IDiagnosisValidationService {

    /**
     * 校验西医诊断是否有效
     * @param diagnosisJson 西医诊断JSON字符串
     * @return 失效的诊断列表
     */
    List<String> validateWesternDiagnosis(String diagnosisJson);

    /**
     * 校验中医诊断是否有效
     * @param tcmDiagnosisJson 中医诊断JSON字符串
     * @return 失效的诊断列表
     */
    List<String> validateTcmDiagnosis(String tcmDiagnosisJson);

    /**
     * 校验模板中的所有诊断
     * @param diagnosis 西医诊断JSON字符串
     * @param tcmDiagnosis 中医诊断JSON字符串
     * @return 失效的诊断描述列表
     */
    List<String> validateAllDiagnosis(String diagnosis, String tcmDiagnosis);
}
