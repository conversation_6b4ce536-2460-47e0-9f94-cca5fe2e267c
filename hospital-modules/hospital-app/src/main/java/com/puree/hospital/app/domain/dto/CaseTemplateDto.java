package com.puree.hospital.app.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 病历模板DTO
 * <AUTHOR>
 * @date 2025-08-08
 */
@Data
public class CaseTemplateDto {

    /** 模板ID（编辑时使用） */
    private Long id;

    /** 模板名称 */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /** 医生ID */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    /** 医院ID */
    @NotNull(message = "医院ID不能为空")
    private Long hospitalId;

    /** 主诉 */
    private String chiefComplaint;

    /** 现病史 */
    private String historyOfPresentIllness;

    /** 既往史 */
    private String pastHistory;

    /** 个人史 */
    private String personalHistory;

    /** 西医诊断 */
    private String diagnosis;

    /** 中医诊断 */
    private String tcmDiagnosis;

    /** 处置 */
    private String management;

    /** 过敏史 */
    private String allergicDrugs;

    /** 医嘱 */
    private String advice;

    /** 模板描述 */
    private String templateDescription;
}
