package com.puree.hospital.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.hospital.app.service.IDiagnosisValidationService;
import com.puree.hospital.business.api.RemoteBusDiseaseService;
import com.puree.hospital.business.api.RemoteBusTcmDiagnosisService;
import com.puree.hospital.business.domain.BusDisease;
import com.puree.hospital.business.domain.BusTcmDiagnosis;
import com.puree.hospital.common.api.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 诊断校验服务实现
 * <AUTHOR>
 * @date 2025-08-11
 */
@Slf4j
@Service
public class DiagnosisValidationServiceImpl implements IDiagnosisValidationService {

    @Autowired
    private RemoteBusDiseaseService remoteBusDiseaseService;

    @Autowired
    private RemoteBusTcmDiagnosisService remoteBusTcmDiagnosisService;

    @Override
    public List<String> validateWesternDiagnosis(String diagnosisJson) {
        List<String> invalidDiagnosis = new ArrayList<>();
        
        if (!StringUtils.hasText(diagnosisJson)) {
            return invalidDiagnosis;
        }

        try {
            JSONArray diagnosisArray = JSON.parseArray(diagnosisJson);
            if (diagnosisArray == null || diagnosisArray.isEmpty()) {
                return invalidDiagnosis;
            }

            for (int i = 0; i < diagnosisArray.size(); i++) {
                JSONObject diagnosisObj = diagnosisArray.getJSONObject(i);
                if (diagnosisObj == null) {
                    continue;
                }

                Long diseaseId = diagnosisObj.getLong("id");
                String diseaseName = diagnosisObj.getString("diseaseName");
                
                if (diseaseId != null) {
                    // 调用远程服务查询诊断状态
                    R<BusDisease> result = remoteBusDiseaseService.getInfo(diseaseId);
                    if (result != null && result.isSuccess() && result.getData() != null) {
                        BusDisease disease = result.getData();
                        // 状态为0表示停用
                        if (disease.getStatus() != null && disease.getStatus() == 0) {
                            invalidDiagnosis.add(diseaseName != null ? diseaseName : "未知诊断");
                        }
                    } else {
                        // 查询失败或数据不存在，也认为是失效
                        invalidDiagnosis.add(diseaseName != null ? diseaseName : "未知诊断");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析西医诊断JSON失败: {}", diagnosisJson, e);
        }

        return invalidDiagnosis;
    }

    @Override
    public List<String> validateTcmDiagnosis(String tcmDiagnosisJson) {
        List<String> invalidDiagnosis = new ArrayList<>();
        
        if (!StringUtils.hasText(tcmDiagnosisJson)) {
            return invalidDiagnosis;
        }

        try {
            JSONArray diagnosisArray = JSON.parseArray(tcmDiagnosisJson);
            if (diagnosisArray == null || diagnosisArray.isEmpty()) {
                return invalidDiagnosis;
            }

            for (int i = 0; i < diagnosisArray.size(); i++) {
                JSONObject diagnosisObj = diagnosisArray.getJSONObject(i);
                if (diagnosisObj == null) {
                    continue;
                }

                Long tcmDiagnosisId = diagnosisObj.getLong("id");
                String tcmDiagnosisName = diagnosisObj.getString("tcmDiagnosis");
                
                if (tcmDiagnosisId != null) {
                    // 调用远程服务查询中医诊断状态
                    R<BusTcmDiagnosis> result = remoteBusTcmDiagnosisService.getInfo(tcmDiagnosisId);
                    if (result != null && result.isSuccess() && result.getData() != null) {
                        BusTcmDiagnosis tcmDiagnosis = result.getData();
                        // 状态为0表示停用
                        if (tcmDiagnosis.getStatus() != null && tcmDiagnosis.getStatus() == 0) {
                            invalidDiagnosis.add(tcmDiagnosisName != null ? tcmDiagnosisName : "未知中医诊断");
                        }
                    } else {
                        // 查询失败或数据不存在，也认为是失效
                        invalidDiagnosis.add(tcmDiagnosisName != null ? tcmDiagnosisName : "未知中医诊断");
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析中医诊断JSON失败: {}", tcmDiagnosisJson, e);
        }

        return invalidDiagnosis;
    }

    @Override
    public List<String> validateAllDiagnosis(String diagnosis, String tcmDiagnosis) {
        List<String> allInvalidDiagnosis = new ArrayList<>();
        
        // 校验西医诊断
        List<String> invalidWesternDiagnosis = validateWesternDiagnosis(diagnosis);
        allInvalidDiagnosis.addAll(invalidWesternDiagnosis);
        
        // 校验中医诊断
        List<String> invalidTcmDiagnosis = validateTcmDiagnosis(tcmDiagnosis);
        allInvalidDiagnosis.addAll(invalidTcmDiagnosis);
        
        return allInvalidDiagnosis;
    }
}
