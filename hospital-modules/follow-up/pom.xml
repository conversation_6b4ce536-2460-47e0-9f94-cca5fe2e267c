<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hospital-modules</artifactId>
        <groupId>com.puree</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>follow-up</artifactId>

    <name>follow-up</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-follow-up-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-app-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-tool-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-business-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-im-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-five-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-order-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-system-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-tutorial-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-ehr-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-shop-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-feign</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-security</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-log</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-datasource</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-job</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-aliyun</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-logback</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.puree</groupId>
            <artifactId>hospital-common-notification</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--drools规则引擎-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>30.1-jre</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-core</artifactId>
            <version>7.6.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-compiler</artifactId>
            <version>7.6.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-templates</artifactId>
            <version>7.6.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.kie</groupId>
            <artifactId>kie-api</artifactId>
            <version>7.6.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.kie</groupId>
            <artifactId>kie-ci</artifactId>
            <version>7.6.0.Final</version>
        </dependency>
        
        <!-- Micrometer -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <version>1.7.3</version>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!-- 测试包 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <copy file="target/${project.artifactId}.jar" todir="../../deploy">
                                </copy>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
