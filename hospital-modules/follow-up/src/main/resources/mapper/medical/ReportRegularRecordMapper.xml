<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.medical.mapper.ReportRegularRecordMapper">
    <resultMap type="com.puree.followup.domain.medical.model.ReportRegularRecord" id="ReportRegularRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="itemType" column="item_type"/>
        <result property="itemValue" column="item_value" jdbcType="VARCHAR"/>
        <result property="itemUnit" column="item_unit" jdbcType="VARCHAR"/>
        <result property="source" column="source"/>
        <result property="recordId" column="record_id" jdbcType="BIGINT"/>
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="patientId" column="patient_id" jdbcType="BIGINT"/>
        <result property="patientIdNumber" column="patient_id_number" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="getById" resultMap="ReportRegularRecordMap">
        select id,
               item_type,
               item_value,
               item_unit,
               source,
               record_id,
               hospital_id,
               patient_id,
               patient_id_number,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               is_delete
        from medical_report_regular_record
        where id = #{id}
          and is_delete = 0
    </select>

    <!--批量查询数据-->
    <select id="getList" resultMap="ReportRegularRecordMap">
        select id,
               item_type,
               item_value,
               item_unit,
               source,
               record_id,
               hospital_id,
               patient_id,
               patient_id_number,
               revision,
               create_by,
               create_time,
               update_by,
               update_time,
               is_delete
        from medical_report_regular_record
        <where>
            is_delete = 0
            <if test="itemType != null">
                and item_type = #{itemType}
            </if>
            <if test="itemValue != null and itemValue != ''">
                and item_value = #{itemValue}
            </if>
            <if test="itemUnit != null and itemUnit != ''">
                and item_unit = #{itemUnit}
            </if>
            <if test="source != null ">
                and source = #{source}
            </if>
            <if test="recordId != null">
                and record_id = #{recordId}
            </if>
            <if test="hospitalId != null ">
                and hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null ">
                and patient_id = #{patientId}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="revision != null ">
                and revision = #{revision}
            </if>
        </where>
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from medical_report_regular_record
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="itemType != null and itemType != ''">
                and item_type = #{itemType}
            </if>
            <if test="itemValue != null and itemValue != ''">
                and item_value = #{itemValue}
            </if>
            <if test="itemUnit != null and itemUnit != ''">
                and item_unit = #{itemUnit}
            </if>
            <if test="source != null and source != ''">
                and source = #{source}
            </if>
            <if test="recordId != null and recordId != ''">
                and record_id = #{recordId}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null and patientId != ''">
                and patient_id = #{patientId}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="revision != null and revision != ''">
                and revision = #{revision}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and update_time = #{updateTime}
            </if>
            <if test="isDelete != null and isDelete != ''">
                and is_delete = #{isDelete}
            </if>
        </where>
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_regular_record( item_type, item_value, item_unit, source, record_id, hospital_id,
                                           patient_id, patient_id_number, revision, create_by, update_by, update_time, is_delete)
        values ( #{itemType}, #{itemValue}, #{itemUnit}, #{source}, #{recordId}, #{hospitalId}, #{patientId},
                #{patientIdNumber}, #{revision}, #{createBy}, #{updateBy}, #{updateTime}, #{isDelete})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_regular_record(item_type, item_value, item_unit, source, record_id, hospital_id,
                                           patient_id, patient_id_number, revision, create_by,create_time, update_by, update_time, is_delete)
        values
        <foreach collection="entities" item="entity" separator=",">
            ( #{entity.itemType}, #{entity.itemValue}, #{entity.itemUnit}, #{entity.source},
             #{entity.recordId}, #{entity.hospitalId}, #{entity.patientId}, #{entity.patientIdNumber},
             #{entity.revision}, #{entity.createBy},#{entity.createTime}, #{entity.updateBy},#{entity.updateTime},
             #{entity.isDelete})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_regular_record(id, item_type, item_value, item_unit, source, record_id, hospital_id,
                                           patient_id, patient_id_number, revision, create_by,create_time, update_by, update_time, is_delete)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.itemType}, #{entity.itemValue}, #{entity.itemUnit}, #{entity.source},
             #{entity.recordId}, #{entity.hospitalId}, #{entity.patientId}, #{entity.patientIdNumber},
             #{entity.revision}, #{entity.createBy},#{entity.createTime}, #{entity.updateBy},#{entity.updateTime},
             #{entity.isDelete})
        </foreach>
        on duplicate key update
                             id=
                         values (id), item_type=
                         values (item_type), item_value=
                         values (item_value), item_unit=
                         values (item_unit), source =
                         values (source), record_id=
                         values (record_id), hospital_id=
                         values (hospital_id), patient_id=
                         values (patient_id), patient_id_number=
                         values (patient_id_number), revision=
                         values (revision), create_by=
                         values (create_by), update_by=
                         values (update_by), is_delete=
                         values (is_delete)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update medical_report_regular_record
        <set>
            <if test="itemType != null ">
                item_type = #{itemType},
            </if>
                item_value = #{itemValue},
            <if test="itemUnit != null and itemUnit != ''">
                item_unit = #{itemUnit},
            </if>
            <if test="source != null ">
                source = #{source},
            </if>
            <if test="recordId != null ">
                record_id = #{recordId},
            </if>
            <if test="hospitalId != null ">
                hospital_id = #{hospitalId},
            </if>
            <if test="patientId != null ">
                patient_id = #{patientId},
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                patient_id_number = #{patientIdNumber},
            </if>
            <if test="revision != null and revision != ''">
                revision = #{revision},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null ">
                update_time = #{updateTime},
            </if>
            <if test="isDelete != null and isDelete != ''">
                is_delete = #{isDelete},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="delete">
        update medical_report_regular_record set is_delete = 1 where id = #{id}
    </update>

    <select id="getGroupList" resultMap="ReportRegularRecordMap">
        select * from medical_report_regular_record
        <where>
             is_delete = 0
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="codes != null and codes.size > 0">
                and item_type in
                <foreach collection="codes" item="item" separator="," open="(" close=")">
                    #{item.code}
                </foreach>
            </if>
            <if test="createTimeList != null and createTimeList.size > 0">
                and create_time in
                <foreach collection="createTimeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by create_time desc ,update_time desc, id asc
    </select>

    <select id="getLineChart" resultType="com.puree.followup.domain.medical.vo.ReportRegularRecordLineChartVO">
        select *
        from medical_report_regular_record
        <where>
            is_delete = 0
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="startTime != null and endTime != null">
                and create_time between #{startTime} and #{endTime}
            </if>
            <if test="regularRecordEnum != null">
                and item_type = #{regularRecordEnum}
            </if>
        </where>
        order by create_time asc
    </select>

    <select id="getListByIdsCreateBy" resultMap="ReportRegularRecordMap">
        select * from medical_report_regular_record
        <where>
            is_delete = 0
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="entities != null ">
                and id in
                <foreach collection="entities" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="recordSourceEnum != null">
                and source = #{recordSourceEnum}
            </if>
        </where>
        order by create_time desc
    </select>

    <delete id="deleteByRecordId">
        update medical_report_regular_record set is_delete = 1 where record_id = #{recordId}
    </delete>

    <select id="getListByIds" resultMap="ReportRegularRecordMap">
        select * from medical_report_regular_record
        <where>
            is_delete = 0
            <if test="ids != null ">
                and id in
                <foreach collection="ids" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="groupByCreateTime" resultMap="ReportRegularRecordMap">
        select min(id) as id, create_time  from medical_report_regular_record
        <where>
            is_delete = 0
            and patient_id_number = #{patientIdNumber}
            and item_type = #{code}
        </where>
        group by create_time order by create_time desc
    </select>

</mapper>