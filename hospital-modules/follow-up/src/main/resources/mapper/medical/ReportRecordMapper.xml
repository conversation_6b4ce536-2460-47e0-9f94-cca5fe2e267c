<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.medical.mapper.ReportRecordMapper">
    <resultMap type="com.puree.followup.domain.medical.model.ReportRecord" id="ReportRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="reportJson" column="report_json" />
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="recordType" column="record_type" />
        <result property="deviceId" column="device_id" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="BIGINT"/>
        <result property="examId" column="exam_id" jdbcType="VARCHAR"/>
        <result property="examDate" column="exam_date" jdbcType="TIMESTAMP"/>
        <result property="isHandle" column="is_handle" />
        <result property="reportId" column="report_id" jdbcType="VARCHAR"/>
        <result property="reportVer" column="report_ver" jdbcType="INTEGER"/>
        <result property="reportDate" column="report_date" jdbcType="TIMESTAMP"/>
        <result property="patientIdNumber" column="patient_id_number" jdbcType="VARCHAR"/>
        <result property="patientId" column="patient_id" jdbcType="BIGINT"/>
        <result property="familyId" column="family_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="getById" resultMap="ReportRecordMap">
        select
        id,report_json,hospital_id,record_type,device_id,org_id,exam_id,exam_date,is_handle,report_id,report_ver,report_date,patient_id_number,patient_id,family_id,create_time
        from medical_report_record
        where id = #{id}
    </select>

    <!--批量获取数据-->
    <select id="getList" resultMap="ReportRecordMap">
        select id,
               report_json,
               hospital_id,
               record_type,
               device_id,
               org_id,
               exam_id,
               exam_date,
               is_handle,
               report_id,
               report_ver,
               report_date,
               patient_id_number,
               patient_id,
               family_id,
               create_time
        from medical_report_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="hospitalId != null">
                and hospital_id = #{hospitalId}
            </if>
            <if test="recordType != null">
                and record_type = #{recordType}
            </if>
            <if test="deviceId != null and deviceId != ''">
                and device_id = #{deviceId}
            </if>
            <if test="orgId != null">
                and org_id = #{orgId}
            </if>
            <if test="examId != null and examId != ''">
                and exam_id = #{examId}
            </if>
            <if test="examDate != null">
                and DATE(exam_date) = DATE(#{examDate})
            </if>
            <if test="isHandle != null">
                and is_handle = #{isHandle}
            </if>
            <if test="reportId != null">
                and report_id = #{reportId}
            </if>
            <if test="reportVer != null">
                and report_ver = #{reportVer}
            </if>
            <if test="reportDate != null">
                and DATE(report_date) = DATE(#{reportDate})
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="patientId != null ">
                and patient_id = #{patientId}
            </if>
            <if test="familyId != null ">
                and family_id = #{familyId}
            </if>
        </where>
        order by create_time desc
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from medical_report_record
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="reportJson != null and reportJson != ''">
                and report_json = #{reportJson}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
            <if test="recordType != null and recordType != ''">
                and record_type = #{recordType}
            </if>
            <if test="deviceId != null and deviceId != ''">
                and device_id = #{deviceId}
            </if>
            <if test="orgId != null and orgId != ''">
                and org_id = #{orgId}
            </if>
            <if test="examId != null and examId != ''">
                and exam_id = #{examId}
            </if>
            <if test="examDate != null and examDate != ''">
                and exam_date = #{examDate}
            </if>
            <if test="isHandle != null and isHandle != ''">
                and is_handle = #{isHandle}
            </if>
            <if test="reportId != null and reportId != ''">
                and report_id = #{reportId}
            </if>
            <if test="reportVer != null and reportVer != ''">
                and report_ver = #{reportVer}
            </if>
            <if test="reportDate != null and reportDate != ''">
                and report_date = #{reportDate}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="patientId != null ">
                and patient_id = #{patientId}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_record(report_json,hospital_id,record_type,device_id,org_id,exam_id,exam_date,is_handle,report_id,report_ver,report_date,patient_id_number,patient_id,family_id)
        values (#{reportJson},#{hospitalId},#{recordType},#{deviceId},#{orgId},#{examId},#{examDate},#{isHandle},#{reportId},#{reportVer},#{reportDate},#{patientIdNumber},#{patientId},#{familyId})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_record(id,report_json,hospital_id,record_type,device_id,org_id,exam_id,exam_date,is_handle,report_id,report_ver,report_date,patient_id_number,patient_id,family_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportJson},#{entity.hospitalId},#{entity.recordType},#{entity.deviceId},#{entity.orgId},#{entity.examId},#{entity.examDate},#{entity.isHandle},#{entity.reportId},#{entity.reportVer},#{entity.reportDate},#{entity.patientIdNumber},#{entity.patientId},#{entity.familyId})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_record(id,report_json,hospital_id,record_type,device_id,org_id,exam_id,exam_date,is_handle,report_id,report_ver,report_date,patient_id_number,patient_id,family_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.reportJson},#{entity.hospitalId},#{entity.recordType},#{entity.deviceId},#{entity.orgId},#{entity.examId},#{entity.examDate},#{entity.isHandle},#{entity.reportId},#{entity.reportVer},#{entity.reportDate},#{entity.patientIdNumber},#{entity.patientId},#{entity.familyId})
        </foreach>
        on duplicate key update
        id=values(id),
        report_json=values(report_json),
        hospital_id=values(hospital_id),
        record_type=values(record_type),
        device_id=values(device_id),
        org_id=values(org_id),
        exam_id=values(exam_id),
        exam_date=values(exam_date),
        is_handle=values(is_handle),
        report_id=values(report_id),
        report_ver=values(report_ver),
        report_date=values(report_date),
        patient_id_number=values(patient_id_number),
        patient_id=values(patient_id),
        family_id=values(family_id)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update medical_report_record
        <set>
            <if test="reportJson != null and reportJson != ''">
                report_json = #{reportJson},
            </if>
            <if test="hospitalId != null">
                hospital_id = #{hospitalId},
            </if>
            <if test="recordType != null">
                record_type = #{recordType},
            </if>
            <if test="deviceId != null and deviceId != ''">
                device_id = #{deviceId},
            </if>
            <if test="orgId != null">
                org_id = #{orgId},
            </if>
            <if test="examId != null and examId != ''">
                exam_id = #{examId},
            </if>
            <if test="examDate != null">
                exam_date = #{examDate},
            </if>
            <if test="isHandle != null">
                is_handle = #{isHandle},
            </if>
            <if test="reportId != null">
                report_id = #{reportId},
            </if>
            <if test="reportVer != null">
                report_ver = #{reportVer},
            </if>
            <if test="reportDate != null">
                report_date = #{reportDate},
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                patient_id_number = #{patientIdNumber},
            </if>
            <if test="patientId != null">
                patient_id = #{patientId},
            </if>
            <if test="familyId != null">
                family_id = #{familyId},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from medical_report_record where id = #{id}
    </delete>


    <!--体检报告+小结-->
    <select id="getReportAndSummaryResult" resultType="com.puree.hospital.business.api.model.BusPatientFamilyVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">

        SELECT distinct hospital_id as hospitalId, patient_id as patientId, family_id as id
        FROM medical_report_record
        <where>
            hospital_id = #{hospitalId}
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and (
                     (
                        SELECT count(1)
                        FROM JSON_TABLE(
                            report_json,
                            "$.inspectSummaries[*]"
                            COLUMNS (
                                summary VARCHAR(1000) PATH "$.summary"
                            )
                        ) AS jt
                        WHERE jt.summary  ${inspectSql}
                    ) > 0
                    OR
                     (
                        SELECT count(1)
                        FROM JSON_TABLE(
                            report_json,
                            "$.examSummaries[*]"
                            COLUMNS (
                                summary VARCHAR(1000) PATH "$.summary"
                            )
                        ) AS jt
                        WHERE jt.summary ${examSql}
                    ) > 0
                )
            </if>
        </where>
    </select>


    <!--体检报告+总检建议-->
    <select id="getReportAndSuggestResult" resultType="com.puree.hospital.business.api.model.BusPatientFamilyVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">

        SELECT distinct hospital_id as hospitalId, patient_id as patientId, family_id as id
        FROM medical_report_record
        <where>
            hospital_id = #{hospitalId}
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="suggestSql != null and suggestSql != ''">
                and (
                    SELECT count(1)
                    FROM JSON_TABLE(
                        report_json,
                        '$.conclusions.suggest[*]'
                        COLUMNS (
                            suggest VARCHAR(1000) PATH '$'
                        )
                    ) AS jt
                    WHERE jt.suggest ${suggestSql}
                ) > 0
            </if>
        </where>
    </select>

    <!--体检报告+二级指标-->
    <select id="getReportAndItemResult" resultType="com.puree.hospital.business.api.model.BusPatientFamilyVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">
        SELECT distinct hospital_id as hospitalId, patient_id as patientId, family_id as id
        FROM medical_report_record
        <where>
            hospital_id = #{hospitalId}
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and (
                 (
                    SELECT count(1)
                    FROM JSON_TABLE(report_json, '$.inspectSummaries[*]' COLUMNS (
                      subject VARCHAR(1000) PATH '$.subject',
                      items JSON PATH '$.items'
                    )) AS inspect,
                    JSON_TABLE(inspect.items, '$[*]' COLUMNS (
                      name VARCHAR(1000) PATH '$.name',
                      result VARCHAR(1000) PATH '$.resultStr'
                    )) AS jt
                    WHERE jt.name = #{secondLevelName} AND ( jt.result ${inspectSql} )
                  ) > 0
                  OR
                   (
                    SELECT count(1)
                    FROM JSON_TABLE(report_json, '$.examSummaries[*]' COLUMNS (
                      subject VARCHAR(1000) PATH '$.subject',
                      items JSON PATH '$.items'
                    )) AS exam,
                    JSON_TABLE(exam.items, '$[*]' COLUMNS (
                      name VARCHAR(1000) PATH '$.name',
                      `desc` VARCHAR(1000) PATH '$.desc'
                    )) AS jt
                    WHERE jt.name = #{secondLevelName} AND ( jt.`desc` ${examSql} )
                  ) > 0
                )
            </if>
        </where>
    </select>

    <!--一级指标+小结-->
    <select id="getSummaryResult" resultType="com.puree.hospital.business.api.model.BusPatientFamilyVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">
        SELECT distinct hospital_id as hospitalId, patient_id as patientId, family_id as id
        FROM medical_report_record
        <where>
            hospital_id = #{hospitalId}
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and (
                   (
                    SELECT count(1)
                    FROM JSON_TABLE(report_json, '$.inspectSummaries[*]' COLUMNS (
                      subject VARCHAR(1000) PATH '$.subject',
                      summary VARCHAR(1000) PATH '$.summary'
                    )) AS jt
                    WHERE jt.subject = #{firstLevelName} AND ( jt.summary ${inspectSql} )
                  ) > 0
                  OR
                   (
                    SELECT count(1)
                    FROM JSON_TABLE(report_json, '$.examSummaries[*]' COLUMNS (
                      subject VARCHAR(1000) PATH '$.subject',
                      summary VARCHAR(1000) PATH '$.summary'
                    )) AS jt
                    WHERE jt.subject = #{firstLevelName} AND ( jt.summary ${examSql} )
                  ) > 0
                )
            </if>
        </where>
    </select>

    <!--一级指标+二级指标-->
    <select id="getItemResult" resultType="com.puree.hospital.business.api.model.BusPatientFamilyVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">
        SELECT distinct hospital_id as hospitalId, patient_id as patientId, family_id as id
        FROM medical_report_record
        <where>
            hospital_id = #{hospitalId}
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and (
                   (
                    SELECT count(1)
                    FROM JSON_TABLE(report_json, '$.inspectSummaries[*]' COLUMNS (
                                subject VARCHAR(1000) PATH '$.subject',
                                items JSON PATH '$.items'
                             )
                        ) AS inspect,
                    JSON_TABLE(inspect.items, '$[*]' COLUMNS (
                            name VARCHAR(1000) PATH '$.name',
                            result VARCHAR(1000) PATH '$.resultStr'
                        )
                    ) AS jt
                    WHERE inspect.subject = #{firstLevelName}
                      AND jt.name = #{secondLevelName}
                      AND ( jt.result ${inspectSql} )
                  ) > 0
                  OR
                   (
                    SELECT count(1)
                    FROM JSON_TABLE(report_json, '$.examSummaries[*]' COLUMNS (
                                subject VARCHAR(1000) PATH '$.subject',
                                items JSON PATH '$.items'
                            )
                        ) AS exam,
                    JSON_TABLE(exam.items, '$[*]' COLUMNS (
                            name VARCHAR(1000) PATH '$.name',
                            `desc` VARCHAR(1000) PATH '$.desc'
                        )
                    ) AS jt
                    WHERE exam.subject = #{firstLevelName}
                      AND jt.name = #{secondLevelName}
                      AND ( jt.`desc` ${examSql} )
                  ) > 0
                )
            </if>
        </where>
    </select>

    <select id="getListByIdNumber" resultMap="ReportRecordMap">
        SELECT *
        FROM medical_report_record
        WHERE patient_id_number = #{idNumber}
        ORDER BY create_time DESC
        LIMIT 2
    </select>


    <!--智能执行；体检报告+小结-->
    <select id="getReportAndSummaryResultWithSmartExecutor" resultType="com.puree.followup.domain.followup.vo.SmartExecutorPatientVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">

      SELECT t1.hospital_id as hospitalId, t1.patient_id as patientId, t1.family_id as id,
            IF(jt.summary ${inspectSql}, jt.summary, NULL) AS firstValue,
            IF(jt2.summary ${examSql}, jt2.summary, NULL) AS secondValue
      FROM medical_report_record t1,
            JSON_TABLE(
                report_json,
                "$.inspectSummaries[*]"
                COLUMNS (
                    summary VARCHAR(1000) PATH "$.summary"
                )
            ) AS jt,
            JSON_TABLE(
                report_json,
                "$.examSummaries[*]"
                COLUMNS (
                    summary VARCHAR(1000) PATH "$.summary"
                )
            ) AS jt2
        <where>
            t1.hospital_id = #{hospitalId}
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and t1.family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and t1.report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and t1.report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and t1.family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and (jt.summary ${inspectSql} or jt2.summary ${examSql})
            </if>
        </where>
        order by t1.report_date desc
    </select>


    <!--智能执行；体检报告+总检建议-->
    <select id="getReportAndSuggestResultWithSmartExecutor" resultType="com.puree.followup.domain.followup.vo.SmartExecutorPatientVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">
        SELECT t1.hospital_id as hospitalId, t1.patient_id as patientId, t1.family_id as id,
            IF(jt.suggest ${suggestSql}, jt.suggest, NULL) AS firstValue
        FROM medical_report_record t1,
            JSON_TABLE(
                report_json,
                '$.conclusions.suggest[*]'
                COLUMNS (
                    suggest VARCHAR(1000) PATH '$'
                )
            ) AS jt
        <where>
            t1.hospital_id = #{hospitalId}
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and t1.family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and t1.report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and t1.report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and t1.family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="suggestSql != null and suggestSql != ''">
                and jt.suggest ${suggestSql}
            </if>
        </where>
        order by t1.report_date desc
    </select>

    <!-- 智能执行；体检报告+二级指标 -->
    <select id="getReportAndItemResultWithSmartExecutor" resultType="com.puree.followup.domain.followup.vo.SmartExecutorPatientVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">
        SELECT hospital_id as hospitalId, patient_id as patientId, family_id as id,
            IF(jt1.name = #{secondLevelName} AND ( jt1.result ${inspectSql} ) , jt1.result, NULL) AS firstValue,
            IF(jt2.name = #{secondLevelName} AND ( jt2.`desc` ${examSql} ) , jt2.`desc`, NULL) AS secondValue
        FROM medical_report_record t1,
            JSON_TABLE(report_json, '$.inspectSummaries[*]' COLUMNS (
              subject VARCHAR(1000) PATH '$.subject',
              items JSON PATH '$.items'
            )) AS inspect,
            JSON_TABLE(inspect.items, '$[*]' COLUMNS (
              name VARCHAR(1000) PATH '$.name',
              result VARCHAR(1000) PATH '$.resultStr'
            )) AS jt1,
            JSON_TABLE(report_json, '$.examSummaries[*]' COLUMNS (
              subject VARCHAR(1000) PATH '$.subject',
              items JSON PATH '$.items'
            )) AS exam,
            JSON_TABLE(exam.items, '$[*]' COLUMNS (
              name VARCHAR(1000) PATH '$.name',
              `desc` VARCHAR(1000) PATH '$.desc'
            )) AS jt2
        <where>
            t1.hospital_id = #{hospitalId}
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and t1.family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and t1.report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and t1.report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and t1.family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and (( jt1.name = #{secondLevelName} AND ( jt1.result ${inspectSql} ) ) or (jt2.name = #{secondLevelName} AND ( jt2.`desc` ${examSql} )))
            </if>
        </where>
        order by t1.report_date desc
    </select>

     <!--智能执行；一级指标+小结-->
    <select id="getSummaryResultWithSmartExecutor" resultType="com.puree.followup.domain.followup.vo.SmartExecutorPatientVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">
        SELECT hospital_id as hospitalId, patient_id as patientId, family_id as id,
            IF(jt.subject = #{firstLevelName} AND ( jt.summary ${inspectSql} ), jt.summary, NULL) AS firstValue,
            IF(jt2.subject = #{firstLevelName} AND ( jt2.summary ${examSql} ), jt2.summary, NULL) AS secondValue
        FROM medical_report_record t1,
            JSON_TABLE(report_json, '$.inspectSummaries[*]' COLUMNS (
              subject VARCHAR(1000) PATH '$.subject',
              summary VARCHAR(1000) PATH '$.summary'
            )) AS jt,
            JSON_TABLE(report_json, '$.examSummaries[*]' COLUMNS (
              subject VARCHAR(1000) PATH '$.subject',
              summary VARCHAR(1000) PATH '$.summary'
            )) AS jt2

        <where>
            t1.hospital_id = #{hospitalId}
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and t1.family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and t1.report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and t1.report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and t1.family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and ( (jt.subject = #{firstLevelName} AND ( jt.summary ${inspectSql} )) or (jt2.subject = #{firstLevelName} AND ( jt2.summary ${examSql} )) )
            </if>
        </where>
        order by t1.report_date desc
    </select>

    <!--智能执行；一级指标+二级指标-->
    <select id="getItemResultWithSmartExecutor" resultType="com.puree.followup.domain.followup.vo.SmartExecutorPatientVo" parameterType="com.puree.followup.domain.medical.query.ReportRecordJoinInQuery">
        SELECT hospital_id as hospitalId, patient_id as patientId, family_id as id,
            IF(inspect.subject = #{firstLevelName} AND jt1.name = #{secondLevelName} AND ( jt1.result ${inspectSql} ) , jt1.result, NULL) AS firstValue,
            IF(exam.subject = #{firstLevelName} AND jt2.name = #{secondLevelName} AND ( jt2.`desc` ${examSql} ) , jt2.`desc`, NULL) AS secondValue
        FROM medical_report_record t1,
            JSON_TABLE(report_json, '$.inspectSummaries[*]' COLUMNS (
                    subject VARCHAR(1000) PATH '$.subject',
                    items JSON PATH '$.items'
                 )
            ) AS inspect,
            JSON_TABLE(inspect.items, '$[*]' COLUMNS (
                    name VARCHAR(1000) PATH '$.name',
                    result VARCHAR(1000) PATH '$.resultStr'
                )
            ) AS jt1,
            JSON_TABLE(report_json, '$.examSummaries[*]' COLUMNS (
                    subject VARCHAR(1000) PATH '$.subject',
                    items JSON PATH '$.items'
                )
            ) AS exam,
            JSON_TABLE(exam.items, '$[*]' COLUMNS (
                    name VARCHAR(1000) PATH '$.name',
                    `desc` VARCHAR(1000) PATH '$.desc'
                )
            ) AS jt2

        <where>
            t1.hospital_id = #{hospitalId}
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="familyId != null">
                and t1.family_id = #{familyId}
            </if>
            <if test="beginDate != null">
                and t1.report_date <![CDATA[>=]]> #{beginDate}
            </if>
            <if test="endDate != null">
                and t1.report_date <![CDATA[<=]]> #{endDate}
            </if>
            <if test="excludePatientIds != null and excludePatientIds.size() > 0  ">
                and t1.family_id NOT IN
                <foreach collection="excludePatientIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="inspectSql != null and inspectSql != '' and  examSql != null and examSql != ''">
                and (
                        (inspect.subject = #{firstLevelName} AND jt1.name = #{secondLevelName} AND ( jt1.result ${inspectSql} ))
                    or
                        (exam.subject = #{firstLevelName} AND jt2.name = #{secondLevelName} AND ( jt2.`desc` ${examSql} ))
                    )
            </if>
        </where>
        order by t1.report_date desc
    </select>

</mapper>