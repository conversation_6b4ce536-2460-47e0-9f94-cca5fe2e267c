<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.medical.mapper.ReportFileRecordMapper">
    <resultMap type="com.puree.followup.domain.medical.model.ReportFileRecord" id="ReportFileRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="source" column="source" />
        <result property="operationType" column="operation_type" />
        <result property="reportFile" column="report_file" jdbcType="VARCHAR"/>
        <result property="reportFileName" column="report_file_name" jdbcType="VARCHAR"/>
        <result property="recordId" column="record_id" jdbcType="BIGINT"/>
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="dataSourceId" column="data_source_id" jdbcType="BIGINT"/>
        <result property="dataSource" column="data_source" />
        <result property="patientId" column="patient_id" jdbcType="BIGINT"/>
        <result property="patientIdNumber" column="patient_id_number" jdbcType="VARCHAR"/>
        <result property="checkTime" column="check_time" jdbcType="TIMESTAMP"/>
        <result property="orgId" column="org_id" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="getById" resultMap="ReportFileRecordMap">
        select
        id,source,operation_type,report_file,data_source_id,data_source,org_id,org_name,report_file_name,record_id,hospital_id,patient_id,patient_id_number,create_time,check_time
        from medical_report_file_record
        where id = #{id}
    </select>

    <!--分页查询指定行数据-->
    <select id="getList" resultMap="ReportFileRecordMap">
        select
        id,source,operation_type,report_file,data_source_id,data_source,report_file_name,org_id,org_name,record_id,hospital_id,patient_id,patient_id_number,create_time,check_time
        from medical_report_file_record
        <where>
            <if test="id != null ">
                and id = #{id}
            </if>
            <if test="source != null ">
                and source = #{source}
            </if>
            <if test="operationType != null ">
                and operation_type = #{operationType}
            </if>
            <if test="recordId != null ">
                and record_id = #{recordId}
            </if>
            <if test="orgId != null ">
                and org_id = #{orgId}
            </if>
            <if test="orgName != null ">
                and org_name = #{orgName}
            </if>
            <if test="hospitalId != null ">
                and hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null">
                and patient_id = #{patientId}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
        </where>
        ORDER BY
        DATE(check_time) DESC,create_time DESC
    </select>


    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_file_record(source,operation_type,data_source_id,data_source,report_file,report_file_name,org_id,org_name,record_id,hospital_id,patient_id,patient_id_number,check_time)
        values (#{source},#{operationType},#{dataSourceId},#{dataSource},#{reportFile},#{reportFileName},#{orgId},#{orgName},#{recordId},#{hospitalId},#{patientId},#{patientIdNumber},#{checkTime})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_file_record(source,operation_type,data_source_id,data_source,report_file,report_file_name,org_id,org_name,record_id,hospital_id,patient_id,patient_id_number,check_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.source},#{entity.operationType},#{entity.dataSourceId},#{entity.dataSource},#{entity.reportFile},#{entity.reportFileName},#{entity.orgId},#{entity.orgName},#{entity.recordId},#{entity.hospitalId},#{entity.patientId},#{entity.patientIdNumber},#{entity.checkTime})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_file_record(id,source,operation_type,report_file,report_file_name,org_id,org_name,record_id,hospital_id,patient_id,patient_id_number,check_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.source},#{entity.operationType},#{entity.reportFile},#{entity.reportFileName},#{entity.orgId},#{entity.orgName},#{entity.recordId},#{entity.hospitalId},#{entity.patientId},#{entity.patientIdNumber},#{entity.checkTime})
        </foreach>
        on duplicate key update
        id=values(id),
        source=values(source),
        operation_type=values(operation_type),
        report_file=values(report_file),
        report_file_name=values(report_file_name),
        org_id=values(org_id),
        org_name=values(org_name),
        record_id=values(record_id),
        hospital_id=values(hospital_id),
        patient_id=values(patient_id),
        patient_id_number=values(patient_id_number)
        check_time=values(check_time)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update medical_report_file_record
        <set>
            <if test="source != null ">
                source = #{source},
            </if>
            <if test="operationType != null ">
                operation_type = #{operationType},
            </if>
            <if test="reportFile != null and reportFile != ''">
                report_file = #{reportFile},
            </if>
            <if test="reportFileName != null and reportFileName != ''">
                report_file_name = #{reportFileName},
            </if>
            <if test="recordId != null and recordId != ''">
                record_id = #{recordId},
            </if>
            <if test="orgId != null ">
                 org_id = #{orgId},
            </if>
            <if test="dataSourceId != null ">
                data_source_id = #{dataSourceId},
            </if>
            <if test="orgName != null ">
                 org_name = #{orgName},
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                hospital_id = #{hospitalId},
            </if>
            <if test="patientId != null and patientId != ''">
                patient_id = #{patientId},
            </if>
            <if test="checkTime != null ">
                check_time = #{checkTime},
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                patient_id_number = #{patientIdNumber},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from medical_report_file_record
        where id = #{id}
    </delete>

    <select id="getRepeatToday" resultMap="ReportFileRecordMap">
        select * from medical_report_file_record
        <where>
            <if test="time != null ">
                and TO_DAYS(check_time)=TO_DAYS(#{time})
            </if>
            <if test="name != null  and name != ''">
                and org_name = #{name}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
        </where>
    </select>

    <select id="getByRecordId" resultMap="ReportFileRecordMap">
        select * from medical_report_file_record
        where
            record_id = #{recordId}
    </select>
</mapper>