<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.medical.mapper.ReportDataSummaryMapper">
    <resultMap type="com.puree.followup.domain.medical.model.ReportDataSummary" id="ReportDataSummaryMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="itemName" column="item_name" jdbcType="VARCHAR"/>
        <result property="jsonType" column="json_type" />
        <result property="json" column="json" />
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="patientId" column="patient_id" jdbcType="BIGINT"/>
        <result property="patientIdNumber" column="patient_id_number" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="getById" resultMap="ReportDataSummaryMap">
        select
        id,item_name,json_type,json,hospital_id,patient_id,patient_id_number,create_time
        from medical_report_data_summary
        where id = #{id}
    </select>

    <!--批量获取数据-->
    <select id="getList" resultMap="ReportDataSummaryMap">
        select
        id,item_name,json_type,json,hospital_id,patient_id,patient_id_number,create_time
        from medical_report_data_summary
        <where>
            <if test="itemName != null and itemName != ''">
                and item_name = #{itemName}
            </if>
            <if test="jsonType != null ">
                and json_type = #{jsonType}
            </if>
            <if test="hospitalId != null">
                and hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null ">
                and patient_id = #{patientId}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_data_summary(item_name,json_type,json,hospital_id,patient_id,patient_id_number)
        values (#{itemName},#{jsonType},#{json},#{hospitalId},#{patientId},#{patientIdNumber})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_data_summary(item_name,json_type,json,hospital_id,patient_id,patient_id_number)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.itemName},#{entity.jsonType},#{entity.json},#{entity.hospitalId},#{entity.patientId},#{entity.patientIdNumber})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_data_summary(id,item_name,json_type,json,hospital_id,patient_id,patient_id_number)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.itemName},#{entity.jsonType},#{entity.json},#{entity.hospitalId},#{entity.patientId},#{entity.patientIdNumber})
        </foreach>
        on duplicate key update
        id=values(id),
        item_name=values(item_name),
        json_type=values(json_type),
        json=values(json),
        hospital_id=values(hospital_id),
        patient_id=values(patient_id),
        patient_id_number=values(patient_id_number)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update medical_report_data_summary
        <set>
            <if test="itemName != null and itemName != ''">
                item_name = #{itemName},
            </if>
            <if test="jsonType != null and jsonType != ''">
                json_type = #{jsonType},
            </if>
            <if test="json != null and json != ''">
                json = #{json},
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                hospital_id = #{hospitalId},
            </if>
            <if test="patientId != null and patientId != ''">
                patient_id = #{patientId},
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                patient_id_number = #{patientIdNumber},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="questionnaireDataSummary" resultMap="ReportDataSummaryMap">
        select
        JSON_EXTRACT(`json`,
        <foreach collection="itemNames" item="item" separator=",">
            #{item}
        </foreach>
        ) json,json_type
        from
        medical_report_data_summary
        WHERE
        JSON_EXTRACT(`json`,
        <foreach collection="itemNames" item="item" separator=",">
            #{item}
        </foreach>
        ) is not null
        and patient_id_number = #{patientIdNumber} and hospital_id = #{hospitalId}
    </select>
<!--    <select id="questionnaireDataSummary" resultMap="ReportDataSummaryMap">-->
<!--        select-->
<!--            *-->
<!--        from-->
<!--            medical_report_data_summary-->
<!--        WHERE-->
<!--&lt;!&ndash;            JSON_EXTRACT(`json`,'<![CDATA[$]]>.&quot;心电图&quot;') is not null&ndash;&gt;-->
<!--&lt;!&ndash;          and &ndash;&gt;-->
<!--        patient_id_number = '410522198606107258' and hospital_id = 2-->
<!--    </select>-->
</mapper>