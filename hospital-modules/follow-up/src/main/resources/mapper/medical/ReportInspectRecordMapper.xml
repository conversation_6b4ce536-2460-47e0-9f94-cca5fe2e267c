<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.medical.mapper.ReportInspectRecordMapper">
    <resultMap type="com.puree.followup.domain.medical.model.ReportInspectRecord" id="ReportInspectRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="subject" column="subject" jdbcType="VARCHAR"/>
        <result property="examDate" column="exam_date" jdbcType="TIMESTAMP"/>
        <result property="summary" column="summary" jdbcType="VARCHAR"/>
        <result property="inspectItems" column="inspect_items" />
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="patientId" column="patient_id" jdbcType="BIGINT"/>
        <result property="patientIdNumber" column="patient_id_number" jdbcType="VARCHAR"/>
        <result property="recordId" column="record_id" jdbcType="BIGINT"/>
        <result property="source" column="source" />
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="checkTime" column="check_time" jdbcType="TIMESTAMP"/>
        <result property="orgId" column="org_id" jdbcType="VARCHAR"/>
        <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="getById" resultMap="ReportInspectRecordMap">
        select
        id,subject,exam_date,summary,inspect_items,file_url,hospital_id,patient_id,patient_id_number,
        record_id,source,create_by,create_time,update_by,update_time,is_delete,        check_time,
        org_id,
        org_name
        from medical_report_inspect_record
        where id = #{id}
        and is_delete = 0
    </select>

    <!--分页查询指定行数据-->
    <select id="getList" resultMap="ReportInspectRecordMap">
        select
        id,subject,exam_date,summary,inspect_items,file_url,hospital_id,patient_id,patient_id_number,
        record_id,source,create_by,create_time,update_by,update_time,is_delete ,       check_time,
        org_id,org_name
        from medical_report_inspect_record
        <where>
            is_delete = 0
            <if test="subject != null and subject != ''">
                and subject = #{subject}
            </if>
            <if test="examDate != null ">
                and exam_date = #{examDate}
            </if>
            <if test="summary != null and summary != ''">
                and summary = #{summary}
            </if>
            <if test="hospitalId != null ">
                and hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null ">
                and patient_id = #{patientId}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="recordId != null ">
                and record_id = #{recordId}
            </if>
            <if test="source != null">
                and source = #{source}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
            <if test="updateTime != null ">
                and update_time = #{updateTime}
            </if>
        </where>
        order by check_time desc

    </select>

    <!--分页查询指定行数据-->
    <select id="getPage" resultMap="ReportInspectRecordMap">
        select
        id,subject,exam_date,summary,inspect_items,file_url,hospital_id,patient_id,patient_id_number,
        record_id,source,create_by,create_time,update_by,update_time,is_delete,       check_time,
        org_id,org_name
        from medical_report_inspect_record
        <where>
            is_delete = 0
            <if test="hospitalId != null ">
                and hospital_id = #{hospitalId}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="notSource != null">
                and source != #{notSource}
            </if>

        </where>
        order by check_time desc

    </select>


    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_inspect_record(subject,exam_date,summary,inspect_items,file_url,hospital_id,patient_id,patient_id_number,
        record_id,source,create_by,update_by,is_delete,check_time,
        org_id,
        org_name,create_time)
        values (#{subject},#{examDate},#{summary},#{inspectItems},#{fileUrl},#{hospitalId},#{patientId},#{patientIdNumber},
        #{recordId},#{source},#{createBy},#{updateBy},#{isDelete},#{checkTime},#{orgId}, #{orgName},#{createTime})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_inspect_record(subject,exam_date,summary,inspect_items,file_url,hospital_id,patient_id,patient_id_number,
        record_id,source,create_by,update_by,is_delete,check_time,
        org_id,
        org_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.subject},#{entity.examDate},#{entity.summary},#{entity.inspectItems},#{entity.fileUrl},#{entity.hospitalId},
            #{entity.patientId},#{entity.patientIdNumber},#{entity.recordId},#{entity.source},
            #{entity.createBy},#{entity.updateBy},#{entity.isDelete},#{entity.checkTime},#{entity.orgId},#{entity.orgName})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_inspect_record(id,subject,exam_date,summary,inspect_items,file_url,hospital_id,patient_id,patient_id_number,
        record_id,source,create_by,update_by,is_delete,check_time,
        org_id,
        org_name)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.subject},#{entity.examDate},#{entity.summary},
            #{entity.inspectItems},#{entity.fileUrl},#{entity.hospitalId},#{entity.patientId},#{entity.patientIdNumber},
            #{entity.recordId},#{entity.source},#{entity.createBy},#{entity.updateBy},#{entity.isDelete},#{entity.checkTime},#{entity.orgId},#{entity.orgName})
        </foreach>
        on duplicate key update
        id=values(id),
        subject=values(subject),
        exam_date=values(exam_date),
        summary=values(summary),
        inspect_items=values(inspect_items),
        file_url=values(file_url),
        hospital_id=values(hospital_id),
        patient_id=values(patient_id),
        patient_id_number=values(patient_id_number),
        record_id=values(record_id),
        source=values(source),
        create_by=values(create_by),
        update_by=values(update_by),
        is_delete=values(is_delete),
        check_time=values(check_time),
        org_id=values(org_id),
        org_name=values(org_name)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update medical_report_inspect_record
        <set>
            <if test="subject != null and subject != ''">
                subject = #{subject},
            </if>
            <if test="examDate != null ">
                exam_date = #{examDate},
            </if>
            <if test="summary != null and summary != ''">
                summary = #{summary},
            </if>
            <if test="inspectItems != null and inspectItems != ''">
                inspect_items = #{inspectItems},
            </if>

            <if test="fileUrl != null and fileUrl != ''">
                file_url = #{fileUrl},
            </if>
            <if test="hospitalId != null ">
                hospital_id = #{hospitalId},
            </if>
            <if test="patientId != null ">
                patient_id = #{patientId},
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                patient_id_number = #{patientIdNumber},
            </if>
            <if test="recordId != null ">
                record_id = #{recordId},
            </if>
            <if test="source != null ">
                source = #{source},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null ">
                update_time = #{updateTime},
            </if>
            <if test="isDelete != null ">
                is_delete = #{isDelete},
            </if>
            <if test="checkTime != null ">
                check_time = #{checkTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        update medical_report_inspect_record set is_delete = 1 where id = #{id}
    </delete>


    <select id="getRepeatToday" resultMap="ReportInspectRecordMap">
        select * from medical_report_inspect_record
        <where>
            <if test="time != null ">
                and TO_DAYS(check_time)=TO_DAYS(#{time})
            </if>
            <if test="name != null  and name != ''">
                and org_name = #{name}
            </if>
            <if test="subject != null  and subject != ''">
                and subject = #{subject}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
        </where>
    </select>

    <select id="questionnaireInspectExam" resultMap="ReportInspectRecordMap">
        select * from medical_report_inspect_record
        <where>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="hospitalId != null ">
                and hospital_id = #{hospitalId}
            </if>
            <if test="item != null ">
                and subject in
                <foreach collection="item" item="entity" open="(" separator="," close=")">
                    #{entity.name}
                </foreach>
            </if>
        </where>
    </select>

    <delete id="deleteByRecordId">
        update medical_report_inspect_record set is_delete = 1 where record_id = #{id}
    </delete>
</mapper>