<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.medical.mapper.ReportRegularRecordSummaryMapper">
    <resultMap type="com.puree.followup.domain.medical.model.ReportRegularRecordSummary" id="ReportRegularRecordSummaryMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="tall" column="tall" jdbcType="DECIMAL"/>
        <result property="weight" column="weight" jdbcType="DECIMAL"/>
        <result property="bmi" column="bmi" jdbcType="DECIMAL"/>
        <result property="systolic" column="systolic" jdbcType="DECIMAL"/>
        <result property="diastolic" column="diastolic" jdbcType="DECIMAL"/>
        <result property="pulseRate" column="pulse_rate" jdbcType="DECIMAL"/>
        <result property="bloodSugar" column="blood_sugar" jdbcType="DECIMAL"/>
        <result property="bloodSugarType" column="blood_sugar_type" />
        <result property="bodyTemperature" column="body_temperature" jdbcType="DECIMAL"/>
        <result property="totalCholesterol" column="total_cholesterol" jdbcType="DECIMAL"/>
        <result property="triglycerides" column="triglycerides" jdbcType="DECIMAL"/>
        <result property="hdl" column="hdl" jdbcType="DECIMAL"/>
        <result property="ldl" column="ldl" jdbcType="DECIMAL"/>
        <result property="uricAcid" column="uric_acid" jdbcType="DECIMAL"/>
        <result property="bloodOxygen" column="blood_oxygen" jdbcType="DECIMAL"/>
        <result property="heartRate" column="heart_rate" jdbcType="DECIMAL"/>
        <result property="bloodType" column="blood_type" />
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="patientId" column="patient_id" jdbcType="BIGINT"/>
        <result property="patientIdNumber" column="patient_id_number" jdbcType="VARCHAR"/>
        <result property="bodyMassTime" column="body_mass_time" jdbcType="TIMESTAMP"/>
        <result property="bpTime" column="bp_time" jdbcType="TIMESTAMP"/>
        <result property="bsTime" column="bs_time" jdbcType="TIMESTAMP"/>
        <result property="btTime" column="bt_time" jdbcType="TIMESTAMP"/>
        <result property="blTime" column="bl_time" jdbcType="TIMESTAMP"/>
        <result property="uaTime" column="ua_time" jdbcType="TIMESTAMP"/>
        <result property="boTime" column="bo_time" jdbcType="TIMESTAMP"/>
        <result property="leftSystolic" column="left_systolic" jdbcType="DECIMAL"/>
        <result property="leftDiastolic" column="left_diastolic" jdbcType="DECIMAL"/>
        <result property="iad" column="iad" jdbcType="DECIMAL"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        tall,
        weight,
        blood_type,
        bmi,
        systolic,
        diastolic,
        pulse_rate,
        blood_sugar,
        body_temperature,
        total_cholesterol,
        triglycerides,
        hdl,
        ldl,
        uric_acid,
        blood_oxygen,
        hospital_id,
        patient_id,
        patient_id_number,
        body_mass_time,
        bp_time,
        bs_time,
        bt_time,
        bl_time,
        ua_time,
        bo_time,
        heart_rate,
        blood_sugar_type,
        left_systolic,
        left_diastolic,
        iad,
        create_time
    </sql>

    <!-- 通过ID查询单条数据 -->
    <select id="getById" resultMap="ReportRegularRecordSummaryMap">
        select
        <include refid="Base_Column_List"/>
        from medical_report_regular_record_summary
        where id = #{id}
    </select>

    <!--分页查询指定行数据-->
    <select id="getList" resultMap="ReportRegularRecordSummaryMap">
        select
        <include refid="Base_Column_List"/>
        from medical_report_regular_record_summary
        <where>
            <if test="tall != null and tall != ''">
                and tall = #{tall}
            </if>
            <if test="weight != null and weight != ''">
                and weight = #{weight}
            </if>
            <if test="bmi != null and bmi != ''">
                and bmi = #{bmi}
            </if>
            <if test="systolic != null and systolic != ''">
                and systolic = #{systolic}
            </if>
            <if test="diastolic != null and diastolic != ''">
                and diastolic = #{diastolic}
            </if>
            <if test="pulseRate != null and pulseRate != ''">
                and pulse_rate = #{pulseRate}
            </if>
            <if test="bloodSugar != null and bloodSugar != ''">
                and blood_sugar = #{bloodSugar}
            </if>
            <if test="bodyTemperature != null and bodyTemperature != ''">
                and body_temperature = #{bodyTemperature}
            </if>
            <if test="totalCholesterol != null and totalCholesterol != ''">
                and total_cholesterol = #{totalCholesterol}
            </if>
            <if test="triglycerides != null and triglycerides != ''">
                and triglycerides = #{triglycerides}
            </if>
            <if test="hdl != null and hdl != ''">
                and hdl = #{hdl}
            </if>
            <if test="ldl != null and ldl != ''">
                and ldl = #{ldl}
            </if>
            <if test="uricAcid != null and uricAcid != ''">
                and uric_acid = #{uricAcid}
            </if>
            <if test="bloodOxygen != null and bloodOxygen != ''">
                and blood_oxygen = #{bloodOxygen}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
            <if test="patientId != null and patientId != ''">
                and patient_id = #{patientId}
            </if>
            <if test="patientIdNumber != null and patientIdNumber != ''">
                and patient_id_number = #{patientIdNumber}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
        </where>
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_regular_record_summary(tall,
                                                          weight,
                                                          blood_type,
                                                          bmi,
                                                          systolic,
                                                          diastolic,
                                                          pulse_rate,
                                                          blood_sugar,
                                                          body_temperature,
                                                          total_cholesterol,
                                                          triglycerides,
                                                          hdl,
                                                          ldl,
                                                          uric_acid,
                                                          blood_oxygen,
                                                          hospital_id,
                                                          patient_id,
                                                          patient_id_number,
                                                          body_mass_time,
                                                          bp_time,
                                                          bs_time,
                                                          bt_time,
                                                          bl_time,
                                                          ua_time,
                                                          bo_time,
                                                          heart_rate,
                                                          blood_sugar_type,
                                                          left_systolic,
                                                          left_diastolic,
                                                          iad)
                                                values (#{tall},
                                                        #{weight},
                                                        #{bloodType},
                                                        #{bmi},
                                                        #{systolic},
                                                        #{diastolic},
                                                        #{pulseRate},
                                                        #{bloodSugar},
                                                        #{bodyTemperature},
                                                        #{totalCholesterol},
                                                        #{triglycerides},
                                                        #{hdl},#{ldl},
                                                        #{uricAcid},
                                                        #{bloodOxygen},
                                                        #{hospitalId},
                                                        #{patientId},
                                                        #{patientIdNumber},
                                                        #{bodyMassTime},
                                                        #{bpTime},
                                                        #{bsTime},
                                                        #{btTime},
                                                        #{blTime},
                                                        #{uaTime},
                                                        #{boTime},
                                                        #{heartRate},
                                                        #{bloodSugarType},
                                                        #{leftSystolic},
                                                        #{leftDiastolic},
                                                        #{iad})
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update medical_report_regular_record_summary
        <set>
            tall = #{tall},
            weight = #{weight},
            blood_type = #{bloodType},
            bmi = #{bmi},
            systolic = #{systolic},
            diastolic = #{diastolic},
            pulse_rate = #{pulseRate},
            blood_sugar = #{bloodSugar},
            blood_sugar_type = #{bloodSugarType},
            body_temperature = #{bodyTemperature},
            total_cholesterol = #{totalCholesterol},
            triglycerides = #{triglycerides},
            hdl = #{hdl},
            ldl = #{ldl},
            uric_acid = #{uricAcid},
            blood_oxygen = #{bloodOxygen},
            heart_rate = #{heartRate},
            hospital_id = #{hospitalId},
            patient_id = #{patientId},
            patient_id_number = #{patientIdNumber},
            left_systolic = #{leftSystolic},
            left_diastolic = #{leftDiastolic},
            iad = #{iad},
            <if test="bodyMassTime != null ">
                body_mass_time = #{bodyMassTime},
            </if>
            <if test="bpTime != null ">
                bp_time = #{bpTime},
            </if>
            <if test="bsTime != null ">
                bs_time = #{bsTime},
            </if>
            <if test="btTime != null">
                bt_time = #{btTime},
            </if>
            <if test="blTime != null ">
                bl_time = #{blTime},
            </if>
            <if test="uaTime != null ">
                ua_time = #{uaTime},
            </if>
            <if test="boTime != null">
                bo_time = #{boTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>