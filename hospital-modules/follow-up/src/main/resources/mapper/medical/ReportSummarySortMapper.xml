<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.medical.mapper.ReportSummarySortMapper">
    <resultMap type="com.puree.followup.domain.medical.model.ReportSummarySort" id="ReportSummarySort">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="sortNum" column="sort_num" jdbcType="INTEGER"/>
        <result property="sortType" column="sort_type" />
        <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="revision" column="revision" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="getById" resultMap="ReportSummarySort">
        select
        id,sort_num,sort_type,hospital_id,user_id,revision,create_by,create_time,update_by,update_time
        from medical_report_summary_sort
        where id = #{id}
    </select>

    <!--分页查询指定行数据-->
    <select id="getList" resultMap="ReportSummarySort">
        select
        id,sort_num,sort_type,hospital_id,user_id,revision,create_by,create_time,update_by,update_time
        from medical_report_summary_sort
        <where>
            <if test="sortNum != null and sortNum != ''">
                and sort_num = #{sortNum}
            </if>
            <if test="sortType != null and sortType != ''">
                and sort_type = #{sortType}
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                and hospital_id = #{hospitalId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="revision != null and revision != ''">
                and revision = #{revision}
            </if>
        </where>
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_summary_sort(sort_num,sort_type,hospital_id,user_id,revision,create_by,update_by)
        values (#{sortNum},#{sortType},#{hospitalId},#{userId},#{revision},#{createBy},#{updateBy})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_summary_sort(sort_num,sort_type,hospital_id,user_id,revision,create_by,update_by)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.sortNum},#{entity.sortType},#{entity.hospitalId},#{entity.userId},#{entity.revision},#{entity.createBy},#{entity.updateBy})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into medical_report_summary_sort(id,sort_num,sort_type,hospital_id,user_id,revision,create_by)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.sortNum},#{entity.sortType},#{entity.hospitalId},#{entity.userId},#{entity.revision},#{entity.createBy})
        </foreach>
        on duplicate key update
        id=values(id),
        sort_num=values(sort_num),
        sort_type=values(sort_type),
        hospital_id=values(hospital_id),
        user_id=values(user_id),
        revision=values(revision),
        create_by=values(create_by)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update medical_report_summary_sort
        <set>
            <if test="sortNum != null and sortNum != ''">
                sort_num = #{sortNum},
            </if>
            <if test="sortType != null and sortType != ''">
                sort_type = #{sortType},
            </if>
            <if test="hospitalId != null and hospitalId != ''">
                hospital_id = #{hospitalId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="revision != null and revision != ''">
                revision = #{revision},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>