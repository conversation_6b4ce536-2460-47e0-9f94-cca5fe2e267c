<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.classification.mapper.ClassificationMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.classification.model.Classification">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>

  <sql id="classificationSql">
    id, `name`, data_type, `source`, hospital_id, revision, create_by, create_time, update_by, 
    update_time, is_delete
  </sql>


  <select id="getList" parameterType="com.puree.followup.domain.classification.model.Classification" resultMap="BaseResultMap">
    select
    <include refid="classificationSql" />
    from classification t1
    <where>
      ( t1.is_delete = -1 or t1.is_delete = 0 )
      <if test="name != null and name != ''">
        and t1.name = #{name}
      </if>
      <if test="dataType != null">
        and t1.data_type = #{dataType}
      </if>
      <if test="source != null">
        and t1.source = #{source}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
    </where>
    order by update_time desc
  </select>


  <select id="getOne" parameterType="com.puree.followup.domain.classification.model.Classification" resultMap="BaseResultMap">
    select
    <include refid="classificationSql" />
    from classification t1
    <where>
      <if test="dataType != null">
        and t1.data_type = #{dataType}
      </if>
      <if test="source != null">
        and t1.source = #{source}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    limit 1
  </select>


  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="classificationSql" />
    from classification
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.classification.model.Classification" useGeneratedKeys="true">
    insert into classification (`name`, data_type, `source`, 
      hospital_id, create_by,
      create_time, update_by, update_time, 
      is_delete)
    values (#{name,jdbcType=VARCHAR}, #{dataType,jdbcType=TINYINT}, #{source,jdbcType=TINYINT}, 
      #{hospitalId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{isDelete,jdbcType=TINYINT})
    on duplicate key update
    name = values(name),
    data_type = values(data_type),
    source = values(source),
    hospital_id = values(hospital_id),
    create_by = values(create_by),
    create_time = values(create_time),
    update_by = values(update_by),
    update_time = values(update_time),
    is_delete = values(is_delete)
  </insert>


  <update id="updateById" parameterType="com.puree.followup.domain.classification.model.Classification">
    update classification t1
    <set>
      <if test="name != null">
        t1.name = #{name},
      </if>
      <if test="revision != null">
        t1.revision = t1.revision + 1,
      </if>
      <if test="updateBy != null">
        t1.update_by = #{updateBy},
      </if>
      <if test="updateTime != null">
        t1.update_time = #{updateTime},
      </if>
      <if test="isDelete != null">
        t1.is_delete = #{isDelete},
      </if>
    </set>
    where id = #{id} and t1.revision = #{revision}
  </update>

  <select id="findDuplicateName" resultType="int">
    select count(*) from classification
    where `name`=trim(#{name}) and data_type=#{dataType} and `source`=#{source} and hospital_id=#{hospitalId} and (is_delete=0 or is_delete=-1)
    <if test=" id!=null ">
      and id != #{id}
    </if>
  </select>

  <select id="getQuestionClassificationList" parameterType="com.puree.followup.domain.classification.model.Classification" resultMap="BaseResultMap">
    select
    <include refid="classificationSql" />
    from classification t1
    <where>
      ( t1.is_delete = -1 or t1.is_delete = 0 )
      <if test="name != null and name != ''">
        and t1.name = #{name}
      </if>
      <if test="dataType != null">
        and t1.data_type = #{dataType}
      </if>
      <if test="source != null">
        and t1.source = #{source}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
    </where>
    order by create_time desc
  </select>

</mapper>