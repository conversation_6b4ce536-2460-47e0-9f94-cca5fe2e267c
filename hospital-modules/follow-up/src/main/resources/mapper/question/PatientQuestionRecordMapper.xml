<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.question.mapper.PatientQuestionRecordMapper">

    <resultMap id="BaseResultMap" type="com.puree.followup.question.domain.model.PatientQuestionRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="hospitalId" column="hospital_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="patientId" column="patient_id" jdbcType="BIGINT"/>
            <result property="questionId" column="question_id" jdbcType="BIGINT"/>
            <result property="followUpId" column="follow_up_id" jdbcType="BIGINT"/>
            <result property="patientEventId" column="patient_event_id" jdbcType="BIGINT"/>
            <result property="doctorId" column="doctor_id" jdbcType="BIGINT"/>
            <result property="doctorName" column="doctor_name" jdbcType="VARCHAR"/>
            <result property="assistantId" column="assistant_id" jdbcType="BIGINT"/>
            <result property="assistantName" column="assistant_name" jdbcType="VARCHAR"/>
            <result property="fillStatus" column="fill_status" jdbcType="TINYINT"/>
            <result property="fillTime" column="fill_time" jdbcType="TIMESTAMP"/>
            <result property="submitTime" column="submit_time" jdbcType="TIMESTAMP"/>
            <result property="totalScore" column="total_score" jdbcType="DECIMAL"/>
            <result property="answerContent" column="answer_content" />
            <result property="answerScoreValue" column="answer_score_value" javaType="com.puree.followup.question.domain.model.ScoreValue" typeHandler="com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler"/>
            <result property="measureTime" column="measure_time" jdbcType="TIMESTAMP"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,hospital_id,user_id,
        patient_id,
        question_id,follow_up_id, patient_event_id ,
        doctor_id,doctor_name, assistant_id, assistant_name, fill_status,
        fill_time,submit_time,total_score,
        answer_content,answer_score_value, measure_time , group_id ,
        create_by, create_time,update_by,update_time,
        is_delete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from patient_question_record
        where  id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from patient_question_record
        where  id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.question.domain.model.PatientQuestionRecord" useGeneratedKeys="true">
        insert into patient_question_record
        ( hospital_id,user_id
        ,patient_id
        ,question_id
        ,follow_up_id
        , patient_event_id
        ,doctor_id,doctor_name,assistant_id,assistant_name,fill_status
        ,fill_time,submit_time,total_score
        ,answer_content,answer_score_value, measure_time
        ,group_id,create_by ,create_time,update_by,update_time
        ,is_delete)
        values ( #{hospitalId,jdbcType=BIGINT},#{userId,jdbcType=BIGINT}
        ,#{patientId,jdbcType=BIGINT}
        ,#{questionId,jdbcType=BIGINT},#{followUpId,jdbcType=BIGINT}, #{patientEventId}
        ,#{doctorId,jdbcType=BIGINT},#{doctorName,jdbcType=VARCHAR},#{assistantId,jdbcType=BIGINT},#{assistantName,jdbcType=VARCHAR},#{fillStatus,jdbcType=TINYINT}
        ,#{fillTime,jdbcType=TIMESTAMP},#{submitTime,jdbcType=TIMESTAMP},#{totalScore,jdbcType=DECIMAL}
        ,#{answerContent },#{answerScoreValue, typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler },#{measureTime,jdbcType=TIMESTAMP}
        ,#{groupId,jdbcType=BIGINT} , #{createBy,jdbcType=VARCHAR} ,#{createTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        ,#{isDelete,jdbcType=TINYINT})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.question.domain.model.PatientQuestionRecord" useGeneratedKeys="true">
        insert into patient_question_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="hospitalId != null">hospital_id,</if>
                <if test="userId != null">user_id,</if>
                <if test="patientId != null">patient_id,</if>
                <if test="questionId != null">question_id,</if>
                <if test="followUpId != null">follow_up_id,</if>
                <if test="patientEventId != null">patient_event_id,</if>
                <if test="doctorId != null">doctor_id,</if>
                <if test=" doctorName != null and doctorName!='' ">doctor_name,</if>
                <if test="fillStatus != null">fill_status,</if>
                <if test="fillTime != null">fill_time,</if>
                <if test="submitTime != null">submit_time,</if>
                <if test="totalScore != null">total_score,</if>
                <if test=" answerContent != null and answerContent!='' ">answer_content,</if>
                <if test="answerScoreValue != null">answer_score_value,</if>
                <if test=" measureTime != null ">measure_time,</if>
                <if test=" groupId != null ">group_id,</if>
                <if test=" createBy != null and createBy!='' ">create_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test=" updateBy != null and updateBy!='' ">update_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="hospitalId != null">#{hospitalId,jdbcType=BIGINT},</if>
                <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
                <if test="patientId != null">#{patientId,jdbcType=BIGINT},</if>
                <if test="questionId != null">#{questionId,jdbcType=BIGINT},</if>
                <if test="followUpId != null">#{followUpId,jdbcType=BIGINT},</if>
                <if test="patientEventId != null">#{patientEventId},</if>
                <if test="doctorId != null">#{doctorId,jdbcType=BIGINT},</if>
                <if test="doctorName != null and doctorName!='' ">#{doctorName,jdbcType=VARCHAR},</if>
                <if test="fillStatus != null">#{fillStatus,jdbcType=TINYINT},</if>
                <if test="fillTime != null">#{fillTime,jdbcType=TIMESTAMP},</if>
                <if test="submitTime != null">#{submitTime,jdbcType=TIMESTAMP},</if>
                <if test="totalScore != null">#{totalScore,jdbcType=DECIMAL},</if>
                <if test=" answerContent != null and answerContent!='' ">#{answerContent},</if>
                <if test="answerScoreValue != null">#{answerScoreValue,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler },</if>
                <if test=" measureTime != null "> #{measureTime,jdbcType=TIMESTAMP},</if>
                <if test=" groupId != null "> #{groupId,jdbcType=BIGINT},</if>
                <if test=" createBy != null and createBy!='' ">#{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test=" updateBy != null and updateBy!='' ">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.puree.followup.question.domain.model.PatientQuestionRecord">
        update patient_question_record
        <set>
                <if test="hospitalId != null">
                    hospital_id = #{hospitalId,jdbcType=BIGINT},
                </if>
                <if test="userId != null">
                    user_id = #{userId,jdbcType=BIGINT},
                </if>
                <if test="patientId != null">
                    patient_id = #{patientId,jdbcType=BIGINT},
                </if>
                <if test="questionId != null">
                    question_id = #{questionId,jdbcType=BIGINT},
                </if>
                <if test="followUpId != null">
                    follow_up_id = #{followUpId,jdbcType=BIGINT},
                </if>
                <if test=" patientEventId != null">
                    patient_event_id = #{patientEventId},
                </if>
                <if test="doctorId != null">
                    doctor_id = #{doctorId,jdbcType=BIGINT},
                </if>
                <if test=" doctorName != null and doctorName!='' ">
                    doctor_name = #{doctorName,jdbcType=VARCHAR},
                </if>
                <if test="assistantId != null">
                    assistant_id = #{assistantId,jdbcType=BIGINT},
                </if>
                <if test=" assistantName != null and assistantName!='' ">
                    assistant_name = #{assistantName,jdbcType=VARCHAR},
                </if>
                <if test="fillStatus != null">
                    fill_status = #{fillStatus,jdbcType=TINYINT},
                </if>
                <if test="fillTime != null">
                    fill_time = #{fillTime,jdbcType=TIMESTAMP},
                </if>
                <if test="submitTime != null">
                    submit_time = #{submitTime,jdbcType=TIMESTAMP},
                </if>
                <if test="totalScore != null">
                    total_score = #{totalScore,jdbcType=DECIMAL},
                </if>
                <if test=" answerContent != null and answerContent!='' ">
                    answer_content = #{answerContent},
                </if>
                <if test="answerScoreValue != null">
                    answer_score_value = #{answerScoreValue,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler },
                </if>
                <if test=" measureTime != null  ">
                    measure_time = #{measureTime,jdbcType=TIMESTAMP},
                </if>
                <if test=" createBy != null and createBy!='' ">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test=" updateBy != null and updateBy!='' ">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="isDelete != null">
                    is_delete = #{isDelete,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.puree.followup.question.domain.model.PatientQuestionRecord">
        update patient_question_record
        set
            hospital_id =  #{hospitalId,jdbcType=BIGINT},
            user_id =  #{userId,jdbcType=BIGINT},
            patient_id =  #{patientId,jdbcType=BIGINT},
            question_id =  #{questionId,jdbcType=BIGINT},
            follow_up_id =  #{followUpId,jdbcType=BIGINT},
            patient_event_id =  #{patientEventId},
            doctor_id =  #{doctorId,jdbcType=BIGINT},
            doctor_name =  #{doctorName,jdbcType=VARCHAR},
            assistant_id =  #{assistantId,jdbcType=BIGINT},
            assistant_name =  #{assistantName,jdbcType=VARCHAR},
            fill_status =  #{fillStatus,jdbcType=TINYINT},
            fill_time =  #{fillTime,jdbcType=TIMESTAMP},
            submit_time =  #{submitTime,jdbcType=TIMESTAMP},
            total_score =  #{totalScore,jdbcType=DECIMAL},
            answer_content =  #{answerContent},
            answer_score_value =  #{answerScoreValue, typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler },
            measure_time = #{measureTime,jdbcType=TIMESTAMP},
            create_by =  #{createBy,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            is_delete =  #{isDelete,jdbcType=TINYINT}
        where   id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateFillStatusByFillStatus">
        update patient_question_record set fill_status=#{fillStatus}
        where 1=1
            <if test=" questionId!=null ">
                and question_id=#{questionId}
            </if>
            and fill_status in
            <foreach collection="fillStatusList" open="("  separator=","  close=")"  item="item" >
                #{item}
            </foreach>
            and follow_up_id is null
    </update>

    <select id="countQuestionByFillStatus" resultType="int">
        select count(*) from patient_question_record
        <where>

            <if test=" hospitalId!=null ">
                and hospital_id=#{hospitalId}
            </if>
            <if test=" questionId!=null ">
                and question_id=#{questionId}
            </if>
            <if test=" userId!=null ">
                and user_id=#{userId}
            </if>
            <if test=" patientId!=null ">
                and patient_id=#{patientId}
            </if>
            <if test=" doctorId!=null ">
                and doctor_id=#{doctorId}
            </if>
            <if test=" createTime!=null ">
                and create_time=#{createTime,jdbcType=DATE}
            </if>
            <if test=" submitTime!=null ">
                and date_format(submit_time,'%Y-%m-%d') = #{submitTime,jdbcType=DATE}
            </if>
            <if test=" isDelete!=null ">
                and is_delete=#{isDelete}
            </if>
            and fill_status in
            <foreach collection="fillStatusList" open="("  separator=","  close=")"  item="item" >
                #{item}
            </foreach>

        </where>
    </select>

    <select id="countPatientByFillStatus" resultType="int">
        select count(*)
        from (select patient_id
              from patient_question_record
              where hospital_id = #{hospitalId}
                and question_id = #{questionId}
                and is_delete = #{isDelete}
                and fill_status in
                <foreach collection="fillStatusList" open="("  separator=","  close=")"  item="item" >
                    #{item}
                </foreach>
              group by patient_id) as t
    </select>

    <select id="findPatientByFillStatus" resultType="java.lang.Long">
        select
            patient_id
        from patient_question_record
        where hospital_id=#{hospitalId} and question_id=#{questionId} and is_delete=#{isDelete}
        and fill_status in
        <foreach collection="fillStatusList" open="("  separator=","  close=")"  item="item" >
            #{item}
        </foreach>
        and follow_up_id is null and doctor_id is null
        group by patient_id having count(*) = #{fillTimes}
    </select>

    <select id="patientFindQuestionList" resultMap="BaseResultMap">
        select t1.*
        from
        patient_question_record as t1 inner join question as t2 on t1.question_id=t2.id
        where
        t1.user_id=#{userId}  and  t1.is_delete=0
        and t2.is_delete=0
        and t2.`status` in
        <foreach collection="questionStatusList" open="("  separator=","  close=")"  item="item">
            #{item}
        </foreach>
    </select>

    <select id="countOnePatientFillOneQuestionTimes" resultType="int">

        select
        count(*)
        from patient_question_record
        where hospital_id=#{hospitalId} and question_id=#{questionId} and patient_id=#{patientId} and is_delete=#{isDelete}
        and fill_status in
        <foreach collection="fillStatusList" open="("  separator=","  close=")"  item="item" >
            #{item}
        </foreach>
        and follow_up_id is null

    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from patient_question_record
        where  1=1
        <if test=" id!=null ">
            and id=#{id}
        </if>
        <if test="hospitalId != null">
            and hospital_id = #{hospitalId,jdbcType=BIGINT}
        </if>
        <if test="userId != null">
            and user_id = #{userId,jdbcType=BIGINT}
        </if>
        <if test="patientId != null">
            and patient_id = #{patientId,jdbcType=BIGINT}
        </if>
        <if test="questionId != null">
            and question_id = #{questionId,jdbcType=BIGINT}
        </if>

        <if test=" followUpId!=null and followUpId!=0 ">
            and follow_up_id = #{followUpId,jdbcType=BIGINT}
        </if>
        <if test=" followUpId!=null and followUpId==0 ">
            and follow_up_id is null
        </if>

        <if test=" patientEventId != null">
            and patient_event_id = #{patientEventId}
        </if>
        <if test="doctorId != null">
            and doctor_id = #{doctorId,jdbcType=BIGINT}
        </if>
        <if test=" doctorName != null and doctorName!='' ">
            and doctor_name = #{doctorName,jdbcType=VARCHAR}
        </if>
        <if test="assistantId != null">
            and assistant_id = #{assistantId,jdbcType=BIGINT}
        </if>
        <if test=" assistantName != null and doctorName!='' ">
            and assistant_name = #{assistantName,jdbcType=VARCHAR}
        </if>
        <if test="fillStatus != null">
            and fill_status = #{fillStatus,jdbcType=TINYINT}
        </if>
        <if test="fillTime != null">
            and fill_time = #{fillTime,jdbcType=TIMESTAMP}
        </if>
        <if test="submitTime != null">
            and submit_time = #{submitTime,jdbcType=TIMESTAMP}
        </if>
        <if test="totalScore != null">
            and total_score = #{totalScore,jdbcType=DECIMAL}
        </if>
        <if test=" answerContent != null and answerContent!='' ">
            and answer_content = #{answerContent}
        </if>
        <if test="answerScoreValue != null">
            and answer_score_value = #{answerScoreValue,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler }
        </if>

        <if test=" patientIdList!=null and patientIdList.size() >0 ">
            and patient_id in
            <foreach collection="patientIdList" item="item" separator="," open="("  close=")" >
                #{item}
            </foreach>
        </if>

        <if test=" beginDate!=null " >
            and submit_time <![CDATA[>=]]> #{beginDate}
        </if>
        <if test=" endDate!=null " >
            and submit_time <![CDATA[<=]]> #{endDate}
        </if>
        order by submit_time desc

    </select>

    <select id="doctorFindPatientQuestion" resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List" />
        from patient_question_record
        where  hospital_id = #{hospitalId} and question_id = #{questionId} and doctor_id = #{doctorId}
        <if test="fillStatus != null">
            and fill_status = #{fillStatus}
        </if>

        <if test="isDelete != null">
            and is_delete = #{isDelete}
        </if>

        <if test="fillTime != null">
            and fill_time = #{fillTime,jdbcType=TIMESTAMP}
        </if>

        <if test=" submitTimeStart != null">
            and submit_time <![CDATA[>=]]> #{submitTimeStart}
        </if>
        <if test=" submitTimeEnd != null">
            and submit_time <![CDATA[<=]]> #{submitTimeEnd}
        </if>

        <if test="totalScore != null">
            and total_score = #{totalScore,jdbcType=DECIMAL}
        </if>
        <if test="answerScoreValue != null">
            and answer_score_value = #{answerScoreValue,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler }
        </if>

        <if test=" patientIdList != null and patientIdList.size() > 0 ">
            and patient_id in
            <foreach collection="patientIdList" open="("  separator=","  close=")"  item="item" >
                #{item}
            </foreach>
        </if>

        order by submit_time desc

    </select>

    <update id="batchUpdateFillStatus">
        update
        patient_question_record as r inner join question as q on r.question_id=q.id and q.`status`=1
        set r.fill_status=3
        where r.fill_status in (0,1)  and follow_up_id is null
    </update>

    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from patient_question_record
        <where>
            <if test=" id!=null ">
                and id=#{id}
            </if>
            <if test="hospitalId != null">
                and hospital_id = #{hospitalId,jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                and user_id = #{userId,jdbcType=BIGINT}
            </if>
            <if test="patientId != null">
                and patient_id = #{patientId,jdbcType=BIGINT}
            </if>
            <if test="questionId != null">
                and question_id = #{questionId,jdbcType=BIGINT}
            </if>
            <if test="followUpId != null">
                and follow_up_id = #{followUpId,jdbcType=BIGINT}
            </if>
            <if test=" patientEventId != null">
                and patient_event_id = #{patientEventId}
            </if>
            <if test="doctorId != null">
                and doctor_id = #{doctorId,jdbcType=BIGINT}
            </if>
            <if test=" doctorName != null and doctorName!='' ">
                and doctor_name = #{doctorName,jdbcType=VARCHAR}
            </if>
            <if test="assistantId != null">
                and assistant_id = #{assistantId,jdbcType=BIGINT}
            </if>
            <if test=" assistantName != null and assistantName!='' ">
                and assistant_name = #{assistantName,jdbcType=VARCHAR}
            </if>
            <if test="fillStatus != null">
                and fill_status = #{fillStatus,jdbcType=TINYINT}
            </if>
            <if test="fillTime != null">
                and fill_time = #{fillTime,jdbcType=TIMESTAMP}
            </if>
            <if test="submitTime != null">
                and submit_time = #{submitTime,jdbcType=TIMESTAMP}
            </if>
            <if test="totalScore != null">
                and total_score = #{totalScore,jdbcType=DECIMAL}
            </if>
            <if test=" answerContent != null and answerContent!='' ">
                and answer_content = #{answerContent}
            </if>
            <if test="answerScoreValue != null">
                and answer_score_value = #{answerScoreValue,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler }
            </if>
            <if test="isDelete != null">
                and is_delete = #{isDelete}
            </if>
        </where>
        limit 1

    </select>

    <update id="updateFillStatusByPatientEventId" parameterType="com.puree.followup.question.domain.model.PatientQuestionRecord">
        update patient_question_record
        <set>
            <if test="fillStatus != null">
                fill_status = #{fillStatus,jdbcType=TINYINT},
            </if>
            <if test="fillTime != null">
                fill_time = #{fillTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where  hospital_id = #{hospitalId} and patient_event_id = #{patientEventId} and fill_status = 0
    </update>


</mapper>