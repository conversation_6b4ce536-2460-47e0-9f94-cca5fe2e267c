<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.question.mapper.FollowUpQuestionRecordMapper">

    <resultMap id="BaseResultMap" type="com.puree.followup.question.domain.model.FollowUpQuestionRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="questionId" column="question_id" jdbcType="BIGINT"/>
            <result property="followUpId" column="follow_up_id" jdbcType="BIGINT"/>
            <result property="itemId" column="item_id" jdbcType="BIGINT"/>
            <result property="eventId" column="event_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id , question_id, follow_up_id ,
        item_id , event_id ,create_by ,create_time ,
        update_by , update_time , is_delete
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from follow_up_question_record
        where  id = #{id,jdbcType=BIGINT} 
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from follow_up_question_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.question.domain.model.FollowUpQuestionRecord" useGeneratedKeys="true">
        insert into follow_up_question_record
        ( question_id , follow_up_id
        ,item_id ,event_id,create_by,create_time
        ,update_by,update_time,is_delete
        )
        values ( #{questionId,jdbcType=BIGINT} , #{followUpId,jdbcType=BIGINT}
        ,#{itemId,jdbcType=BIGINT}, #{eventId,jdbcType=BIGINT},#{createBy,jdbcType=VARCHAR},#{createTime,jdbcType=TIMESTAMP}
        ,#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP},#{isDelete,jdbcType=TINYINT}
        )
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.question.domain.model.FollowUpQuestionRecord" useGeneratedKeys="true">
        insert into follow_up_question_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="questionId != null">question_id,</if>
                <if test="followUpId != null">follow_up_id,</if>
                <if test="itemId != null">item_id,</if>
                <if test="eventId != null">event_id,</if>
                <if test="createBy != null">create_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="questionId != null">#{questionId,jdbcType=BIGINT},</if>
                <if test="followUpId != null">#{followUpId,jdbcType=BIGINT},</if>
                <if test="itemId != null">#{itemId,jdbcType=BIGINT},</if>
                <if test="eventId != null">#{eventId},</if>
                <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.puree.followup.question.domain.model.FollowUpQuestionRecord">
        update follow_up_question_record
        <set>
                <if test="questionId != null">
                    question_id = #{questionId,jdbcType=BIGINT},
                </if>
                <if test="followUpId != null">
                    follow_up_id = #{followUpId,jdbcType=BIGINT},
                </if>
                <if test="itemId != null">
                    item_id = #{itemId,jdbcType=BIGINT},
                </if>
                <if test="eventId != null">
                    event_id = #{eventId},
                </if>
                <if test=" createBy != null and createBy!='' ">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test=" updateBy != null and updateBy!='' ">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="isDelete != null">
                    is_delete = #{isDelete,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <update id="updateByPrimaryKey" parameterType="com.puree.followup.question.domain.model.FollowUpQuestionRecord">
        update follow_up_question_record
        set
            question_id =  #{questionId,jdbcType=BIGINT},
            follow_up_id =  #{followUpId,jdbcType=BIGINT},
            item_id =  #{itemId,jdbcType=BIGINT},
            event_id =  #{eventId},
            create_by =  #{createBy,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            is_delete =  #{isDelete,jdbcType=TINYINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <update id="updateIsDelete" parameterType="com.puree.followup.question.domain.model.FollowUpQuestionRecord">
        update follow_up_question_record
        set
            update_by =  #{updateBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            is_delete =  #{isDelete,jdbcType=TINYINT}
        where follow_up_id = #{followUpId,jdbcType=BIGINT}
    </update>

    <select id="selectByCondition"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from follow_up_question_record
        <where>
            <if test=" id!=null ">
                and id = #{id}
            </if>
            <if test=" questionId!=null ">
                and question_id = #{questionId}
            </if>
            <if test=" followUpId!=null ">
                and follow_up_id = #{followUpId}
            </if>
            <if test=" itemId!=null ">
                and item_id = #{itemId}
            </if>
            <if test=" eventId!=null ">
                and event_id = #{eventId}
            </if>
            <if test=" isDelete!=null ">
                and is_delete = #{isDelete}
            </if>
        </where>
    </select>

    <select id="countByCondition"  resultType="int">
        select
        count(*)
        from follow_up_question_record
        <where>
            <if test=" id!=null ">
                and id = #{id}
            </if>
            <if test=" questionId!=null ">
                and question_id = #{questionId}
            </if>
            <if test=" followUpId!=null ">
                and follow_up_id = #{followUpId}
            </if>
            <if test=" itemId!=null ">
                and item_id = #{itemId}
            </if>
            <if test=" eventId!=null ">
                and event_id = #{eventId}
            </if>
            <if test=" isDelete!=null ">
                and is_delete = #{isDelete}
            </if>
        </where>
    </select>



</mapper>
