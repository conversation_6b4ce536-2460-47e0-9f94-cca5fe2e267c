<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.question.mapper.QuestionMapper">

    <resultMap id="BaseResultMap" type="com.puree.followup.question.domain.model.Question">
        <id     column="id"    property="id" />
        <result column="name"  property="name" />
        <result column="desc"  property="desc" />
        <result column="remark" property="remark" />
        <result column="hospital_id" property="hospitalId" />
        <result column="classify_id" property="classifyId" />
        <result column="status" property="status" />
        <result column="show_order" property="showOrder" />
        <result column="department_limit"  property="departmentLimit" />
        <result column="doctor_limit"      property="doctorLimit" />
        <result column="close_type"        property="closeType" />
        <result column="close_begin_day"   property="closeBeginDay" />
        <result column="fill_times"      property="fillTimes" />
        <result column="can_revise"      property="canRevise" />
        <result column="is_score"        property="isScore" />
        <result column="question_content"  property="questionContent" />
        <result column="question_score_values"  javaType="java.util.List" property="questionScoreValues" typeHandler="com.puree.followup.config.ScoreValueTypeHandler" />
        <result column="revision"    property="revision" />
        <result column="create_by"   property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by"   property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="is_delete"   property="isDelete" />
    </resultMap>

    <sql id="Base_Column_List">
        id, `name`, `desc`, remark, hospital_id,
        classify_id, `status` , show_order, department_limit,
        doctor_limit, close_type, close_begin_day,  fill_times,
        can_revise, is_score, question_content, question_score_values, revision,
        create_by, create_time, update_by, update_time, is_delete, applet_qr_code
    </sql>
    
    <select id="selectById" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List" /> from question where id=#{id}
        <if test=" isDelete!=null ">
            and is_delete=#{isDelete}
        </if>
    </select>
    
    <select id="selectByCondition" resultMap="BaseResultMap">
        select  <include refid="Base_Column_List" /> from question
        <where>

            <if test=" id!=null ">
                and id=#{id}
            </if>
            <if test=" hospitalId!=null ">
                and hospital_id=#{hospitalId}
            </if>
            <if test=" classifyId!=null ">
                and classify_id=#{classifyId}
            </if>
            <if test=" status!=null ">
                and `status`=#{status}
            </if>
            <if test=" name!=null and name!='' ">
                and `name` like concat('%', trim( #{name} ) , '%')
            </if>
            <if test=" isDelete != null ">
                and is_delete = #{isDelete}
            </if>
        </where>

        <if test=" statusOrder != null and statusOrder ">
            order by status desc, create_time desc
        </if>
        <if test=" statusOrder == null or !statusOrder ">
            order by create_time desc
        </if>
    </select>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.question.domain.model.Question" useGeneratedKeys="true">
        insert into question ( `name`, `desc`, remark, hospital_id,
                              classify_id, `status` ,  show_order, department_limit,
                              doctor_limit, close_type, close_begin_day, fill_times,
                              can_revise, is_score, question_content, question_score_values, revision,
                              create_by, create_time, update_by, update_time, is_delete
        )
        values ( #{name}, #{desc}, #{remark}, #{hospitalId},
        #{classifyId}, #{status}, #{showOrder}, #{departmentLimit},
        #{doctorLimit}, #{closeType}, #{closeBeginDay},  #{fillTimes},
        #{canRevise}, #{isScore}, #{questionContent}, #{questionScoreValues, typeHandler=com.puree.followup.config.ScoreValueTypeHandler}, #{revision},
        #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{isDelete}
        )
    </insert>

    <update id="updateByCondition">

        update question
        <set>
            <if test=" name != null and name!='' ">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test=" desc != null and desc!='' ">
                `desc` = #{desc,jdbcType=VARCHAR},
            </if>
            <if test=" remark != null and remark!='' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="hospitalId != null">
                hospital_id = #{hospitalId,jdbcType=BIGINT},
            </if>
            <if test="classifyId != null">
                classify_id = #{classifyId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                `status` = #{status},
            </if>
            <if test="showOrder != null">
                show_order = #{showOrder,jdbcType=TINYINT},
            </if>
            <if test="departmentLimit != null">
                department_limit = #{departmentLimit,jdbcType=TINYINT},
            </if>
            <if test="doctorLimit != null">
                doctor_limit = #{doctorLimit,jdbcType=TINYINT},
            </if>
            <if test="closeType != null">
                close_type = #{closeType,jdbcType=TINYINT},
            </if>
            <if test="closeBeginDay != null">
                close_begin_day = #{closeBeginDay},
            </if>
            <if test="fillTimes != null">
                fill_times = #{fillTimes,jdbcType=INTEGER},
            </if>
            <if test="canRevise != null">
                can_revise = #{canRevise,jdbcType=TINYINT},
            </if>
            <if test="isScore != null">
                is_score = #{isScore,jdbcType=TINYINT},
            </if>
            <if test=" questionContent != null and questionContent!='' ">
                question_content = #{questionContent},
            </if>
            <if test="questionScoreValues != null">
                question_score_values = #{questionScoreValues, typeHandler=com.puree.followup.config.ScoreValueTypeHandler },
            </if>
            <if test=" 1==1 ">
                revision = revision+1 ,
            </if>
            <if test="createBy != null and createBy!='' ">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy!='' ">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="appletQrCode != null">
                applet_qr_code = #{appletQrCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}

    </update>

    <update id="updateClassifyId">
        update question set classify_id=#{newClassifyId} where classify_id=#{oldClassifyId}
    </update>

    <select id="findQuestionRelateFollowUp" resultType="com.puree.followup.question.domain.model.Question">

        SELECT
            q.id ,
            q.`name` ,
            q.classify_id ,
            q.remark ,
            q.`status` ,
            q.create_time
        from question as q
             inner join ( select question_id from follow_up_question_record where is_delete=0 group by question_id ) as t1
             on t1.question_id=q.id
        <where>
            <if test=" id!=null ">
                and q.id=#{id}
            </if>
            <if test=" hospitalId!=null ">
                and q.hospital_id=#{hospitalId}
            </if>
            <if test=" classifyId!=null ">
                and q.classify_id=#{classifyId}
            </if>
            <if test=" status!=null ">
                and q.`status`=#{status}
            </if>
            <if test=" name!=null and name!='' ">
                and q.`name` like concat('%', trim( #{name} ) , '%')
            </if>
            <if test=" isDelete != null ">
                and q.is_delete = #{isDelete}
            </if>
        </where>

        order by q.create_time desc

    </select>

    <select id="findQuestionNoRelateFollowUp" resultType="com.puree.followup.question.domain.model.Question">

        SELECT
            q.id ,
            q.`name` ,
            q.classify_id ,
            q.remark ,
            q.`status` ,
            q.create_time
        from question as q
             left join ( select question_id from follow_up_question_record where is_delete=0 group by question_id ) as t1
             on t1.question_id=q.id
        <where>
            <if test=" id!=null ">
                and q.id=#{id}
            </if>
            <if test=" hospitalId!=null ">
                and q.hospital_id=#{hospitalId}
            </if>
            <if test=" classifyId!=null ">
                and q.classify_id=#{classifyId}
            </if>
            <if test=" status!=null ">
                and q.`status`=#{status}
            </if>
            <if test=" name!=null and name!='' ">
                and q.`name` like concat('%', trim( #{name} ) , '%')
            </if>
            <if test=" isDelete != null ">
                and q.is_delete = #{isDelete}
            </if>
            <if test=" 1==1 ">
                and t1.question_id is null
            </if>
        </where>

        order by q.create_time desc

    </select>

    <update id="updateStatusByCloseType">

        update question
        set `status` = #{status}
        where
            `status` = 2 and close_type = #{closeType} and close_begin_day <![CDATA[<=]]>  #{now}

    </update>

    <select id="doctorViewQuestionList" resultType="java.lang.Long">

        SELECT
            t.id
        FROM
            (
                SELECT
                    id,
                    `status`,
                    create_time
                FROM
                    question
                WHERE
                    hospital_id = #{hospitalId}
                  AND is_delete = 0
                  AND department_limit = 0
                  AND doctor_limit = 0
                  AND `status` IN ( 1, 2 )
                  <if test=" questionName!=null and questionName!='' ">
                      AND `name` like concat('%', trim(#{questionName}), '%')
                  </if>

                UNION

                SELECT
                    q.id,
                    q.`status`,
                    q.create_time
                FROM
                    question q
                        INNER JOIN question_dept_doctor_record r ON q.id = r.question_id
                    WHERE
                        q.hospital_id = #{hospitalId} AND q.is_delete = 0 AND r.is_delete = 0
                        AND ( q.department_limit = 1 OR q.doctor_limit = 1 )
                        AND q.`status` IN ( 1, 2 )
                        AND (
                                r.doctor_id = #{doctorId}
                                OR
                                r.department_id in
                                <foreach collection="departmentIdList" open="("  separator=","  close=")"  item="item" >
                                    #{item}
                                </foreach>
                            )

                        <if test=" questionName!=null and questionName!='' ">
                            AND q.`name` like concat('%', trim(#{questionName}), '%')
                        </if>

            ) AS t
        ORDER BY
            `status` DESC,
            create_time DESC

    </select>
    
    
    
</mapper>