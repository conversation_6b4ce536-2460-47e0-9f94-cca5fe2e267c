<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.question.mapper.QuestionDeptDoctorRecordMapper">

    <resultMap id="BaseResultMap" type="com.puree.followup.question.domain.model.QuestionDeptDoctorRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="questionId" column="question_id" jdbcType="BIGINT"/>
            <result property="departmentIds"  column="department_ids" javaType="java.util.List"  typeHandler="com.puree.hospital.common.core.mybatis.typehandler.LongTypeHandler" />
            <result property="departmentId" column="department_id" jdbcType="BIGINT"/>
            <result property="doctorId" column="doctor_id" jdbcType="BIGINT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,question_id,department_ids,
        department_id,doctor_id,create_by,
        create_time,update_by,update_time,
        is_delete
    </sql>

    <select id="selectByCondition" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from question_dept_doctor_record
        <where>
            <if test=" id!=null ">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test=" questionId!=null ">
                and question_id = #{questionId}
            </if>
            <if test=" isDelete!=null ">
                and is_delete = #{isDelete}
            </if>
        </where>
    </select>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from question_dept_doctor_record
        where  id = #{id,jdbcType=BIGINT} 
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.question.domain.model.QuestionDeptDoctorRecord" useGeneratedKeys="true">
        insert into question_dept_doctor_record
        ( question_id,department_ids
        ,department_id,doctor_id,create_by
        ,create_time,update_by,update_time
        ,is_delete)
        values ( #{questionId,jdbcType=BIGINT},#{departmentIds,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.LongTypeHandler }
        ,#{departmentId,jdbcType=BIGINT},#{doctorId,jdbcType=BIGINT},#{createBy,jdbcType=VARCHAR}
        ,#{createTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR},#{updateTime,jdbcType=TIMESTAMP}
        ,#{isDelete,jdbcType=TINYINT})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.question.domain.model.QuestionDeptDoctorRecord" useGeneratedKeys="true">
        insert into question_dept_doctor_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="questionId != null">question_id,</if>
                <if test="departmentIds != null">department_ids,</if>
                <if test="departmentId != null">department_id,</if>
                <if test="doctorId != null">doctor_id,</if>
                <if test="createBy != null">create_by,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updateBy != null">update_by,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="isDelete != null">is_delete,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="questionId != null">#{questionId,jdbcType=BIGINT},</if>
                <if test="departmentIds != null">#{departmentIds, typeHandler=com.puree.hospital.common.core.mybatis.typehandler.LongTypeHandler },</if>
                <if test="departmentId != null">#{departmentId,jdbcType=BIGINT},</if>
                <if test="doctorId != null">#{doctorId,jdbcType=BIGINT},</if>
                <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.puree.followup.question.domain.model.QuestionDeptDoctorRecord">
        update question_dept_doctor_record
        <set>
                <if test="questionId != null">
                    question_id = #{questionId,jdbcType=BIGINT},
                </if>
                <if test="departmentIds != null">
                    department_ids = #{departmentIds, typeHandler=com.puree.hospital.common.core.mybatis.typehandler.LongTypeHandler },
                </if>
                <if test="departmentId != null">
                    department_id = #{departmentId,jdbcType=BIGINT},
                </if>
                <if test="doctorId != null">
                    doctor_id = #{doctorId,jdbcType=BIGINT},
                </if>
                <if test=" createBy != null and createBy!='' ">
                    create_by = #{createBy,jdbcType=VARCHAR},
                </if>
                <if test="createTime != null">
                    create_time = #{createTime,jdbcType=TIMESTAMP},
                </if>
                <if test=" updateBy != null and updateBy !='' ">
                    update_by = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateTime != null">
                    update_time = #{updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="isDelete != null">
                    is_delete = #{isDelete,jdbcType=TINYINT},
                </if>
        </set>
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <update id="updateByPrimaryKey" parameterType="com.puree.followup.question.domain.model.QuestionDeptDoctorRecord">
        update question_dept_doctor_record
        set 
            question_id =  #{questionId,jdbcType=BIGINT},
            department_ids =  #{departmentIds,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.LongTypeHandler },
            department_id =  #{departmentId,jdbcType=BIGINT},
            doctor_id =  #{doctorId,jdbcType=BIGINT},
            create_by =  #{createBy,jdbcType=VARCHAR},
            create_time =  #{createTime,jdbcType=TIMESTAMP},
            update_by =  #{updateBy,jdbcType=VARCHAR},
            update_time =  #{updateTime,jdbcType=TIMESTAMP},
            is_delete =  #{isDelete,jdbcType=TINYINT}
        where   id = #{id,jdbcType=BIGINT} 
    </update>

    <update id="updateByQuestion" parameterType="com.puree.followup.question.domain.model.QuestionDeptDoctorRecord">
        update question_dept_doctor_record
        <set>
            <if test="departmentIds != null">
                department_ids = #{departmentIds, typeHandler=com.puree.hospital.common.core.mybatis.typehandler.LongTypeHandler },
            </if>
            <if test="departmentId != null">
                department_id = #{departmentId,jdbcType=BIGINT},
            </if>
            <if test="doctorId != null">
                doctor_id = #{doctorId,jdbcType=BIGINT},
            </if>
            <if test=" createBy != null and createBy!='' ">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test=" updateBy != null and updateBy !='' ">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=TINYINT},
            </if>
        </set>
        where  question_id = #{questionId}
    </update>

</mapper>
