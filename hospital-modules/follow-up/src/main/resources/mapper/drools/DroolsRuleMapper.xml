<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.drools.mapper.DroolsRuleMapper">
    <resultMap type="com.puree.followup.domain.drools.DroolsRule" id="DroolsRuleMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="strRule" column="str_rule" jdbcType="LONGVARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="isAll" column="is_all" jdbcType="TINYINT"/>
    </resultMap>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="DroolsRuleMap">
        select
        id,str_rule,update_time,create_time,is_delete,is_all
        from drools_rule
        where id = #{id} and is_delete = 0
    </select>

    <!--分页查询指定行数据-->
    <select id="queryAllByLimit" resultMap="DroolsRuleMap">
        select
        id,str_rule,update_time,create_time,is_delete,is_all
        from drools_rule
        <where>
            is_delete = 0
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="isAll != null and isAll != ''">
                and is_all = #{isAll}
            </if>
        </where>
        limit #{pageable.offset}, #{pageable.pageSize}
    </select>

    <!--统计总行数-->
    <select id="count" resultType="java.lang.Long">
        select count(1)
        from drools_rule
        <where>
            is_delete = 0
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="strRule != null and strRule != ''">
                and str_rule = #{strRule}
            </if>
            <if test="updateTime != null and updateTime != ''">
                and update_time = #{updateTime}
            </if>
            <if test="createTime != null and createTime != ''">
                and create_time = #{createTime}
            </if>
            <if test="isDelete != null and isDelete != ''">
                and is_delete = #{isDelete}
            </if>
        </where>
    </select>

    <!--新增数据-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into drools_rule(str_rule,update_time,create_time,is_delete,is_all)
        values (#{strRule},#{updateTime},#{createTime},#{isDelete},#{isAll})
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into drools_rule(str_rule,update_time,create_time,is_delete,is_all)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.strRule},#{entity.updateTime},#{entity.createTime},#{entity.isDelete},#{entity.isAll})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into drools_rule(str_rule,update_time,create_time,is_delete,is_all)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.strRule},#{entity.updateTime},#{entity.createTime},#{entity.isDelete},#{entity.isAll})
        </foreach>
        on duplicate key update
        id=values(id),
        str_rule=values(str_rule),
        update_time=values(update_time),
        create_time=values(create_time),
        is_delete=values(is_delete),
        is_all=values(is_all)
    </insert>

    <!-- 更新数据 -->
    <update id="update">
        update drools_rule
        <set>
            <if test="strRule != null and strRule != ''">
                str_rule = #{strRule},
            </if>
            <if test="updateTime != null and updateTime != ''">
                update_time = #{updateTime},
            </if>
            <if test="isAll != null and isAll != ''">
                is_all = #{isAll},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        UPDATE drools_rule SET is_delete = 1  where id = #{id}
    </delete>
</mapper>