<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.PatientFollowUpRemarkMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.PatientFollowUpRemark">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="follow_up_record_id" jdbcType="BIGINT" property="followUpRecordId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>

  <sql id="Base_Column_List">
    id, follow_up_id, hospital_id, follow_up_record_id, user_id,patient_id, content, revision, create_by,
    create_time, update_by, update_time, is_delete
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from patient_follow_up_remark
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getLatestOne" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_follow_up_remark
    where follow_up_record_id = #{followUpRecordId} and is_delete = 0
    order by create_time desc
    limit 1
  </select>

  <select id="getList" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpRemark" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_follow_up_remark t1
    <where>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="followUpRecordId != null">
        and t1.follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="userId != null">
        and t1.user_id = #{userId}
      </if>
      <if test="patientId != null">
        and t1.patient_id = #{patientId}
      </if>
      <if test="revision != null">
        and t1.revision = #{revision}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    order by t1.create_time desc
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpRemark" useGeneratedKeys="true">
    insert into patient_follow_up_remark (follow_up_id, hospital_id, follow_up_record_id,
      user_id, patient_id, content, revision,
      create_by, create_time, update_by, 
      update_time, is_delete)
    values (#{followUpId,jdbcType=BIGINT}, #{hospitalId,jdbcType=BIGINT}, #{followUpRecordId,jdbcType=BIGINT},
      #{userId,jdbcType=BIGINT}, #{patientId,jdbcType=BIGINT},  #{content,jdbcType=VARCHAR}, #{revision,jdbcType=INTEGER},
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
  </insert>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpRemark">
    update patient_follow_up_remark
    <set>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="revision != null">
        revision = revision + 1,
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>

    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="revision != null">
        and revision = #{revision}
      </if>
    </where>

  </update>

</mapper>