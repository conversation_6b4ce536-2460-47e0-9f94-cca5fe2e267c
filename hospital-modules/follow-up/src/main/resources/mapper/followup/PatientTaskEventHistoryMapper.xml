<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.PatientTaskEventHistoryMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.PatientTaskEventHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="follow_up_record_id" jdbcType="BIGINT" property="followUpRecordId" />
    <result column="item_record_id" jdbcType="BIGINT" property="itemRecordId" />
    <result column="task_history_id" jdbcType="BIGINT" property="taskHistoryId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="event_id" jdbcType="BIGINT" property="eventId" />
    <result column="event_type" jdbcType="TINYINT" property="eventType" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="event_execute_time" jdbcType="TIMESTAMP" property="eventExecuteTime" />
    <result column="remind_time" jdbcType="TIMESTAMP" property="remindTime" />
    <result column="event_remind_status" jdbcType="TINYINT" property="eventRemindStatus" />
    <result column="event_finish_status" jdbcType="TINYINT" property="eventFinishStatus" />
    <result column="is_terminate_future" jdbcType="TINYINT" property="isTerminateFuture" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="events"  javaType="com.puree.followup.domain.followup.model.TaskEvent" property="events" typeHandler="com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler" />
  </resultMap>

  <resultMap id="BaseResultMap2" type="com.puree.followup.domain.followup.model.PatientTaskEventHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="follow_up_record_id" jdbcType="BIGINT" property="followUpRecordId" />
    <result column="item_record_id" jdbcType="BIGINT" property="itemRecordId" />
    <result column="task_history_id" jdbcType="BIGINT" property="taskHistoryId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="event_id" jdbcType="BIGINT" property="eventId" />
    <result column="event_type" jdbcType="TINYINT" property="eventType" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="event_execute_time" jdbcType="TIMESTAMP" property="eventExecuteTime" />
    <result column="remind_time" jdbcType="TIMESTAMP" property="remindTime" />
    <result column="event_remind_status" jdbcType="TINYINT" property="eventRemindStatus" />
    <result column="event_finish_status" jdbcType="TINYINT" property="eventFinishStatus" />
    <result column="is_terminate_future" jdbcType="TINYINT" property="isTerminateFuture" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="events"  javaType="com.puree.followup.domain.followup.model.TaskEvent" property="events" typeHandler="com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler" />
  </resultMap>

  <sql id="Base_Column_List">
    id, follow_up_id, follow_up_record_id, item_record_id, task_history_id,task_id,event_id,event_type,item_id, events, hospital_id, user_id, patient_id, event_execute_time,remind_time,event_remind_status,
    event_finish_status, is_terminate_future, create_by, create_time, update_by, update_time,is_delete
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from patient_task_event_history
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getEventStatistics" parameterType="java.lang.Long" resultType="com.puree.followup.domain.followup.model.EventStat">
    SELECT
    IFNULL(SUM(CASE WHEN event_finish_status = 0 THEN 1 ELSE 0 END), 0) AS iscomingEvents,
    IFNULL(SUM(CASE WHEN event_finish_status = 1 THEN 1 ELSE 0 END), 0) AS ongoingEvents,
    IFNULL(SUM(CASE WHEN event_finish_status = 2 THEN 1 ELSE 0 END), 0) AS finishedEvents,
    IFNULL(SUM(CASE WHEN event_finish_status = 3 THEN 1 ELSE 0 END), 0) AS hasExpiredEvents,
    IFNULL(SUM(CASE WHEN event_finish_status = 4 THEN 1 ELSE 0 END), 0) AS terminatedEvents
    from patient_task_event_history
    where is_delete = 0
    <if test="followUpId != null">
      and follow_up_id = #{followUpId,jdbcType=BIGINT}
    </if>
    <if test="followUpRecordId != null">
      and follow_up_record_id = #{followUpRecordId}
    </if>
    <if test="itemRecordId != null">
      and item_record_id = #{itemRecordId}
    </if>
    <if test="taskHistoryId != null">
      and task_history_id = #{taskHistoryId,jdbcType=BIGINT}
    </if>
    <if test="taskId != null">
      and task_id = #{taskId,jdbcType=BIGINT}
    </if>
    <if test="eventId != null">
      and event_id = #{eventId,jdbcType=BIGINT}
    </if>
    <if test="itemId != null">
      and item_id = #{itemId,jdbcType=BIGINT}
    </if>
    <if test="hospitalId != null">
      and hospital_id = #{hospitalId,jdbcType=BIGINT}
    </if>
    <if test="userId != null">
      and user_id = #{userId,jdbcType=BIGINT}
    </if>
    <if test="patientId != null">
      and patient_id = #{patientId,jdbcType=BIGINT}
    </if>
    <if test="eventRemindStatus != null">
      and event_remind_status = #{eventRemindStatus,jdbcType=TINYINT}
    </if>
    <if test="eventFinishStatus != null">
      and event_finish_status = #{eventFinishStatus,jdbcType=TINYINT},
    </if>
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory" useGeneratedKeys="true">
    insert into patient_task_event_history (follow_up_id, follow_up_record_id,item_record_id, task_history_id,task_id,event_id, event_type, item_id, hospital_id, user_id, patient_id,
      event_execute_time,remind_time,event_remind_status,
      event_finish_status, is_terminate_future, create_by, create_time,
      update_by, update_time, events, is_delete)
    values (#{followUpId},#{followUpRecordId},#{itemRecordId},#{taskHistoryId},#{taskId,jdbcType=BIGINT},
      #{eventId},#{eventType},#{itemId}, #{hospitalId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{patientId,jdbcType=BIGINT},
      #{eventExecuteTime,jdbcType=TIMESTAMP}, #{remindTime,jdbcType=TIMESTAMP}, #{eventRemindStatus,jdbcType=TINYINT},
      #{eventFinishStatus,jdbcType=TINYINT}, #{isTerminateFuture,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler}, #{isDelete,jdbcType=TINYINT})
  </insert>


  <select id="getList" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_task_event_history
    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="itemRecordId != null">
        and item_record_id = #{itemRecordId}
      </if>
      <if test="taskHistoryId != null">
        and task_history_id = #{taskHistoryId}
      </if>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="eventId != null">
        and event_id = #{eventId}
      </if>
      <if test="eventType != null">
        and event_type = #{eventType}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId}
      </if>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
      <if test="patientId != null">
        and patient_id = #{patientId}
      </if>
      <if test="remindTime != null">
        and date(remind_time) = #{remindTime,jdbcType=DATE}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId}
      </if>
      <if test="isTerminateFuture != null">
        and is_terminate_future = #{isTerminateFuture}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
    </where>
  </select>

  <select id="getListByIds" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory" resultType="com.puree.followup.domain.followup.model.PatientTaskEventHistory">
    select t1.id as id,
          t1.event_finish_status as eventFinishStatus,
          t1.task_history_id as taskHistoryId,
          t2.task_status as taskStatus,
          t1.task_id as taskId
    from patient_task_event_history t1 join patient_task_history t2 on t1.task_history_id = t2.id
    <where>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="eventHistoryIds != null and eventHistoryIds.size() > 0  ">
        and t1.id IN
        <foreach collection="eventHistoryIds" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getOne" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_task_event_history
    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="itemRecordId != null">
        and item_record_id = #{itemRecordId}
      </if>
      <if test="taskHistoryId != null">
        and task_history_id = #{taskHistoryId}
      </if>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="eventId != null">
        and event_id = #{eventId}
      </if>
      <if test="eventType != null">
        and event_type = #{eventType}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId}
      </if>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
      <if test="patientId != null">
        and patient_id = #{patientId}
      </if>
      <if test="remindTime != null">
        and date(remind_time) = #{remindTime,jdbcType=DATE}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId}
      </if>
      <if test="isTerminateFuture != null">
        and is_terminate_future = #{isTerminateFuture}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
    </where>
    limit 1
  </select>

  <select id="listByTaskHistoryId" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_task_event_history
    <where>
      is_delete = 0
      <if test="taskHistoryId != null">
        and task_history_id = #{taskHistoryId}
      </if>
      <if test="taskHistoryIds != null and taskHistoryIds.size() > 0  ">
        and task_history_id IN
        <foreach collection="taskHistoryIds" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="eventType != null">
        and event_type = #{eventType}
      </if>
      <if test="eventFinishStatus != null">
        and event_finish_status = #{eventFinishStatus}
      </if>
    </where>
  </select>


  <update id="updateEvent" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory">
    update patient_task_event_history
    <set>
      <if test="isTerminateFuture != null">
        is_terminate_future = #{isTerminateFuture},
      </if>
      <if test="eventFinishStatus != null">
        event_finish_status = #{eventFinishStatus,jdbcType=TINYINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>

    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="itemRecordId != null">
        and item_record_id = #{itemRecordId}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
      <if test="eventFinishStatusList != null and eventFinishStatusList.size() > 0  ">
        and event_finish_status IN
        <foreach collection="eventFinishStatusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </update>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory">
    update patient_task_event_history
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="followUpRecordId != null">
        follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="itemRecordId != null">
        item_record_id = #{itemRecordId,jdbcType=BIGINT},
      </if>
      <if test="taskHistoryId != null">
        task_history_id = #{taskHistoryId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="patientId != null">
        patient_id = #{patientId,jdbcType=BIGINT},
      </if>
      <if test="eventExecuteTime != null">
        event_execute_time = #{eventExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remindTime != null">
        remind_time = #{remindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventRemindStatus != null">
        event_remind_status = #{eventRemindStatus,jdbcType=TINYINT},
      </if>
      <if test="eventFinishStatus != null">
        event_finish_status = #{eventFinishStatus,jdbcType=TINYINT},
      </if>
      <if test="isTerminateFuture != null">
        is_terminate_future = #{isTerminateFuture},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="events != null">
        events = #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>

    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="eventFinishStatusList != null and eventFinishStatusList.size() > 0  ">
        and event_finish_status IN
        <foreach collection="eventFinishStatusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>

  </update>


  <update id="updateByIds" parameterType="com.puree.followup.domain.followup.model.PatientTaskEventHistory">
    update patient_task_event_history
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="followUpRecordId != null">
        follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="itemRecordId != null">
        item_record_id = #{itemRecordId,jdbcType=BIGINT},
      </if>
      <if test="taskHistoryId != null">
        task_history_id = #{taskHistoryId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="eventId != null">
        event_id = #{eventId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="patientId != null">
        patient_id = #{patientId,jdbcType=BIGINT},
      </if>
      <if test="eventExecuteTime != null">
        event_execute_time = #{eventExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remindTime != null">
        remind_time = #{remindTime,jdbcType=TIMESTAMP},
      </if>
      <if test="eventRemindStatus != null">
        event_remind_status = #{eventRemindStatus,jdbcType=TINYINT},
      </if>
      <if test="eventFinishStatus != null">
        event_finish_status = #{eventFinishStatus,jdbcType=TINYINT},
      </if>
      <if test="isTerminateFuture != null">
        is_terminate_future = #{isTerminateFuture},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="events != null">
        events = #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>

    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="ids != null and ids.size() > 0  ">
        and id IN
        <foreach collection="ids" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>

  </update>

  <select id="countPatientTaskEventHistory" resultType="int">

    select
    count(*)
    from patient_task_event_history
    <where>
      is_delete = 0
      <if test="followUpId != null">
        and follow_up_id = #{followUpId}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="itemRecordId != null">
        and item_record_id = #{itemRecordId}
      </if>
      <if test="taskHistoryId != null">
        and task_history_id = #{taskHistoryId}
      </if>
      <if test="taskId != null">
        and task_id = #{taskId}
      </if>
      <if test="eventId != null">
        and event_id = #{eventId}
      </if>
      <if test="eventType != null">
        and event_type = #{eventType}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId}
      </if>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
      <if test="patientId != null">
        and patient_id = #{patientId}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId}
      </if>

    </where>

  </select>

</mapper>