<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.FollowUpJoinRuleMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUpJoinRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="join_type" jdbcType="TINYINT" property="joinType" />
    <result column="join_switch" jdbcType="TINYINT" property="joinSwitch" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="rule" javaType="com.puree.followup.domain.followup.model.joinrule.JoinRule" property="rule" typeHandler="com.puree.followup.config.JoinRuleTypeHandler" />
  </resultMap>

  <sql id="Base_Column_List">
    id, follow_up_id, hospital_id, join_type, join_switch, rule_id, create_by, create_time, update_by,
    update_time,rule
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from follow_up_join_rule
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.FollowUpJoinRule" useGeneratedKeys="true">
    insert into follow_up_join_rule (follow_up_id, hospital_id, join_type,
      join_switch, rule_id, create_by, create_time,
      update_by, update_time, rule
      )
    values (#{followUpId,jdbcType=BIGINT}, #{hospitalId,jdbcType=BIGINT}, #{joinType,jdbcType=TINYINT}, 
      #{joinSwitch,jdbcType=TINYINT}, #{ruleId,jdbcType=BIGINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{rule, typeHandler=com.puree.followup.config.JoinRuleTypeHandler}
      )
  </insert>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into follow_up_join_rule (follow_up_id, hospital_id, join_type,
      join_switch, rule_id, create_by, create_time,
      update_by, update_time, rule)
    values
    <foreach collection="collect" item="entity" separator=",">
      (#{entity.followUpId,jdbcType=BIGINT}, #{entity.hospitalId,jdbcType=BIGINT}, #{entity.joinType,jdbcType=TINYINT},
      #{entity.joinSwitch,jdbcType=TINYINT}, #{entity.ruleId,jdbcType=BIGINT}, #{entity.createBy,jdbcType=VARCHAR}, #{entity.createTime,jdbcType=TIMESTAMP},
      #{entity.updateBy,jdbcType=VARCHAR}, #{entity.updateTime,jdbcType=TIMESTAMP}, #{entity.rule, typeHandler=com.puree.followup.config.JoinRuleTypeHandler})
    </foreach>
  </insert>

  <select id="getList" parameterType="com.puree.followup.domain.followup.model.FollowUpJoinRule" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from follow_up_join_rule t1
    <where>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="ruleId != null">
        and t1.rule_id = #{ruleId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="joinType != null">
        and t1.join_type = #{joinType}
      </if>
    </where>
    order by t1.join_type asc
  </select>

  <select id="getOne" parameterType="com.puree.followup.domain.followup.model.FollowUpJoinRule" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from follow_up_join_rule t1
    <where>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="ruleId != null">
        and t1.rule_id = #{ruleId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="joinType != null">
        and t1.join_type = #{joinType}
      </if>
      <if test="joinSwitchList != null and joinSwitchList.size() > 0  ">
        and t1.join_switch IN
        <foreach collection="joinSwitchList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
    limit 1
  </select>

  <select id="getSwitchOnList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from follow_up_join_rule t1
    where t1.join_switch > 0 and t1.follow_up_id = #{followUpId}
    order by t1.join_type asc
  </select>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.FollowUpJoinRule">
    update follow_up_join_rule
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="joinType != null">
        join_type = #{joinType,jdbcType=TINYINT},
      </if>
      <if test="joinSwitch != null">
        join_switch = #{joinSwitch,jdbcType=TINYINT},
      </if>

      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>

      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rule != null">
        rule = #{rule,typeHandler=com.puree.followup.config.JoinRuleTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>