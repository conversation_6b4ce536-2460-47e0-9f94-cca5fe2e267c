<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.FollowUpItemTaskMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUpItemTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="execute_time" jdbcType="VARCHAR" property="executeTime" />
    <result column="interval_type" jdbcType="TINYINT" property="intervalType" />
    <result column="intervals" jdbcType="INTEGER" property="intervals" />
    <result column="week_days" jdbcType="CHAR" property="weekDays" />
    <result column="events"  javaType="java.util.List" property="events" typeHandler="com.puree.followup.config.TaskEventTypeHandler" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>

  <sql id="Base_Column_List">
    id, follow_up_id, item_id,task_name, sort_num, hospital_id,
    execute_time, interval_type, intervals, week_days, events, create_by,
    create_time, revision, is_delete
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from follow_up_item_task
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getList" parameterType="com.puree.followup.domain.followup.model.FollowUpItemTask" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from follow_up_item_task t1
    <where>
      <if test="id != null">
        and t1.id = #{id}
      </if>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="itemId != null">
        and t1.item_id = #{itemId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    order by sort_num asc
  </select>

  <select id="getEventsListByIds" parameterType="com.puree.followup.domain.followup.model.FollowUpItemTask" resultMap="BaseResultMap">
    select t1.id, t1.events
    from follow_up_item_task t1
    <where>
      <if test="collect != null and collect.size() > 0  ">
        and t1.id IN
        <foreach collection="collect" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>

  <select id="getMaxIntervals" parameterType="com.puree.followup.domain.followup.model.FollowUpItemTask" resultType="int">
    select  IFNULL(max(t1.intervals), 0)
    from follow_up_item_task t1
    <where>
      <if test="id != null">
        and t1.id = #{id}
      </if>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="itemId != null">
        and t1.item_id = #{itemId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.FollowUpItemTask" useGeneratedKeys="true">
    insert into follow_up_item_task (follow_up_id, item_id, task_name,sort_num,
      hospital_id, execute_time, interval_type,
      intervals, week_days, events,
      create_by, create_time, revision, is_delete)
    values (#{followUpId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}, #{taskName,jdbcType=BIGINT}, #{sortNum,jdbcType=INTEGER},
      #{hospitalId,jdbcType=BIGINT}, #{executeTime,jdbcType=VARCHAR}, #{intervalType,jdbcType=TINYINT},
      #{intervals,jdbcType=INTEGER}, #{weekDays,jdbcType=CHAR}, #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{revision,jdbcType=INTEGER}, #{isDelete,jdbcType=TINYINT})
  </insert>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into follow_up_item_task (follow_up_id, item_id, task_name, sort_num,
    hospital_id, execute_time, interval_type,
    intervals, week_days, events,
    create_by, create_time, revision, is_delete)
    values
    <foreach collection="collect" item="entity" separator=",">
      (#{entity.followUpId,jdbcType=BIGINT}, #{entity.itemId,jdbcType=BIGINT}, #{entity.taskName,jdbcType=BIGINT}, #{entity.sortNum,jdbcType=INTEGER},
      #{entity.hospitalId,jdbcType=BIGINT}, #{entity.executeTime,jdbcType=VARCHAR}, #{entity.intervalType,jdbcType=TINYINT},
      #{entity.intervals,jdbcType=INTEGER}, #{entity.weekDays,jdbcType=CHAR}, #{entity.events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      #{entity.createBy,jdbcType=VARCHAR}, #{entity.createTime,jdbcType=TIMESTAMP}, #{entity.revision,jdbcType=INTEGER}, #{entity.isDelete,jdbcType=TINYINT})
    </foreach>
  </insert>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.FollowUpItemTask">
    update follow_up_item_task
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="sortNum != null">
        sort_num = #{sortNum,jdbcType=INTEGER},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="executeTime != null">
        execute_time = #{executeTime,jdbcType=VARCHAR},
      </if>
      <if test="intervalType != null">
        interval_type = #{intervalType,jdbcType=TINYINT},
      </if>
      <if test="intervals != null">
        intervals = #{intervals,jdbcType=INTEGER},
      </if>
      <if test="weekDays != null">
        week_days = #{weekDays,jdbcType=CHAR},
      </if>
      <if test="events != null">
        events = #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revision != null">
        revision = #{revision,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectTaskByExecuteTime"  resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />
    from follow_up_item_task
    where is_delete = #{isDelete} and
    timestampdiff( MINUTE,   CONCAT( DATE( #{now} ) , " " , execute_time ) ,  #{now} ) = 0

  </select>

</mapper>