<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.FollowUpItemMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUpItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
    <result column="execute_type" jdbcType="TINYINT" property="executeType" />
    <result column="begin_day" jdbcType="INTEGER" property="beginDay" />
    <result column="end_day" jdbcType="INTEGER" property="endDay" />
    <result column="remind_time_type" jdbcType="TINYINT" property="remindTimeType" />
    <result column="remind_time" jdbcType="VARCHAR" property="remindTime" />
    <result column="interval_type" jdbcType="TINYINT" property="intervalType" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>

  <sql id="Base_Column_List">
    id, follow_up_id, `name`, sort_num, execute_type, begin_day, end_day, remind_time_type, 
    remind_time, interval_type, hospital_id, revision, create_by, create_time,
    update_by, update_time, is_delete
  </sql>

  <sql id="Base_Column_List2">
    t1.id, t1.follow_up_id, t1.`name`, t1.sort_num, t1.execute_type, t1.begin_day, t1.end_day, t1.remind_time_type,
    t1.remind_time, t1.interval_type, t1.hospital_id, t1.revision, t1.create_by, t1.create_time,
    t1.update_by, t1.update_time, t1.is_delete
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from follow_up_item
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.FollowUpItem" useGeneratedKeys="true">
    insert into follow_up_item (follow_up_id, `name`, sort_num,
      execute_type, begin_day, end_day, 
      remind_time_type, remind_time, interval_type, hospital_id, revision,
      create_by, create_time, update_by, 
      update_time, is_delete
      )
    values (#{followUpId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{sortNum,jdbcType=INTEGER}, 
      #{executeType,jdbcType=TINYINT}, #{beginDay,jdbcType=INTEGER}, #{endDay,jdbcType=INTEGER}, 
      #{remindTimeType,jdbcType=TINYINT}, #{remindTime,jdbcType=VARCHAR}, #{intervalType,jdbcType=TINYINT},
       #{hospitalId,jdbcType=BIGINT}, #{revision,jdbcType=INTEGER},
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}
      )
  </insert>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into follow_up_item (follow_up_id, `name`, sort_num,
    execute_type, begin_day, end_day, interval_type, hospital_id, revision,
    create_by, create_time, update_by,
    update_time, is_delete)
    values
    <foreach collection="collect" item="entity" separator=",">
      (#{entity.followUpId,jdbcType=BIGINT}, #{entity.name,jdbcType=VARCHAR}, #{entity.sortNum,jdbcType=INTEGER},
      #{entity.executeType,jdbcType=TINYINT}, #{entity.beginDay,jdbcType=INTEGER}, #{entity.endDay,jdbcType=INTEGER}, #{entity.intervalType,jdbcType=TINYINT},
      #{entity.hospitalId,jdbcType=BIGINT}, #{entity.revision,jdbcType=INTEGER},
      #{entity.createBy,jdbcType=VARCHAR}, #{entity.createTime,jdbcType=TIMESTAMP}, #{entity.updateBy,jdbcType=VARCHAR},
      #{entity.updateTime,jdbcType=TIMESTAMP}, #{entity.isDelete,jdbcType=TINYINT})
    </foreach>
  </insert>


  <select id="getList" parameterType="com.puree.followup.domain.followup.model.FollowUpItem" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from follow_up_item t1
    <where>
      <if test="id != null">
        and t1.id = #{id}
      </if>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>

      <if test="executeTypes != null and executeTypes.size() > 0  ">
        and t1.execute_type IN
        <foreach collection="executeTypes" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>

      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    order by t1.sort_num asc
  </select>

  <select id="getJoinedList" parameterType="com.puree.followup.domain.followup.model.FollowUpItem" resultMap="BaseResultMap">
    select distinct
    <include refid="Base_Column_List2" />
    from follow_up_item t1 join patient_follow_up_item_record t2 on t1.id = t2.item_id
    <where>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="followUpRecordId != null">
        and t2.follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    order by t1.sort_num asc
  </select>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.FollowUpItem">
    update follow_up_item
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sortNum != null">
        sort_num = #{sortNum,jdbcType=INTEGER},
      </if>
      <if test="executeType != null">
        execute_type = #{executeType,jdbcType=TINYINT},
      </if>
      <if test="beginDay != null">
        begin_day = #{beginDay,jdbcType=INTEGER},
      </if>
      <if test="endDay != null">
        end_day = #{endDay,jdbcType=INTEGER},
      </if>
      <if test="remindTimeType != null">
        remind_time_type = #{remindTimeType,jdbcType=TINYINT},
      </if>
      <if test="remindTime != null">
        remind_time = #{remindTime,jdbcType=VARCHAR},
      </if>
      <if test="intervalType != null">
        interval_type = #{intervalType,jdbcType=TINYINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="revision != null">
        revision = #{revision,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectItemByFollowUpId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from follow_up_item where follow_up_id=#{followUpId}
  </select>

  <select id="getItemNamesByIds" resultMap="BaseResultMap">
    select id, name
    from follow_up_item t1
    <where>
      <if test="collect != null and collect.size() > 0  ">
        and t1.id IN
        <foreach collection="collect" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>


</mapper>