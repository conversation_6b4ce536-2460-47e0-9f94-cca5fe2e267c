<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.FollowUpItemWarnMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUpItemWarn">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="follow_up_record_id" jdbcType="BIGINT" property="followUpRecordId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="item_record_id" jdbcType="BIGINT" property="itemRecordId" />
    <result column="executor_id" jdbcType="BIGINT" property="executorId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="questionnaire_id" jdbcType="BIGINT" property="questionnaireId" />
    <result column="expire_times" jdbcType="INTEGER" property="expireTimes" />
    <result column="warn_type" jdbcType="TINYINT" property="warnType" />
    <result column="warn_status" jdbcType="TINYINT" property="warnStatus" />
    <result column="warn_remark" jdbcType="VARCHAR" property="warnRemark" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="is_delete" jdbcType="BIT" property="isDelete" />
    <result column="events" javaType="java.util.List" property="events" typeHandler="com.puree.followup.config.TaskEventTypeHandler" />
    <result column="conditions" javaType="java.util.List" property="conditions" typeHandler="com.puree.followup.config.WarnConditionHandler" />
  </resultMap>

  <sql id="Base_Column_List">
    id, hospital_id, user_id, patient_id, follow_up_id, follow_up_record_id, item_id, item_record_id, executor_id,
    task_id, task_name, questionnaire_id, expire_times, warn_type, warn_status, warn_remark, create_by,
    create_time, revision, is_delete, events, conditions
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from follow_up_item_warn
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.FollowUpItemWarn" useGeneratedKeys="true">
    insert into follow_up_item_warn (hospital_id, user_id, patient_id, follow_up_id, follow_up_record_id,
      item_id, item_record_id, executor_id,
      task_id, task_name, questionnaire_id, expire_times,
      warn_type, warn_status, warn_remark,
      create_by, revision,
      is_delete, events, conditions)
    values (#{hospitalId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT}, #{patientId,jdbcType=BIGINT}, #{followUpId,jdbcType=BIGINT}, #{followUpRecordId,jdbcType=BIGINT},
        #{itemId,jdbcType=BIGINT}, #{itemRecordId,jdbcType=BIGINT}, #{executorId,jdbcType=BIGINT},
        #{taskId,jdbcType=BIGINT}, #{taskName,jdbcType=VARCHAR}, #{questionnaireId,jdbcType=BIGINT}, #{expireTimes,jdbcType=INTEGER},
        #{warnType,jdbcType=TINYINT}, #{warnStatus,jdbcType=TINYINT}, #{warnRemark,jdbcType=VARCHAR},
        #{createBy,jdbcType=VARCHAR}, #{revision,jdbcType=INTEGER},
        #{isDelete,jdbcType=BIT}, #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
        #{conditions,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler}
      )
  </insert>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.FollowUpItemWarn">
    update follow_up_item_warn
    <set>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="followUpRecordId != null">
        follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="itemRecordId != null">
        item_record_id = #{itemRecordId,jdbcType=BIGINT},
      </if>
      <if test="executorId != null">
        executor_id = #{executorId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="expireTimes != null">
        expire_times = #{expireTimes,jdbcType=INTEGER},
      </if>
      <if test="warnType != null">
        warn_type = #{warnType,jdbcType=TINYINT},
      </if>
      <if test="warnStatus != null">
        warn_status = #{warnStatus,jdbcType=TINYINT},
      </if>
      <if test="warnRemark != null">
        warn_remark = #{warnRemark,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revision != null">
        revision = #{revision,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=BIT},
      </if>
      <if test="events != null">
        events = #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      </if>
      <if test="conditions != null">
        conditions = #{conditions,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getList" parameterType="com.puree.followup.domain.followup.model.FollowUpItemWarn" resultMap="BaseResultMap">
    select distinct
    <include refid="Base_Column_List" />
    from follow_up_item_warn t1
    <if test="eventType != null and eventType != 0">
      join JSON_TABLE(events, '$[*]' COLUMNS (
      jtId VARCHAR(50) PATH '$.id',
      eventType int PATH '$.eventType'
      )
      ) AS jt on jt.eventType = #{eventType}
    </if>
    <where>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId,jdbcType=BIGINT}
      </if>
      <if test="userId != null">
        and t1.user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="patientId != null">
        and t1.patient_id = #{patientId,jdbcType=BIGINT}
      </if>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId,jdbcType=BIGINT}
      </if>
      <if test="followUpRecordId != null">
        and t1.follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT}
      </if>
      <if test="itemId != null">
        and t1.item_id = #{itemId,jdbcType=BIGINT}
      </if>
      <if test="itemRecordId != null">
        and t1.item_record_id = #{itemRecordId,jdbcType=BIGINT}
      </if>
      <if test="executorId != null">
        and t1.executor_id = #{executorId,jdbcType=BIGINT}
      </if>
      <if test="taskId != null">
        and t1.task_id = #{taskId,jdbcType=BIGINT}
      </if>
      <if test="taskName != null">
        and t1.task_name = #{taskName,jdbcType=VARCHAR}
      </if>
      <if test="expireTimes != null">
        and t1.expire_times = #{expireTimes,jdbcType=INTEGER}
      </if>
      <if test="warnType != null">
        and t1.warn_type = #{warnType,jdbcType=TINYINT}
      </if>
      <if test="warnStatus != null">
        and t1.warn_status = #{warnStatus,jdbcType=TINYINT}
      </if>
      <if test="warnRemark != null">
        and t1.warn_remark = #{warnRemark,jdbcType=VARCHAR}
      </if>
      <if test="createBy != null">
        and t1.create_by = #{createBy,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and t1.create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="revision != null">
        and t1.revision = #{revision,jdbcType=INTEGER}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete,jdbcType=BIT}
      </if>
      <if test="beginDate != null">
        and t1.create_time <![CDATA[>=]]> #{beginDate}
      </if>
      <if test="endDate != null">
        and t1.create_time <![CDATA[<=]]> #{endDate}
      </if>
      <if test="eventType != null and eventType == 0">
        and (t1.events is null or JSON_LENGTH(t1.events) = 0)
      </if>
      order by t1.create_time desc
    </where>
  </select>

  <select id="getListCount" parameterType="com.puree.followup.domain.followup.model.FollowUpItemWarn" resultType="java.lang.Integer">
    select count(1)
    from follow_up_item_warn
    <where>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId,jdbcType=BIGINT}
      </if>
      <if test="userId != null">
        and user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="patientId != null">
        and patient_id = #{patientId,jdbcType=BIGINT}
      </if>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId,jdbcType=BIGINT}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId,jdbcType=BIGINT}
      </if>
      <if test="itemRecordId != null">
        and item_record_id = #{itemRecordId,jdbcType=BIGINT}
      </if>
      <if test="executorId != null">
        and executor_id = #{executorId,jdbcType=BIGINT}
      </if>
      <if test="taskId != null">
        and task_id = #{taskId,jdbcType=BIGINT}
      </if>
      <if test="taskName != null">
        and task_name = #{taskName,jdbcType=VARCHAR}
      </if>
      <if test="expireTimes != null">
        and expire_times = #{expireTimes,jdbcType=INTEGER}
      </if>
      <if test="warnType != null">
        and warn_type = #{warnType,jdbcType=TINYINT}
      </if>
      <if test="warnStatus != null">
        and warn_status = #{warnStatus,jdbcType=TINYINT}
      </if>
      <if test="warnRemark != null">
        and warn_remark = #{warnRemark,jdbcType=VARCHAR}
      </if>
      <if test="createBy != null">
        and create_by = #{createBy,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="revision != null">
        and revision = #{revision,jdbcType=INTEGER}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete,jdbcType=BIT}
      </if>
      <if test="beginDate != null">
        and create_time <![CDATA[>=]]> #{beginDate}
      </if>
      <if test="endDate != null">
        and create_time <![CDATA[<=]]> #{endDate}
      </if>
    </where>
  </select>

  <update id="update" parameterType="com.puree.followup.domain.followup.model.FollowUpItemWarn">
    update follow_up_item_warn
    set hospital_id = #{hospitalId,jdbcType=BIGINT},
      follow_up_id = #{followUpId,jdbcType=BIGINT},
      follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      item_record_id = #{itemRecordId,jdbcType=BIGINT},
      executor_id = #{executorId,jdbcType=BIGINT},
      task_id = #{taskId,jdbcType=BIGINT},
      task_name = #{taskName,jdbcType=VARCHAR},
      expire_times = #{expireTimes,jdbcType=INTEGER},
      warn_type = #{warnType,jdbcType=TINYINT},
      warn_status = #{warnStatus,jdbcType=TINYINT},
      warn_remark = #{warnRemark,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      revision = #{revision,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=BIT},
      events = #{events,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      conditions = #{conditions,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>