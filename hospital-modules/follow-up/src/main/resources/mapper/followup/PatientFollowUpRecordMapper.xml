<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.PatientFollowUpRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="join_type" jdbcType="TINYINT" property="joinType" />
    <result column="invite_time" jdbcType="TIMESTAMP" property="inviteTime" />
    <result column="join_time" jdbcType="TIMESTAMP" property="joinTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="terminator_type" jdbcType="TINYINT" property="terminatorType" />
    <result column="terminator" jdbcType="VARCHAR" property="terminator" />
    <result column="doctor_id" jdbcType="BIGINT" property="doctorId" />
    <result column="dept_id" jdbcType="BIGINT" property="deptId" />
    <result column="join_status" jdbcType="TINYINT" property="joinStatus" />
    <result column="join_reason" jdbcType="VARCHAR" property="joinReason" />
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
    <result column="group_id" jdbcType="BIGINT" property="groupId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
  </resultMap>
  <sql id="Base_Column_List">
    id, follow_up_id, hospital_id, user_id,patient_id, join_type, invite_time, join_time, end_time,
    terminator_type, terminator, doctor_id, dept_id, join_status, join_reason, source_type, group_id, create_by, order_no,
    create_time, update_by, update_time, is_delete, phone_num
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from patient_follow_up_record
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getPatientStatistics" resultType="com.puree.followup.domain.followup.model.PatientStat">
    SELECT
      IFNULL(SUM(CASE WHEN join_status = 1 THEN 1 ELSE 0 END), 0) AS ongoingPatients,
      IFNULL(SUM(CASE WHEN join_status = 2 THEN 1 ELSE 0 END), 0) AS finishedPatients,
      IFNULL(SUM(CASE WHEN join_status = 3 THEN 1 ELSE 0 END), 0) AS terminatedPatients,
      <choose>
        <when test="todayStr != null">
          IFNULL(SUM(CASE WHEN DATE(join_time) = #{todayStr} THEN 1 ELSE 0 END), 0) AS todayNewPatients
        </when>
        <otherwise>
          0 AS todayNewPatients
        </otherwise>
      </choose>
    from patient_follow_up_record
    where is_delete = 0 and follow_up_id = #{followUpId} and hospital_id = #{hospitalId}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpRecord" useGeneratedKeys="true">
    insert into patient_follow_up_record (follow_up_id, hospital_id, user_id, patient_id,
      join_type, invite_time, join_time, 
      end_time, terminator_type, terminator, 
      doctor_id, dept_id, join_status, 
      join_reason, source_type, group_id, order_no, create_by, create_time,
      update_by, update_time, is_delete, phone_num
      )
    values (#{followUpId,jdbcType=BIGINT}, #{hospitalId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},  #{patientId,jdbcType=BIGINT},
      #{joinType,jdbcType=TINYINT}, #{inviteTime,jdbcType=TIMESTAMP}, #{joinTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{terminatorType,jdbcType=TINYINT}, #{terminator,jdbcType=VARCHAR}, 
      #{doctorId,jdbcType=BIGINT}, #{deptId,jdbcType=BIGINT}, #{joinStatus,jdbcType=TINYINT}, 
      #{joinReason,jdbcType=VARCHAR}, #{sourceType,jdbcType=TINYINT}, #{groupId,jdbcType=BIGINT}, #{orderNo,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT},  #{phoneNum}
      )
  </insert>


  <select id="getList" parameterType="com.puree.followup.domain.followup.query.PatientFollowUpRecordQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_follow_up_record t1
    <where>
      <if test="id != null">
        and t1.id = #{id}
      </if>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="userId != null">
        and t1.user_id = #{userId}
      </if>
      <if test="patientId != null">
        and t1.patient_id = #{patientId}
      </if>
      <if test="joinType != null">
        and t1.join_type = #{joinType}
      </if>
      <if test="terminatorType != null">
        and t1.terminator_type = #{terminatorType}
      </if>
      <if test="doctorId != null">
        and t1.doctor_id = #{doctorId}
      </if>
      <if test="deptId != null">
        and t1.dept_id = #{deptId}
      </if>
      <if test="joinStatus != null">
        and t1.join_status = #{joinStatus}
      </if>
      <if test="sourceType != null">
        and t1.source_type = #{sourceType}
      </if>
      <if test="groupId != null">
        and t1.group_id = #{groupId}
      </if>
      <if test="orderNo != null">
        and t1.order_no = #{orderNo}
      </if>
      <if test="statusList != null and statusList.size() > 0  ">
        and t1.join_status IN
        <foreach collection="statusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
  </select>

  <select id="getPatientList" parameterType="com.puree.followup.domain.followup.dto.FollowUpPatientQueryDTO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_follow_up_record t1
    <where>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>

      <if test="patientIds != null and patientIds.size() > 0  ">
        and t1.patient_id IN
        <foreach collection="patientIds" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="joinType != null">
        and t1.join_type = #{joinType}
      </if>
      <if test="joinStatus != null">
        and t1.join_status = #{joinStatus}
      </if>
      <if test="sourceType != null">
        and t1.source_type = #{sourceType}
      </if>
      <if test="groupId != null">
        and t1.group_id = #{groupId}
      </if>
      <if test="joinBeginTime != null ">
        and join_time <![CDATA[>=]]> #{joinBeginTime}
      </if>
      <if test="joinEndTime != null ">
        and join_time <![CDATA[<=]]> #{joinEndTime}
      </if>
      <if test="finishedBeginTime != null ">
        and end_time <![CDATA[>=]]> #{finishedBeginTime}
      </if>
      <if test="finishedEndTime != null ">
        and end_time <![CDATA[<=]]> #{finishedEndTime}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    order by t1.create_time desc
  </select>

  <select id="hasJoinedFollowUp" parameterType="com.puree.followup.domain.followup.dto.FollowUpPatientQueryDTO" resultType="com.puree.followup.domain.followup.vo.PatientFollowUpListVO">
    select t1.id as id, t1.follow_up_id as followUpId, t2.name as name, t2.expire_type as expireType, t2.expire_day as expireDay, t1.join_time as joinTime
    from patient_follow_up_record t1 join follow_up t2 on t1.follow_up_id = t2.id and t1.is_delete = 0
    <where>
      t1.join_status in (1,2,3)
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="userId != null">
        and t1.user_id = #{userId}
      </if>
      <if test="patientId != null">
        and t1.patient_id = #{patientId}
      </if>
    </where>
    order by t1.join_time desc
  </select>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpRecord">
    update patient_follow_up_record
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="patientId != null">
        patient_id = #{patientId,jdbcType=BIGINT},
      </if>
      <if test="joinType != null">
        join_type = #{joinType,jdbcType=TINYINT},
      </if>
      <if test="inviteTime != null">
        invite_time = #{inviteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="joinTime != null">
        join_time = #{joinTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="terminatorType != null">
        terminator_type = #{terminatorType,jdbcType=TINYINT},
      </if>
      <if test="terminator != null">
        terminator = #{terminator,jdbcType=VARCHAR},
      </if>
      <if test="doctorId != null">
        doctor_id = #{doctorId,jdbcType=BIGINT},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=BIGINT},
      </if>
      <if test="joinStatus != null">
        join_status = #{joinStatus,jdbcType=TINYINT},
      </if>
      <if test="joinReason != null">
        join_reason = #{joinReason,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=TINYINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=BIGINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <update id="updateRecord" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpRecord">
    update patient_follow_up_record
    <set>
      <if test="joinStatus != null">
        join_status = #{joinStatus,jdbcType=TINYINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId}
      </if>
      <if test="joinStatusList != null and joinStatusList.size() > 0  ">
        and join_status IN
        <foreach collection="joinStatusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
    </where>
  </update>


  <select id="selectPatientFollowUpByFollowUpUser" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_follow_up_record
    where  hospital_id = #{hospitalId} and user_id = #{userId} and patient_id = #{patientId} and follow_up_id = #{followUpId}
    and join_status in
    <foreach item="item" collection="joinStatusList" open="(" close=")" separator=",">
      #{item}
    </foreach>
    limit 1
  </select>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into patient_follow_up_record (follow_up_id, hospital_id, user_id, patient_id,
        join_type, invite_time, join_time,
        end_time, terminator_type, terminator,
        doctor_id, dept_id, join_status,
        join_reason, source_type, group_id, order_no, create_by, create_time,
        update_by, update_time, is_delete, phone_num)
    values
    <foreach collection="collect" item="entity" separator=",">
      (#{entity.followUpId,jdbcType=BIGINT}, #{entity.hospitalId,jdbcType=BIGINT}, #{entity.userId,jdbcType=BIGINT},  #{entity.patientId,jdbcType=BIGINT},
      #{entity.joinType,jdbcType=TINYINT}, #{entity.inviteTime,jdbcType=TIMESTAMP}, #{entity.joinTime,jdbcType=TIMESTAMP},
      #{entity.endTime,jdbcType=TIMESTAMP}, #{entity.terminatorType,jdbcType=TINYINT}, #{entity.terminator,jdbcType=VARCHAR},
      #{entity.doctorId,jdbcType=BIGINT}, #{entity.deptId,jdbcType=BIGINT}, #{entity.joinStatus,jdbcType=TINYINT},
      #{entity.joinReason,jdbcType=VARCHAR}, #{entity.sourceType,jdbcType=TINYINT}, #{entity.groupId,jdbcType=BIGINT}, #{entity.orderNo,jdbcType=VARCHAR}, #{entity.createBy,jdbcType=VARCHAR},
      #{entity.createTime,jdbcType=TIMESTAMP}, #{entity.updateBy,jdbcType=VARCHAR}, #{entity.updateTime,jdbcType=TIMESTAMP}, #{entity.isDelete,jdbcType=TINYINT}, #{entity.phoneNum})
    </foreach>
  </insert>

</mapper>