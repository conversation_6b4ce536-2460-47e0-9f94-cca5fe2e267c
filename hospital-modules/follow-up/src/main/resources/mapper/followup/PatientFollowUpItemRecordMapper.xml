<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="follow_up_record_id" jdbcType="BIGINT" property="followUpRecordId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="begin_day" jdbcType="DATE" property="beginDay" />
    <result column="end_day" jdbcType="DATE" property="endDay" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="terminator_type" jdbcType="TINYINT" property="terminatorType" />
    <result column="terminator" jdbcType="VARCHAR" property="terminator" />
    <result column="item_status" jdbcType="TINYINT" property="itemStatus" />
    <result column="first_day_of_join_week" jdbcType="TIMESTAMP" property="firstDayOfJoinWeek" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
    <result column="max_end_day" jdbcType="DATE" property="maxEndDay" />
    <result column="max_begin_day" jdbcType="DATE" property="maxBeginDay" />

  </resultMap>
  <sql id="Base_Column_List">
    id, follow_up_id, follow_up_record_id, item_id, hospital_id, user_id,patient_id, begin_day, end_day, end_date, terminator_type,
    terminator, item_status, first_day_of_join_week, revision, create_by, create_time, update_by, update_time,
    is_delete
  </sql>
  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from patient_follow_up_item_record
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getList" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_follow_up_item_record
    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId,jdbcType=BIGINT}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId,jdbcType=BIGINT}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId,jdbcType=BIGINT}
      </if>
      <if test="userId != null">
        and user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="patientId != null">
        and patient_id = #{patientId,jdbcType=BIGINT}
      </if>
      <if test="terminatorType != null">
        and terminator_type = #{terminatorType,jdbcType=TINYINT}
      </if>
      <if test="terminator != null">
        and terminator = #{terminator,jdbcType=VARCHAR}
      </if>
      <if test="itemStatus != null">
        and item_status = #{itemStatus,jdbcType=TINYINT}
      </if>
      <if test="itemStatusList != null and itemStatusList.size() > 0  ">
        and item_status IN
        <foreach collection="itemStatusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="firstDayOfJoinWeek != null">
        and first_day_of_join_week = #{firstDayOfJoinWeek,jdbcType=TIMESTAMP}
      </if>
      <if test="revision != null">
        and revision = #{revision,jdbcType=INTEGER}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
    </where>

  </select>

  <select id="getListWithItemName" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord" resultMap="BaseResultMap">
    select
      t1.id, t1.follow_up_id, t1.follow_up_record_id, t1.item_id, t1.hospital_id, t1.user_id, t1.patient_id,
      t1.begin_day, t1.end_day, t1.end_date, t1.item_status, t1.first_day_of_join_week, t1.is_delete, t2.name as item_name, t2.sort_num as sort_num
    from patient_follow_up_item_record t1 join follow_up_item t2 on t1.item_id = t2.id and t2.is_delete = 0
    <where>
      t1.is_delete = 0
      <if test="followUpRecordId != null">
        and t1.follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT}
      </if>

      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId,jdbcType=BIGINT}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId,jdbcType=BIGINT}
      </if>
      <if test="userId != null">
        and t1.user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="patientId != null">
        and t1.patient_id = #{patientId,jdbcType=BIGINT}
      </if>

      <if test="itemId != null">
        and t1.item_id = #{itemId}
      </if>
    </where>
    order by t2.sort_num asc
  </select>

  <select id="getMaxBeginDayAndEndDay" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord" resultMap="BaseResultMap">
    select MIN(t1.begin_day) AS begin_day,
        CASE
            WHEN SUM(CASE WHEN t1.end_day IS NULL THEN 1 ELSE 0 END) > 0 THEN NULL
            ELSE MAX(t1.end_day)
        END AS end_day,
        MAX(t1.end_day) AS max_end_day,
        MAX(t1.begin_day) AS max_begin_day
    from patient_follow_up_item_record t1
    <where>
      t1.is_delete = 0
      <if test="followUpRecordId != null">
        and t1.follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT}
      </if>

      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId,jdbcType=BIGINT}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId,jdbcType=BIGINT}
      </if>
      <if test="userId != null">
        and t1.user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="patientId != null">
        and t1.patient_id = #{patientId,jdbcType=BIGINT}
      </if>

      <if test="itemId != null">
        and t1.item_id = #{itemId}
      </if>
    </where>
  </select>


  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord" useGeneratedKeys="true">
    insert into patient_follow_up_item_record (follow_up_id, follow_up_record_id, item_id, hospital_id,
      user_id, patient_id, begin_day, end_day, end_date,
      terminator_type, terminator, item_status, first_day_of_join_week,
      revision, create_by, create_time, 
      update_by, update_time, is_delete
      )
    values (#{followUpId,jdbcType=BIGINT}, #{followUpRecordId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}, #{hospitalId,jdbcType=BIGINT},
      #{userId,jdbcType=BIGINT}, #{patientId,jdbcType=BIGINT},  #{beginDay,jdbcType=DATE}, #{endDay,jdbcType=DATE}, #{endDate,jdbcType=TIMESTAMP},
      #{terminatorType,jdbcType=TINYINT}, #{terminator,jdbcType=VARCHAR}, #{itemStatus,jdbcType=TINYINT}, #{firstDayOfJoinWeek,jdbcType=TIMESTAMP},
      #{revision,jdbcType=INTEGER}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}
      )
  </insert>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into patient_follow_up_item_record (follow_up_id, follow_up_record_id, item_id, hospital_id,
    user_id, patient_id, begin_day, end_day, end_date, terminator_type, terminator, item_status, first_day_of_join_week,
    revision, create_by, create_time,update_by, update_time, is_delete)
    values
    <foreach collection="collect" item="entity" separator=",">
      (#{entity.followUpId,jdbcType=BIGINT}, #{entity.followUpRecordId,jdbcType=BIGINT}, #{entity.itemId,jdbcType=BIGINT}, #{entity.hospitalId,jdbcType=BIGINT},
      #{entity.userId,jdbcType=BIGINT}, #{entity.patientId,jdbcType=BIGINT},  #{entity.beginDay,jdbcType=DATE}, #{entity.endDay,jdbcType=DATE}, #{entity.endDate,jdbcType=TIMESTAMP},
      #{entity.terminatorType,jdbcType=TINYINT}, #{entity.terminator,jdbcType=VARCHAR}, #{entity.itemStatus,jdbcType=TINYINT}, #{entity.firstDayOfJoinWeek,jdbcType=TIMESTAMP},
      #{entity.revision,jdbcType=INTEGER}, #{entity.createBy,jdbcType=VARCHAR}, #{entity.createTime,jdbcType=TIMESTAMP},
      #{entity.updateBy,jdbcType=VARCHAR}, #{entity.updateTime,jdbcType=TIMESTAMP}, #{entity.isDelete,jdbcType=TINYINT})
    </foreach>
  </insert>

  <update id="updateItemRecord" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord">
    update patient_follow_up_item_record
    <set>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="terminatorType != null">
        terminator_type = #{terminatorType,jdbcType=TINYINT},
      </if>
      <if test="terminator != null">
        terminator = #{terminator,jdbcType=VARCHAR},
      </if>
      <if test="itemStatus != null">
        item_status = #{itemStatus,jdbcType=TINYINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
      <if test="itemStatusList != null and itemStatusList.size() > 0  ">
        and item_status IN
        <foreach collection="itemStatusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>

  </update>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord">
    update patient_follow_up_item_record
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="followUpRecordId != null">
        follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="patientId != null">
        patient_id = #{patientId,jdbcType=BIGINT},
      </if>
      <if test="beginDay != null">
        begin_day = #{beginDay,jdbcType=DATE},
      </if>
      <if test="endDay != null">
        end_day = #{endDay,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="terminatorType != null">
        terminator_type = #{terminatorType,jdbcType=TINYINT},
      </if>
      <if test="terminator != null">
        terminator = #{terminator,jdbcType=VARCHAR},
      </if>
      <if test="itemStatus != null">
        item_status = #{itemStatus,jdbcType=TINYINT},
      </if>
      <if test="firstDayOfJoinWeek != null">
        first_day_of_join_week = #{firstDayOfJoinWeek,jdbcType=TIMESTAMP},
      </if>
      <if test="revision != null">
        revision = #{revision,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectPatientItem" resultMap="BaseResultMap">

    select <include refid="Base_Column_List" />
    from patient_follow_up_item_record
    where is_delete = #{isDelete} and
    hospital_id = #{hospitalId} and follow_up_id = #{followUpId} and item_id = #{itemId} and item_status=1
    and begin_day <![CDATA[ <= ]]> #{now,jdbcType=DATE}
    and ( ( end_day is not null and end_day <![CDATA[ >= ]]> #{now,jdbcType=DATE} ) or end_day is null )

  </select>

  <update id="updateAllById" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord">
    update patient_follow_up_item_record
    set follow_up_id = #{followUpId,jdbcType=BIGINT},
      follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT},
      hospital_id = #{hospitalId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      patient_id = #{patientId,jdbcType=BIGINT},
      begin_day = #{beginDay,jdbcType=DATE},
      end_day = #{endDay,jdbcType=DATE},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      terminator_type = #{terminatorType,jdbcType=TINYINT},
      terminator = #{terminator,jdbcType=VARCHAR},
      item_status = #{itemStatus,jdbcType=TINYINT},
      first_day_of_join_week = #{firstDayOfJoinWeek,jdbcType=TIMESTAMP},
      revision = #{revision,jdbcType=INTEGER},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectPatientItemByHospitalAndDate" resultType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord">
    select user_id
    from patient_follow_up_item_record
    where
    item_status = 1 and
    hospital_id = #{hospitalId}  and
    begin_day <![CDATA[ <= ]]> #{now,jdbcType=DATE}
    and
    ( end_day <![CDATA[ >= ]]>  #{now,jdbcType=DATE} or end_day is null )
    group by user_id
    limit #{offset}, #{pageSize}
  </select>

  <select id="selectPatientItemByHospitalUser" resultType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord">
    select <include refid="Base_Column_List" />
    from patient_follow_up_item_record
    where
    item_status = 1 and
    hospital_id = #{hospitalId} and user_id = #{userId}
    and begin_day <![CDATA[ <= ]]> #{now,jdbcType=DATE}
    and ( ( end_day is not null and end_day <![CDATA[ >= ]]> #{now,jdbcType=DATE} ) or end_day is null )
  </select>

  <select id="getOne" parameterType="com.puree.followup.domain.followup.model.PatientFollowUpItemRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from patient_follow_up_item_record
    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId,jdbcType=BIGINT}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT}
      </if>
      <if test="itemId != null">
        and item_id = #{itemId,jdbcType=BIGINT}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId,jdbcType=BIGINT}
      </if>
      <if test="userId != null">
        and user_id = #{userId,jdbcType=BIGINT}
      </if>
      <if test="patientId != null">
        and patient_id = #{patientId,jdbcType=BIGINT}
      </if>
      <if test="terminatorType != null">
        and terminator_type = #{terminatorType,jdbcType=TINYINT}
      </if>
      <if test="terminator != null">
        and terminator = #{terminator,jdbcType=VARCHAR}
      </if>
      <if test="itemStatus != null">
        and item_status = #{itemStatus,jdbcType=TINYINT}
      </if>
      <if test="itemStatusList != null and itemStatusList.size() > 0  ">
        and item_status IN
        <foreach collection="itemStatusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="firstDayOfJoinWeek != null">
        and first_day_of_join_week = #{firstDayOfJoinWeek,jdbcType=TIMESTAMP}
      </if>
      <if test="revision != null">
        and revision = #{revision,jdbcType=INTEGER}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
    </where>
    limit 1
  </select>

</mapper>