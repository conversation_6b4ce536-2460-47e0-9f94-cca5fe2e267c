<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.FollowUpMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUp">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="classify_id" jdbcType="BIGINT" property="classifyId" />
    <result column="is_enable" jdbcType="TINYINT" property="isEnable" />
    <result column="is_publish" jdbcType="TINYINT" property="isPublish" />
    <result column="expire_type" jdbcType="TINYINT" property="expireType" />
    <result column="expire_day" jdbcType="INTEGER" property="expireDay" />
    <result column="department_limit" jdbcType="TINYINT" property="departmentLimit" />
    <result column="doctor_limit" jdbcType="TINYINT" property="doctorLimit" />
    <result column="task_expire_switch" jdbcType="TINYINT" property="taskExpireSwitch" />
    <result column="task_expire_day" jdbcType="INTEGER" property="taskExpireDay" />
    <result column="task_expire_warn_switch" jdbcType="TINYINT" property="taskExpireWarnSwitch" />
    <result column="task_expire_times" jdbcType="INTEGER" property="taskExpireTimes" />
    <result column="join_type" jdbcType="TINYINT" property="joinType" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="rule" javaType="com.puree.followup.domain.followup.model.joinrule.JoinRule" property="rule" typeHandler="com.puree.followup.config.JoinRuleTypeHandler" />
  </resultMap>

  <resultMap id="BaseResultMap2" extends="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUp">
    <collection property="rules" ofType="com.puree.followup.domain.followup.model.FollowUpJoinRule"
                select="com.puree.followup.admin.followup.mapper.FollowUpJoinRuleMapper.getSwitchOnList"
                column="{followUpId=id}">
    </collection>
  </resultMap>

  <sql id="followUpSql">
    id, `name`, `desc`, remark, hospital_id, classify_id, is_enable, is_publish,
    expire_type, expire_day, department_limit, doctor_limit, task_expire_switch, task_expire_day, 
    task_expire_warn_switch, task_expire_times, join_type, revision, create_by, create_time, 
    update_by, update_time, is_delete, applet_qr_code_auto, applet_qr_code_agree
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="followUpSql" />
    from follow_up
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getList" parameterType="com.puree.followup.domain.followup.dto.FollowUpQueryDTO" resultMap="BaseResultMap">
    select
    <include refid="followUpSql" />
    from follow_up t1
    <where>
      <if test="id != null">
        and t1.id = #{id}
      </if>
      <if test="name != null">
        and t1.name = #{name}
      </if>
      <if test="keyword != null">
        and t1.name like concat('%', trim(#{keyword}), '%')
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>
      <if test="classifyId != null">
        and t1.classify_id = #{classifyId}
      </if>
      <if test="isEnable != null">
        and t1.is_enable = #{isEnable}
      </if>
      <if test="isPublish != null">
        and t1.is_publish = #{isPublish}
      </if>
      <if test="revision != null">
        and t1.revision = #{revision}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    order by t1.create_time desc
  </select>

  <select id="getListByJoinRule" parameterType="com.puree.followup.domain.followup.model.FollowUpJoinRule" resultMap="BaseResultMap2">
    select t1.id, t1.`name`, t1.hospital_id, t1.classify_id, t1.expire_type, t1.expire_day, t1.is_enable, t1.is_publish,
    t1.department_limit, t1.doctor_limit, t1.task_expire_switch, t1.task_expire_day, t1.task_expire_warn_switch, t1.task_expire_times, t1.join_type
    from follow_up t1
    where t1.is_delete = 0 and t1.is_enable = 1 and t1.is_publish = 1 and t1.hospital_id = #{hospitalId}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.FollowUp" useGeneratedKeys="true">
    insert into follow_up (`name`, `desc`, remark, hospital_id, classify_id,
      is_enable, is_publish, expire_type, 
      expire_day, department_limit, doctor_limit, 
      task_expire_switch, task_expire_day, task_expire_warn_switch, 
      task_expire_times, join_type, revision, 
      create_by, create_time, update_by, update_time, is_delete)
    values (#{name,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
       #{hospitalId,jdbcType=BIGINT}, #{classifyId,jdbcType=BIGINT},
      #{isEnable,jdbcType=TINYINT}, #{isPublish,jdbcType=TINYINT}, #{expireType,jdbcType=TINYINT}, 
      #{expireDay,jdbcType=INTEGER}, #{departmentLimit,jdbcType=TINYINT}, #{doctorLimit,jdbcType=TINYINT}, 
      #{taskExpireSwitch,jdbcType=TINYINT}, #{taskExpireDay,jdbcType=INTEGER}, #{taskExpireWarnSwitch,jdbcType=TINYINT}, 
      #{taskExpireTimes,jdbcType=INTEGER}, #{joinType,jdbcType=TINYINT}, #{revision,jdbcType=INTEGER}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT})
  </insert>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.FollowUp">
    update follow_up
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="desc != null">
        `desc` = #{desc,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="classifyId != null">
        classify_id = #{classifyId,jdbcType=BIGINT},
      </if>
      <if test="isEnable != null">
        is_enable = #{isEnable,jdbcType=TINYINT},
      </if>
      <if test="isPublish != null">
        is_publish = #{isPublish,jdbcType=TINYINT},
      </if>
      <if test="expireType != null">
        expire_type = #{expireType,jdbcType=TINYINT},
      </if>
      <if test="expireDay != null">
        expire_day = #{expireDay,jdbcType=INTEGER},
      </if>
      <if test="departmentLimit != null">
        department_limit = #{departmentLimit,jdbcType=TINYINT},
      </if>
      <if test="doctorLimit != null">
        doctor_limit = #{doctorLimit,jdbcType=TINYINT},
      </if>
      <if test="taskExpireSwitch != null">
        task_expire_switch = #{taskExpireSwitch,jdbcType=TINYINT},
      </if>
      <if test="taskExpireDay != null">
        task_expire_day = #{taskExpireDay,jdbcType=INTEGER},
      </if>
      <if test="taskExpireWarnSwitch != null">
        task_expire_warn_switch = #{taskExpireWarnSwitch,jdbcType=TINYINT},
      </if>
      <if test="taskExpireTimes != null">
        task_expire_times = #{taskExpireTimes,jdbcType=INTEGER},
      </if>
      <if test="joinType != null">
        join_type = #{joinType,jdbcType=TINYINT},
      </if>
      <if test="revision != null">
        revision = revision + 1,
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="appletQrCodeAuto != null">
        applet_qr_code_auto = #{appletQrCodeAuto,jdbcType=VARCHAR},
      </if>
      <if test="appletQrCodeAgree != null">
        applet_qr_code_agree = #{appletQrCodeAgree,jdbcType=VARCHAR},
      </if>
    </set>
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="revision != null">
        and revision = #{revision}
      </if>
    </where>

  </update>

  <select id="getFollowUpWithGoodsRule" parameterType="com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO" resultMap="BaseResultMap">
    select distinct t1.id as id, t1.join_type, t2.rule as rule
    from follow_up t1 join follow_up_join_rule t2 on t1.id = t2.follow_up_id and t2.join_switch > 0 and t2.join_type = 3
    where t1.hospital_id = #{hospitalId} and t1.is_delete = 0 and t1.is_enable = 1 and t1.is_publish = 1
    and (
        SELECT count(1)
        FROM JSON_TABLE(
            t2.rule,
            '$.joinRuleGoods[*]'
            COLUMNS(
                id INT PATH '$.id'
            )
        ) AS jt
        <where>
          <if test="goodsIds != null and goodsIds.size() > 0  ">
            and jt.id IN
            <foreach collection="goodsIds" open="(" separator="," close=")" item="item">
              #{item}
            </foreach>
          </if>
        </where>
    ) > 0
  </select>

  <select id="getFollowUpWithDrugRule" parameterType="com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO" resultMap="BaseResultMap">
    select distinct t1.id as id, t1.join_type, t2.rule as rule
    from follow_up t1 join follow_up_join_rule t2 on t1.id = t2.follow_up_id and t2.join_switch > 0 and t2.join_type = 4
    where t1.hospital_id = #{hospitalId} and t1.is_delete = 0 and t1.is_enable = 1 and t1.is_publish = 1
    and (
        SELECT count(1)
        FROM JSON_TABLE(
            t2.rule,
            '$.joinRuleDrug[*]'
            COLUMNS(
                id INT PATH '$.drugsId'
            )
        ) AS jt
        <where>
          <if test="drugIds != null and drugIds.size() > 0  ">
            and jt.id IN
            <foreach collection="drugIds" open="(" separator="," close=")" item="item">
              #{item}
            </foreach>
          </if>
        </where>
    ) > 0
  </select>

  <select id="getFollowUpWithPackRule" parameterType="com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO" resultMap="BaseResultMap">
    select distinct t1.id as id, t1.join_type, t2.rule as rule
    from follow_up t1 join follow_up_join_rule t2 on t1.id = t2.follow_up_id and t2.join_switch > 0 and t2.join_type = 5
    where t1.hospital_id = #{hospitalId} and t1.is_delete = 0 and t1.is_enable = 1 and t1.is_publish = 1
    and (
        SELECT count(1)
        FROM JSON_TABLE(
            t2.rule,
            '$.joinRuleServicePackage[*]'
            COLUMNS(
                id INT PATH '$.id'
            )
        ) AS jt
        <where>
          <if test="packIds != null and packIds.size() > 0  ">
            and jt.id IN
            <foreach collection="packIds" open="(" separator="," close=")" item="item">
              #{item}
            </foreach>
          </if>
        </where>
    ) > 0
  </select>

  <select id="getFollowUpWithAllDiagnosis" parameterType="com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO" resultMap="BaseResultMap">
    select t1.id as id, t1.join_type, t2.rule as rule, t2.join_switch as joinSwitch
    from follow_up t1 join follow_up_join_rule t2 on t1.id = t2.follow_up_id and t2.join_switch = 2 and t2.join_type = 1,  JSON_TABLE(
                    t2.rule,
                    "$.joinRuleDiagnosis[*]" COLUMNS (
                        id INT PATH "$.id",
                        type VARCHAR(10) PATH "$.type",
                        syndromeId INT PATH "$.syndromeId"
                    )
                ) AS jt
    where t1.hospital_id = #{hospitalId} and t1.is_delete = 0 and t1.is_enable = 1 and t1.is_publish = 1
    GROUP BY t1.id
    having count(1) <![CDATA[<=]]> #{totalDiags}
    and sum(
            case
                <if test="mmIds != null and mmIds.size() > 0  ">
                  when jt.type = 'MM' and jt.id in
                  <foreach collection="mmIds" open="(" separator="," close=")" item="item">
                    #{item}
                  </foreach>
                  THEN 1
                </if>
                <if test="tcmList != null and tcmList.size() > 0  ">
                  when jt.type = 'TCM' and (jt.id, jt.syndromeId) in
                  <foreach collection="tcmList" open="(" separator="," close=")" item="item">
                    (#{item.id}, #{item.syndromeId})
                  </foreach>
                  THEN 1
                </if>
              else 0 end
    ) = count(1)
  </select>

   <select id="getFollowUpWithEitherDiagnosis" parameterType="com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO" resultMap="BaseResultMap">
    select distinct t1.id as id, t1.join_type , t2.rule as rule, t2.join_switch as joinSwitch
    from follow_up t1 join follow_up_join_rule t2 on t1.id = t2.follow_up_id and t2.join_switch = 1 and t2.join_type = 1,  JSON_TABLE(
                    t2.rule,
                    "$.joinRuleDiagnosis[*]" COLUMNS (
                        id INT PATH "$.id",
                        type VARCHAR(10) PATH "$.type",
                        syndromeId INT PATH "$.syndromeId"
                    )
                ) AS jt
    where t1.hospital_id = #{hospitalId} and t1.is_delete = 0 and t1.is_enable = 1 and t1.is_publish = 1

   <if test="(mmIds != null and mmIds.size() > 0) or (tcmList != null and tcmList.size() > 0)">
     and (
     <if test="mmIds != null and mmIds.size() > 0  ">
       ( jt.type = 'MM' and jt.id in
       <foreach collection="mmIds" open="(" separator="," close=")" item="item">
         #{item}
       </foreach>
       )
     </if>

     <if test="mmIds != null and mmIds.size() > 0 and tcmList != null and tcmList.size() > 0">
       or
     </if>

     <if test="tcmList != null and tcmList.size() > 0  ">
       ( jt.type = 'TCM' and (jt.id, jt.syndromeId) in
       <foreach collection="tcmList" open="(" separator="," close=")" item="item">
         (#{item.id}, #{item.syndromeId})
       </foreach>
       )
     </if>
     )
   </if>

  </select>



</mapper>