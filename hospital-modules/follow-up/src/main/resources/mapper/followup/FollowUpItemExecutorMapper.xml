<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.FollowUpItemExecutorMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUpItemExecutor">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="executor_switch" jdbcType="TINYINT" property="executorSwitch" />
    <result column="data_source_type" jdbcType="TINYINT" property="dataSourceType" />
    <result column="questionnaire_id" jdbcType="BIGINT" property="questionnaireId" />
    <result column="questionnaire_name" jdbcType="VARCHAR" property="questionnaireName" />
    <result column="valid_days" jdbcType="INTEGER" property="validDays" />
    <result column="warn_switch" jdbcType="TINYINT" property="warnSwitch" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="revision" jdbcType="INTEGER" property="revision" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="events" javaType="java.util.List" property="events" typeHandler="com.puree.followup.config.TaskEventTypeHandler" />
    <result column="conditions" javaType="java.util.List" property="conditions" typeHandler="com.puree.followup.config.SmartExecutorConditionTypeHandler" />
    <result column="follow_up_record_id" jdbcType="BIGINT" property="followUpRecordId" />
  </resultMap>

  <sql id="Base_Column_List">
    id, sort_num, follow_up_id, item_id, hospital_id, executor_switch, data_source_type,
    questionnaire_id, questionnaire_name, valid_days, warn_switch, create_by, create_time, 
    revision, is_delete, events, conditions
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from follow_up_item_executor
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.FollowUpItemExecutor" useGeneratedKeys="true">
    insert into follow_up_item_executor (sort_num, follow_up_id, item_id, 
      hospital_id, executor_switch, data_source_type,
      questionnaire_id, questionnaire_name, valid_days, 
      warn_switch, create_by, create_time, 
      revision, is_delete, events, 
      conditions)
    values (#{sortNum,jdbcType=INTEGER}, #{followUpId,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}, 
      #{hospitalId,jdbcType=BIGINT}, #{executorSwitch,jdbcType=TINYINT}, #{dataSourceType,jdbcType=TINYINT},
      #{questionnaireId,jdbcType=BIGINT}, #{questionnaireName,jdbcType=VARCHAR}, #{validDays,jdbcType=INTEGER}, 
      #{warnSwitch,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{revision,jdbcType=INTEGER}, #{isDelete,jdbcType=TINYINT}, #{events,typeHandler=com.puree.followup.config.TaskEventTypeHandler},
      #{conditions,typeHandler=com.puree.followup.config.SmartExecutorConditionTypeHandler})
  </insert>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into follow_up_item_executor (sort_num, follow_up_id, item_id,
      hospital_id, executor_switch, data_source_type,
      questionnaire_id, questionnaire_name, valid_days,
      warn_switch, create_by, create_time,
      revision, is_delete, events,
      conditions)
    values
    <foreach collection="collect" item="entity" separator=",">
      (#{entity.sortNum,jdbcType=INTEGER}, #{entity.followUpId,jdbcType=BIGINT}, #{entity.itemId,jdbcType=BIGINT},
      #{entity.hospitalId,jdbcType=BIGINT}, #{entity.executorSwitch,jdbcType=TINYINT}, #{entity.dataSourceType,jdbcType=TINYINT},
      #{entity.questionnaireId,jdbcType=BIGINT}, #{entity.questionnaireName,jdbcType=VARCHAR}, #{entity.validDays,jdbcType=INTEGER},
      #{entity.warnSwitch,jdbcType=TINYINT}, #{entity.createBy,jdbcType=VARCHAR}, #{entity.createTime,jdbcType=TIMESTAMP},
      #{entity.revision,jdbcType=INTEGER}, #{entity.isDelete,jdbcType=TINYINT}, #{entity.events,typeHandler=com.puree.followup.config.TaskEventTypeHandler},
      #{entity.conditions,typeHandler=com.puree.followup.config.SmartExecutorConditionTypeHandler})
    </foreach>
  </insert>

  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.FollowUpItemExecutor">
    update follow_up_item_executor
    <set>
      <if test="sortNum != null">
        sort_num = #{sortNum,jdbcType=INTEGER},
      </if>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="executorSwitch != null">
        executor_switch = #{executorSwitch,jdbcType=TINYINT},
      </if>
      <if test="dataSourceType != null">
        data_source_type = #{dataSourceType,jdbcType=TINYINT},
      </if>
      <if test="questionnaireId != null">
        questionnaire_id = #{questionnaireId,jdbcType=BIGINT},
      </if>
      <if test="questionnaireName != null">
        questionnaire_name = #{questionnaireName,jdbcType=VARCHAR},
      </if>
      <if test="validDays != null">
        valid_days = #{validDays,jdbcType=INTEGER},
      </if>
      <if test="warnSwitch != null">
        warn_switch = #{warnSwitch,jdbcType=TINYINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="revision != null">
        revision = #{revision,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="events != null">
        events = #{events,typeHandler=com.puree.followup.config.TaskEventTypeHandler},
      </if>
      <if test="conditions != null">
        conditions = #{conditions,typeHandler=com.puree.followup.config.SmartExecutorConditionTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getList" parameterType="com.puree.followup.domain.followup.model.FollowUpItemExecutor" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from follow_up_item_executor t1
    <where>
      <if test="id != null">
        and t1.id = #{id}
      </if>
      <if test="followUpId != null">
        and t1.follow_up_id = #{followUpId}
      </if>
      <if test="itemId != null">
        and t1.item_id = #{itemId}
      </if>
      <if test="hospitalId != null">
        and t1.hospital_id = #{hospitalId}
      </if>

      <if test="executorSwitch != null">
        and t1.executor_switch = #{executorSwitch}
      </if>
      <if test="dataSourceType != null">
        and t1.data_source_type = #{dataSourceType}
      </if>
      <if test="questionnaireId != null">
        and t1.questionnaire_id = #{questionnaireId}
      </if>
      <if test="warnSwitch != null">
        and t1.warn_switch = #{warnSwitch}
      </if>
      <if test="isDelete != null">
        and t1.is_delete = #{isDelete}
      </if>
    </where>
    order by sort_num asc
  </select>

  <select id="getFollowUpWithExecute" parameterType="com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO" resultMap="BaseResultMap">
    select t2.id, t2.follow_up_id, t2.item_id, t2.hospital_id, t2.events, t2.conditions, t2.executor_switch, t2.warn_switch, t2.valid_days, t2.data_source_type, t1.follow_up_record_id,
            t2.questionnaire_id, t2.questionnaire_name
    from (
            select follow_up_id as id, id as follow_up_record_id
            from patient_follow_up_record
            where hospital_id = #{hospitalId} and user_id = #{userId} and patient_id = #{patientId} and join_status = 1 and is_delete = 0
        ) t1 join follow_up_item_executor t2 on t1.id = t2.follow_up_id and t2.executor_switch > 0
    where t2.hospital_id = #{hospitalId}
    <if test="dataSourceType != null">
      and t2.data_source_type = #{dataSourceType}
    </if>
    <if test="questionnaireId != null">
      and t2.questionnaire_id = #{questionnaireId}
    </if>
    <if test="warnSwitch != null">
      and t2.warn_switch = #{warnSwitch}
    </if>
    <if test="followUpId != null">
      and t2.follow_up_id = #{followUpId}
    </if>
    <if test="itemId != null">
      and t2.item_id = #{itemId}
    </if>
    <if test="isDelete != null">
      and t2.is_delete = #{isDelete}
    </if>
  </select>

  <select id="getQuestionResult" parameterType="com.puree.followup.domain.followup.query.FollowUpItemExecutorQuery" resultType="com.puree.followup.domain.followup.model.SmartExecutorQuestionResult">
    select id, random_id as randomId, answer_content as answerContent
    from
      JSON_TABLE('${questionJson}',
              '$[*]'
              COLUMNS(
                        id VARCHAR ( 100 ) PATH '$.id',
                      random_id  VARCHAR(100) PATH '$.randomId',
                      answer JSON PATH '$.answer'
              )
      ) AS answer left join
        JSON_TABLE(
                answer.answer,
                '$[*]'
                COLUMNS(
                        answer_content VARCHAR(1000) PATH '$'
                )
        ) AS content
        on 1=1
    <where>
      <if test="questionSql != null and questionSql != ''">
        ${questionSql}
      </if>
    </where>
  </select>


</mapper>