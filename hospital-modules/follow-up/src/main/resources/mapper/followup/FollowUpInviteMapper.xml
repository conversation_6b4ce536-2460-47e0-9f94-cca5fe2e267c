<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.FollowUpInviteMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.FollowUpInvite">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="join_type" jdbcType="TINYINT" property="joinType" />
    <result column="select_patient_type" jdbcType="TINYINT" property="selectPatientType" />
    <result column="conditions" jdbcType="LONGVARCHAR" property="conditions" />
    <result column="patients" javaType="java.util.List" property="patients" typeHandler="com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler" />
    <result column="qrcode" jdbcType="VARCHAR" property="qrcode" />
    <result column="patient_count" jdbcType="INTEGER" property="patientCount" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <sql id="Base_Column_List">
    id, follow_up_id, hospital_id, join_type, select_patient_type,conditions, patients, qrcode, patient_count,
    create_by, create_time
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from follow_up_invite
    where id = #{id,jdbcType=BIGINT}
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.FollowUpInvite" useGeneratedKeys="true">
    insert into follow_up_invite (follow_up_id, hospital_id, join_type,
      select_patient_type, qrcode, patient_count, 
      create_by, create_time, conditions, 
      patients)
    values (#{followUpId,jdbcType=BIGINT}, #{hospitalId,jdbcType=BIGINT}, #{joinType,jdbcType=TINYINT}, 
      #{selectPatientType,jdbcType=TINYINT}, #{qrcode,jdbcType=VARCHAR}, #{patientCount,jdbcType=INTEGER}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{conditions,jdbcType=LONGVARCHAR}, #{patients, typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler})
  </insert>


  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.FollowUpInvite">
    update follow_up_invite
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="joinType != null">
        join_type = #{joinType,jdbcType=TINYINT},
      </if>
      <if test="selectPatientType != null">
        select_patient_type = #{selectPatientType,jdbcType=TINYINT},
      </if>
      <if test="qrcode != null">
        qrcode = #{qrcode,jdbcType=VARCHAR},
      </if>
      <if test="patientCount != null">
        patient_count = #{patientCount,jdbcType=INTEGER},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="conditions != null">
        conditions = #{conditions,jdbcType=LONGVARCHAR},
      </if>
      <if test="patients != null">
        patients = #{patients,typeHandler=com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>