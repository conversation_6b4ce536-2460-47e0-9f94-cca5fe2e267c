<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.puree.followup.admin.followup.mapper.PatientTaskHistoryMapper">
  <resultMap id="BaseResultMap" type="com.puree.followup.domain.followup.model.PatientTaskHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="follow_up_id" jdbcType="BIGINT" property="followUpId" />
    <result column="follow_up_record_id" jdbcType="BIGINT" property="followUpRecordId" />
    <result column="item_record_id" jdbcType="BIGINT" property="itemRecordId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
    <result column="hospital_id" jdbcType="BIGINT" property="hospitalId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="patient_id" jdbcType="BIGINT" property="patientId" />
    <result column="begin_day" jdbcType="DATE" property="beginDay" />
    <result column="end_day" jdbcType="DATE" property="endDay" />
    <result column="remind_time" jdbcType="TIMESTAMP" property="remindTime" />
    <result column="task_finish_time" jdbcType="TIMESTAMP" property="taskFinishTime" />
    <result column="task_remind_status" jdbcType="TINYINT" property="taskRemindStatus" />
    <result column="task_status" jdbcType="TINYINT" property="taskStatus" />
    <result column="is_terminate_future" jdbcType="TINYINT" property="isTerminateFuture" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="event_type" jdbcType="TINYINT" property="eventType" />
  </resultMap>

    <resultMap id="BaseResultMap2" extends="BaseResultMap" type="com.puree.followup.domain.followup.model.PatientTaskHistory">
        <collection property="events" ofType="com.puree.followup.domain.followup.model.PatientTaskEventHistory"
                    select="com.puree.followup.admin.followup.mapper.PatientTaskEventHistoryMapper.listByTaskHistoryId"
                    column="{taskHistoryId=id, eventType=event_type}">
        </collection>
    </resultMap>

  <sql id="Base_Column_List">
    id, follow_up_id, follow_up_record_id, item_record_id, task_id, task_name, item_id, hospital_id, user_id, patient_id, begin_day, end_day,
    remind_time, task_finish_time, task_remind_status, task_status, is_terminate_future,
    create_by, create_time,update_by, update_time,is_delete
  </sql>

  <select id="getById" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from patient_task_history
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="getTaskStatistics" parameterType="java.lang.Long" resultType="com.puree.followup.domain.followup.model.TaskStat">
     SELECT
      IFNULL(SUM(CASE WHEN task_status = 0 THEN 1 ELSE 0 END), 0) AS iscomingTasks,
      IFNULL(SUM(CASE WHEN task_status = 1 THEN 1 ELSE 0 END), 0) AS ongoingTasks,
      IFNULL(SUM(CASE WHEN task_status = 2 THEN 1 ELSE 0 END), 0) AS finishedTasks,
      IFNULL(SUM(CASE WHEN task_status = 3 THEN 1 ELSE 0 END), 0) AS hasExpiredTasks,
      IFNULL(SUM(CASE WHEN task_status = 4 THEN 1 ELSE 0 END), 0) AS terminatedTasks
    from patient_task_history
    where is_delete = 0 and follow_up_id = #{followUpId} and hospital_id = #{hospitalId}
    <if test="patientId != null">
      and patient_id = #{patientId}
    </if>
    <if test="followUpRecordId != null">
      and follow_up_record_id = #{followUpRecordId}
    </if>
  </select>


   <select id="getList" parameterType="com.puree.followup.domain.followup.model.PatientTaskHistory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from patient_task_history t1
        <where>
            <if test="followUpId != null">
                and t1.follow_up_id = #{followUpId}
            </if>
            <if test="followUpRecordId != null">
                and t1.follow_up_record_id = #{followUpRecordId}
            </if>
            <if test="followUpRecordIds != null and followUpRecordIds.size() > 0  ">
                and t1.follow_up_record_id IN
                <foreach collection="followUpRecordIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="itemRecordId != null">
                and t1.item_record_id = #{itemRecordId}
            </if>
            <if test="taskId != null">
                and t1.task_id = #{taskId}
            </if>
            <if test="itemId != null">
                and t1.item_id = #{itemId}
            </if>
            <if test="hospitalId != null">
                and t1.hospital_id = #{hospitalId}
            </if>
            <if test="userId != null">
                and t1.user_id = #{userId}
            </if>
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="taskRemindStatus != null">
                and t1.task_remind_status = #{taskRemindStatus}
            </if>
            <if test="taskStatus != null">
                and t1.task_status = #{taskStatus}
            </if>
            <if test="isTerminateFuture != null">
                and t1.is_terminate_future = #{isTerminateFuture}
            </if>
            <if test="taskStatusList != null and taskStatusList.size() > 0  ">
                and t1.task_status IN
                <foreach collection="taskStatusList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="isDelete != null">
                and t1.is_delete = #{isDelete}
            </if>
        </where>
        order by t1.create_time asc
    </select>


    <select id="getTaskCount" parameterType="com.puree.followup.domain.followup.model.PatientTaskHistory" resultType="int">
        select count(distinct task_id)
        from patient_task_history t1
        <where>
            <if test="followUpId != null">
                and t1.follow_up_id = #{followUpId}
            </if>
            <if test="followUpRecordId != null">
                and t1.follow_up_record_id = #{followUpRecordId}
            </if>
            <if test="taskIds != null and taskIds.size() > 0  ">
                and t1.task_id IN
                <foreach collection="taskIds" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="itemRecordId != null">
                and t1.item_record_id = #{itemRecordId}
            </if>
            <if test="taskId != null">
                and t1.task_id = #{taskId}
            </if>
            <if test="itemId != null">
                and t1.item_id = #{itemId}
            </if>
            <if test="hospitalId != null">
                and t1.hospital_id = #{hospitalId}
            </if>
            <if test="userId != null">
                and t1.user_id = #{userId}
            </if>
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="taskRemindStatus != null">
                and t1.task_remind_status = #{taskRemindStatus}
            </if>
            <if test="taskStatus != null">
                and t1.task_status = #{taskStatus}
            </if>
            <if test="isTerminateFuture != null">
                and t1.is_terminate_future = #{isTerminateFuture}
            </if>
            <if test="taskStatusList != null and taskStatusList.size() > 0  ">
                and t1.task_status IN
                <foreach collection="taskStatusList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="isDelete != null">
                and t1.is_delete = #{isDelete}
            </if>
        </where>

    </select>

   <select id="getOne" parameterType="com.puree.followup.domain.followup.model.PatientTaskHistory" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from patient_task_history t1
        <where>
            <if test="followUpId != null">
                and t1.follow_up_id = #{followUpId}
            </if>
            <if test="followUpRecordId != null">
                and t1.follow_up_record_id = #{followUpRecordId}
            </if>
            <if test="itemRecordId != null">
                and t1.item_record_id = #{itemRecordId}
            </if>
            <if test="taskId != null">
                and t1.task_id = #{taskId}
            </if>
            <if test="itemId != null">
                and t1.item_id = #{itemId}
            </if>
            <if test="hospitalId != null">
                and t1.hospital_id = #{hospitalId}
            </if>
            <if test="userId != null">
                and t1.user_id = #{userId}
            </if>
            <if test="patientId != null">
                and t1.patient_id = #{patientId}
            </if>
            <if test="beginDay != null">
                and t1.begin_day = #{beginDay,jdbcType=DATE}
            </if>
            <if test="remindTime != null">
                and date(t1.remind_time) = #{remindTime,jdbcType=DATE}
            </if>
            <if test="taskRemindStatus != null">
                and t1.task_remind_status = #{taskRemindStatus}
            </if>
            <if test="taskStatus != null">
                and t1.task_status = #{taskStatus}
            </if>
            <if test="isTerminateFuture != null">
                and t1.is_terminate_future = #{isTerminateFuture}
            </if>
            <if test="isDelete != null">
                and t1.is_delete = #{isDelete}
            </if>
        </where>
        limit 1
    </select>

    <select id="getListByDate" parameterType="com.puree.followup.domain.followup.bo.PatientTaskHistoryBO" resultMap="BaseResultMap">
        select
        t1.id, t1.follow_up_id, t1.follow_up_record_id, t1.item_record_id, t1.task_id, t1.task_name, t1.item_id, t1.hospital_id, t1.user_id, t1.patient_id, t1.begin_day, t1.end_day,
        t1.remind_time, t1.task_finish_time, t1.task_remind_status, t1.task_status,
        t1.create_by, t1.create_time, t1.update_by, t1.update_time, t1.is_delete, t2.name as item_name
        from patient_task_history t1 join follow_up_item t2 on t1.item_id = t2.id
        <where>
            <if test="followUpRecordId != null">
                and t1.follow_up_record_id = #{followUpRecordId}
            </if>

            <if test="followUpId != null">
                and t1.follow_up_id = #{followUpId,jdbcType=BIGINT}
            </if>
            <if test="hospitalId != null">
                and t1.hospital_id = #{hospitalId,jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                and t1.user_id = #{userId,jdbcType=BIGINT}
            </if>
            <if test="patientId != null">
                and t1.patient_id = #{patientId,jdbcType=BIGINT}
            </if>

            <if test="itemId != null">
                and t1.item_id = #{itemId}
            </if>
            <if test="taskId != null">
                and t1.task_id = #{taskId}
            </if>
            <if test="taskStatus != null">
                and t1.task_status = #{taskStatus}
            </if>

            <if test="taskStatusList != null and taskStatusList.size() > 0  ">
                and t1.task_status IN
                <foreach collection="taskStatusList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>

            <if test="minQueryDay != null and maxQueryDay != null">
                and t1.begin_day <![CDATA[>=]]> #{minQueryDay,jdbcType=DATE} and t1.begin_day <![CDATA[<=]]> #{maxQueryDay,jdbcType=DATE}
            </if>

            <if test="beginDay != null">
                and t1.begin_day <![CDATA[>=]]> #{beginDay,jdbcType=DATE}
            </if>
            <if test="endDay != null">
                and t1.end_day <![CDATA[<=]]> #{endDay,jdbcType=DATE}
            </if>
            <if test="isDelete != null">
                and t1.is_delete = #{isDelete}
            </if>
            <if test="smartExecutorFlag != null">
                and t1.item_record_id  <![CDATA[>]]>  0
            </if>
        </where>
        order by t1.begin_day asc
    </select>


   <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.PatientTaskHistory" useGeneratedKeys="true">
    insert into patient_task_history ( follow_up_id, follow_up_record_id, item_record_id, task_id, task_name, item_id, hospital_id, user_id, patient_id,
      begin_day, end_day, remind_time, task_finish_time,
      task_remind_status, task_status, is_terminate_future, create_by, create_time, update_by, update_time, is_delete
      )
    values ( #{followUpId,jdbcType=BIGINT},#{followUpRecordId,jdbcType=BIGINT},#{itemRecordId,jdbcType=BIGINT},#{taskId,jdbcType=BIGINT},#{taskName,jdbcType=BIGINT},
      #{itemId,jdbcType=BIGINT},#{hospitalId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},  #{patientId,jdbcType=BIGINT},
      #{beginDay,jdbcType=DATE}, #{endDay,jdbcType=DATE},  #{remindTime}, #{taskFinishTime,jdbcType=TIMESTAMP},
      #{taskRemindStatus,jdbcType=TINYINT}, #{taskStatus,jdbcType=TINYINT},  #{isTerminateFuture,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
       #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},#{isDelete,jdbcType=TINYINT})
  </insert>

  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.puree.followup.domain.followup.model.PatientTaskHistory" useGeneratedKeys="true">
   insert into patient_task_history ( follow_up_id, follow_up_record_id, item_record_id, task_id, task_name, item_id, hospital_id, user_id, patient_id,
      begin_day, end_day, remind_time, task_finish_time,
      task_remind_status, task_status, is_terminate_future, create_by, create_time, update_by, update_time, is_delete
      )
    values ( #{followUpId,jdbcType=BIGINT},#{followUpRecordId,jdbcType=BIGINT},#{itemRecordId,jdbcType=BIGINT},#{taskId,jdbcType=BIGINT},#{taskName,jdbcType=BIGINT},
      #{itemId,jdbcType=BIGINT},#{hospitalId,jdbcType=BIGINT}, #{userId,jdbcType=BIGINT},  #{patientId,jdbcType=BIGINT},
      #{beginDay,jdbcType=DATE}, #{endDay,jdbcType=DATE},  #{remindTime}, #{taskFinishTime,jdbcType=TIMESTAMP},
      #{taskRemindStatus,jdbcType=TINYINT}, #{taskStatus,jdbcType=TINYINT},  #{isTerminateFuture,jdbcType=TINYINT}, #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
       #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},#{isDelete,jdbcType=TINYINT})
    on duplicate key update
    follow_up_id = values(follow_up_id),
    is_delete = values(is_delete)
  </insert>

    <update id="updateTask" parameterType="com.puree.followup.domain.followup.model.PatientTaskHistory">
    update patient_task_history
    <set>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="isTerminateFuture != null">
        is_terminate_future = #{isTerminateFuture},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>

    <where>
      <if test="followUpId != null">
        and follow_up_id = #{followUpId}
      </if>
      <if test="followUpRecordId != null">
        and follow_up_record_id = #{followUpRecordId}
      </if>
      <if test="hospitalId != null">
        and hospital_id = #{hospitalId}
      </if>
       <if test="itemId != null">
        and item_id = #{itemId}
      </if>
      <if test="isDelete != null">
        and is_delete = #{isDelete}
      </if>
      <if test="taskStatusList != null and taskStatusList.size() > 0  ">
        and task_status IN
        <foreach collection="taskStatusList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </update>


  <update id="updateById" parameterType="com.puree.followup.domain.followup.model.PatientTaskHistory">
    update patient_task_history
    <set>
      <if test="followUpId != null">
        follow_up_id = #{followUpId,jdbcType=BIGINT},
      </if>
      <if test="followUpRecordId != null">
        follow_up_record_id = #{followUpRecordId,jdbcType=BIGINT},
      </if>
      <if test="itemRecordId != null">
        item_record_id = #{itemRecordId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
      <if test="hospitalId != null">
        hospital_id = #{hospitalId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="patientId != null">
        patient_id = #{patientId,jdbcType=BIGINT},
      </if>
      <if test="beginDay != null">
        begin_day = #{beginDay,jdbcType=DATE},
      </if>
      <if test="endDay != null">
        end_day = #{endDay,jdbcType=DATE},
      </if>
      <if test="remindTime != null">
        remind_time = #{remindTime, jdbcType=TIMESTAMP},
      </if>
      <if test="taskFinishTime != null">
        task_finish_time = #{taskFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskRemindStatus != null">
        task_remind_status = #{taskRemindStatus,jdbcType=TINYINT},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=TINYINT},
      </if>
      <if test="isTerminateFuture != null">
        is_terminate_future = #{isTerminateFuture},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectTodayPatientTaskHistory" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List" /> from patient_task_history
    where 1=1

    <if test=" hospitalId!=null ">
      and hospital_id = #{hospitalId}
    </if>
    <if test=" followUpId!=null ">
      and follow_up_id = #{followUpId}
    </if>
    <if test=" itemId!=null ">
      and item_id = #{itemId}
    </if>
    <if test=" taskId!=null ">
      and task_id = #{taskId}
    </if>
    <if test=" followUpRecordId!=null ">
      and follow_up_record_id = #{followUpRecordId}
    </if>
    <if test=" itemRecordId!=null ">
      and item_record_id = #{itemRecordId}
    </if>
    <if test=" userId!=null ">
      and user_id = #{userId}
    </if>
    <if test=" patientId!=null ">
      and patient_id = #{patientId}
    </if>
    and begin_day = #{beginDay,jdbcType=DATE}

  </select>

    <select id="countFutureTerminatedPatientTaskHistory" resultType="int">

        select  count(*) from patient_task_history
        where

        patient_id = #{patientId}
        and begin_day <![CDATA[ < ]]> #{beginDay,jdbcType=DATE}
        and is_terminate_future = 1

        <if test=" hospitalId!=null ">
            and hospital_id = #{hospitalId}
        </if>
        <if test=" followUpId!=null ">
            and follow_up_id = #{followUpId}
        </if>
        <if test=" itemId!=null ">
            and item_id = #{itemId}
        </if>
        <if test=" taskId!=null ">
            and task_id = #{taskId}
        </if>
        <if test=" followUpRecordId!=null ">
            and follow_up_record_id = #{followUpRecordId}
        </if>
        <if test=" itemRecordId!=null ">
            and item_record_id = #{itemRecordId}
        </if>
        <if test=" userId!=null ">
            and user_id = #{userId}
        </if>

    </select>

</mapper>