package com.puree.followup.domain.followup.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 患者随访任务执行历史表 DTO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientTaskHistoryQueryDTO {

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 随访记录ID
     */
    private Long recordId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 任务
     */
    private Long taskId;

    /**
     * 事件类型： questionnaire:问卷、tutorial:患教、message:消息、recommend:推荐
     */
    private String eventType;

    /**
     * 任务完成状态： iscoming:未开始、 ongoing:待完成、 finished:已完成、 hasexpired:已过期、 terminated:已终止
     */
    private String taskStatus;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 每页多少个
     */
    private Integer pageSize;

    /**
     * 多少页
     */
    private Integer pageNum;

    /**
     * up：向上取数据(取之前的数据)  down：向下取数据(取未来的数据)
     */
    private String upOrDown;

    /**
     * 是否自动
     */
    private Boolean isAuto;

    /**
     * 患者入组开始时间
     */
    private String joinInBeginDateStr;

    /**
     * 患者入组结束时间
     */
    private String joinInEndDateStr;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 是否停止对未来任务的生成
     * 该字段主要针对统计接口使用，如果是长期随访的分项，不需要统计未来任务
     */
    private Boolean isStopFutureQuery = false;



}