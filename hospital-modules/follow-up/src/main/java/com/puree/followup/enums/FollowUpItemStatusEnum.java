package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/14 17:55
 * @Description  分项状态：0.未开始 1.进行中 2.已结束 3.提前终止
 */
public enum FollowUpItemStatusEnum {

    ISCOMING(0, "未开始"),
    ONGOING(1, "进行中"),
    FINISHED(2, "已结束"),
    TERMINATED(3, "提前终止");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    FollowUpItemStatusEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static FollowUpItemStatusEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpItemStatusEnum[] metaArr = FollowUpItemStatusEnum.values();
        for (FollowUpItemStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpItemStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpItemStatusEnum[] metaArr = FollowUpItemStatusEnum.values();
        for (FollowUpItemStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
