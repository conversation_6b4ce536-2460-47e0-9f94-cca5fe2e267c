package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.JoinRuleTypeHandler;
import com.puree.followup.domain.followup.model.joinrule.JoinRule;
import com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访-入组方式
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpJoinRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 入组方式标识：1.处方诊断 2.数据推送 3.购买商品 4.购买药品 5.购买服务包 6.健康档案
     */
    private Integer joinType;

    /**
     * 入组开关：0.关闭 1.开启(任一) 2.满足全部
     */
    private Integer joinSwitch;

    /**
     * 规则id
     */
    private Long ruleId;

    /**
     * 入组规则
     */
    @TableField(typeHandler = JoinRuleTypeHandler.class)
    private JoinRule rule;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 入组开关列表
     */
    private List<Integer> joinSwitchList;

}