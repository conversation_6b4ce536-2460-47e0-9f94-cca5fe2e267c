package com.puree.followup.domain.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.medical.constant.RegularRecordParentEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportSummarySort
 * <AUTHOR>
 * @Description 排序
 * @Date 2024/5/23 17:26
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportSummarySort {
    /**
     * 主键
     */
    private Long id ;
    /**
     * 排序值
     */
    private Integer sortNum ;
    /**
     * 排序类型;BM 身体质量指数,BP 血压,BS 血糖,BT 体温,BL 血脂,UA 尿酸,BO 血氧
     */
    private RegularRecordParentEnum sortType ;
    /**
     * 医院ID
     */
    private Long  hospitalId ;
    /**
     * 用户ID
     */
    private Long userId ;
    /**
     * 乐观锁
     */
    private Integer revision ;
    /**
     * 创建人
     */
    private String createBy ;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime ;
    /**
     * 更新人
     */
    private String updateBy ;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime ;
}
