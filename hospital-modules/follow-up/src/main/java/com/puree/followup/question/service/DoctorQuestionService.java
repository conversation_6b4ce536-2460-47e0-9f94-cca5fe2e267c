package com.puree.followup.question.service;

import com.puree.followup.question.domain.dto.DoctorQuestionDTO;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.vo.PatientQuestionVO;
import com.puree.followup.question.domain.vo.QuestionVO;

import java.util.List;

/**
 * @ClassName: DoctorQuestionService
 * @Date 2024/7/23 18:16
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public interface DoctorQuestionService {

    List<QuestionVO> doctorPageQuestion(DoctorQuestionDTO req) ;

    List<PatientQuestionRecord> doctorPagePatientQuestion(DoctorQuestionDTO req) ;

    PatientQuestionVO doctorDetailPatientQuestion(Long id) ;

    void doctorSendQuestion(DoctorQuestionDTO req) ;

    QuestionVO doctorDetailQuestion(Long id) ;


}
