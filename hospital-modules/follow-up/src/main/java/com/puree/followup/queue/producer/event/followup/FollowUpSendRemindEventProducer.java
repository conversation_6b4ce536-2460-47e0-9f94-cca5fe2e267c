package com.puree.followup.queue.producer.event.followup;

import com.puree.hospital.common.redis.mq.RedisStreamProducer;
import com.puree.hospital.common.redis.mq.annotation.RedisProducer;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendRemindEvent;

/**
 *  随访事件提醒生产者
 * <AUTHOR>
 * @date 2025/2/28 14:34
 */
@RedisProducer(topic = FollowUpSendRemindEvent.TOPIC)
public class FollowUpSendRemindEventProducer extends RedisStreamProducer<FollowUpSendRemindEvent> {
}
