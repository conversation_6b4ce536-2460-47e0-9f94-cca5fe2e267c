package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 随访-分项 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemDTO {

    /**
     * ID
     * 该ID的值也可以是时间戳(取反)：针对智能执行功能，如果是新增的分项，并且在设置事件中，事件的类型为“加入新分项”时，选择的分项为新增的分项，那么该字段作为分项的id
     */
    private Long id;

    /**
     * 临时id
     */
    private Long tempId;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项名称
     */
    private String name;

    /**
     * 分项排序
     */
    private Integer sortNum;

    /**
     * 分项执行时间的类型：longTerm:全周期 、joinIn:加入随访后第X至Y天 、eventJoinIn:通过事件加入后至Y天
     */
    private String executeType;

    /**
     * 分项执行时间：加入随访第X天开始
     */
    private Integer beginDay;

    /**
     * 分项执行时间：加入随访至Y天结束，或者表示通过事件加入至Y天结束
     */
    private Integer endDay;

//    /**
//     * 提醒时间的类型：0.统一提醒 1.单独提醒
//     */
//    private Integer remindTimeType;
//
//    /**
//     * 提醒时间，格式为：HH:mm
//     */
//    private String remindTime;

    /**
     * 分项任务执行周期的类型：custom:自定义 、periodCycle:周期循环、weekCycle:固定周循环
     */
    private String intervalType;

    /**
     * 分项任务(包括执行事件)
     */
    private List<FollowUpItemTaskDTO> tasks;

    /**
     * 智能执行列表
     */
    private List<FollowUpSmartExecutorDTO> smartExecutors;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 分项编辑时的行为：add、update、del
     */
    private String action;

    /**
     * 记录分项执行时间的类型是否发生变化
     */
    private Boolean executeTypeHasChanged = false;

}