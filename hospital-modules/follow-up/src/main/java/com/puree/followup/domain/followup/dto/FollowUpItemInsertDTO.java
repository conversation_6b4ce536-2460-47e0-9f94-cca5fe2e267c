package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 随访分项新增 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemInsertDTO {

    /**
     * 随访
     */
    private Long followUpId;

    /**
     * 随访记录id
     */
    private Long followUpRecordId;

    /**
     * 分项id
     */
    private Long itemId;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 随访入组类型：0.直接添加 1.邀请入组(需患者同意) 2.手动直接添加 3.手动邀请入组(需患者同意)
     */
    private Integer joinType;

    /**
     * 患者随访状态：0.邀请中 1.随访中 2.已完成 3.提前终止 4.已拒绝
     */
    private Integer joinStatus;

    /**
     * 入组原因
     */
    private String joinReason;

    /**
     * 患者入组的来源：0.默认入组  1.诊断入组  2.数据推送入组 3.商品入组 4.药品入组 5.服务包入组 6.健康档案入组 7.所属医生入组
     */
    private Integer sourceType;

    /**
     * 群组id（服务包和处方诊断入组时所属的群组）
     */
    private Long groupId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * redis锁的key
     */
    private String redisKey;

    /**
     * 患者手机号
     */
    private String phoneNum ;

}