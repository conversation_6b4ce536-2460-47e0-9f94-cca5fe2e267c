package com.puree.followup.domain.medical.dto;

import com.puree.followup.domain.medical.constant.RegularRecordEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @ClassName ReportRegularRecordDTO
 * <AUTHOR>
 * @Description 修改 DTO
 * @Date 2024/5/24 16:59
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ReportRegularRecordUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 值
     */
    private String itemValue;
    /**
     * 项目类型;TALL为 身高,WEIGHT为 体重,BMI为 体重指数,SYSTOLIC为 收缩压,DIASTOLIC为 舒张压,PULSE_RATE为 脉搏,BLOOD_SUGAR为 血糖,BODY_TEMPERATURE为 体温,TOTAL_CHOLESTEROL为 总胆固醇,TRIGLYCERIDES为 甘油三脂,HDL为 高密度脂蛋白,LDL为 低密度脂蛋白,URIC_ACID为 尿酸,BLOOD_OXYGEN为 血氧，HEART_RATE 心率
     */
    private RegularRecordEnum itemType;
}
