package com.puree.followup.notification.assembler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.puree.followup.domain.followup.dto.FollowUpEventBusinessDTO;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.business.api.model.BusPartnersVO;
import com.puree.hospital.common.notification.constant.TemplateMessageConstants;
import com.puree.hospital.common.notification.domain.bo.WxOfficialAccountTemplateData;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 随访事件提醒通知组装器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 18:41
 */
public class FollowUpSendRemindNotificationAssembler extends BaseFollowUpNotificationAssembler {

    public FollowUpSendRemindNotificationAssembler(BaseFollowUpEvent event, FollowUpEventBusinessDTO businessDTO) {
        super(event, businessDTO);
    }

    /**
     * 获取微信公众号模板参数
     *
     * @return 微信公众号模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxOfficialAccountTemplateParam() {
        Map<String, WxOfficialAccountTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.THING2, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME));
        templateParam.put(TemplateMessageConstants.TIME3, new WxOfficialAccountTemplateData(DateUtil.format(getEvent().getEventTime(), DatePattern.NORM_DATETIME_FORMAT)));
        templateParam.put(TemplateMessageConstants.CONST4, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_SUMMARY));
        return convert(templateParam);
    }

    /**
     * 获取微信小程序模板参数
     *
     * @return 小程序模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxUniAppTemplateParam() {
        return getDefaultWxUniAppTemplateParam();
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxOfficialAccountMsgKey() {
        return TemplateMessageConstants.WX_OFFICIAL_ACCOUNT_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxUniAppMsgKey() {
        return TemplateMessageConstants.WX_UNI_APP_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取微信公众号重定向页面
     *
     * @param wechatConfig 医院微信公众号配置
     * @param partnersVO   合作机构方信息
     * @return 重定向页面
     */
    @Override
    public String getWxOfficialAccountRedirectPage(BusHospitalWechatConfig wechatConfig, BusPartnersVO partnersVO) {
        return followUpRemindDefaultRedirectPage();
    }

    /**
     * 获取微信小程序重定向页面
     *
     * @return 重定向页面
     */
    @Override
    public String getWxUniAppRedirectPage(BusHospitalWechatConfig wechatConfig) {
        return followUpRemindDefaultRedirectPage();
    }

    /**
     *  随访患教默认跳转页面
     * @return  path
     */
    private String followUpRemindDefaultRedirectPage() {
        return String.format(TemplateMessageConstants.FOLLOW_UP_SUMMARY_URL, getBusinessDTO().getPatientId());
    }
}
