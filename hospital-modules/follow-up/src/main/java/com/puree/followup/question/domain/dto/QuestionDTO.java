package com.puree.followup.question.domain.dto;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.question.domain.model.ScoreValue;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: QuestionDTO
 * @Date 2024/6/12 18:08
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
@Data
public class QuestionDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 问卷名称
     */
    @NotBlank
    private String name;

    /**
     * 问卷描述
     */
    private String desc;

    /**
     * 备注说明(仅内部可见)
     */
    private String remark;

    /**
     * 分类id
     */
    @NotNull
    private Long classifyId;

    /**
     * 显示题目标题 ：0.否 1.是
     */
    @NotNull
    @Max(1)
    @Min(0)
    private Integer showOrder ;

    /**
     * 可用科室：0.不限 1.自定义
     */
    @NotNull
    @Max(1)
    @Min(0)
    private Integer departmentLimit;

    private List<List<Long>> departmentCascadeIds ;

    /**
     * 可用医生：0.不限 1.自定义
     */
    @NotNull
    @Max(1)
    @Min(0)
    private Integer doctorLimit;

    private List<Long> doctorIds ;

    /**
     * 问卷关闭时间类型：0.长期 1.自定义 ; 通过随访打开问卷，不受问卷启用状态影响
     */
    @NotNull
    @Max(1)
    @Min(0)
    private Integer closeType ;

    /**
     * 问卷关闭开始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date closeBeginDay ;


    /**
     * 每人最多可填问卷次数 ; 通过随访打开问卷，不受填写次数影响
     */
    @NotNull
    @Min(1)
    private Integer fillTimes ;

    /**
     * 用户填写问卷后可更改, 0.否 1.是
     */
    @NotNull
    @Max(1)
    @Min(0)
    private Integer canRevise ;

    /**
     * 积分问卷, 0.否 1.是
     */
    @NotNull
    @Max(1)
    @Min(0)
    private Integer isScore ;

    /**
     * 问卷试题内容, 包含 :  题目， 患者指标， 患者报告
     */
    @NotBlank
    private String questionContent ;

    /**
     *  问卷总分评定
     */
    private List<ScoreValue> questionScoreValues;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0;

    /**
     * 问卷状态: 0-关闭 1-启用 2-发布
     * QuestionStatusEnum
     */
    private Integer status ;

}
