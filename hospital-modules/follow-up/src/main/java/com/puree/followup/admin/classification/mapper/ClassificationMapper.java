package com.puree.followup.admin.classification.mapper;

import com.puree.followup.domain.classification.model.Classification;
import com.puree.followup.question.domain.dto.DuplicateClassificationDTO;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;


/**
 * 随访-分类mapper
 * <AUTHOR>
 * @date 2024-04-07 14:31:51
 */
public interface ClassificationMapper {

    int insert(Classification record);

    List<Classification> getList(Classification record);

    Classification getById(Long id);

    int updateById(Classification record) throws SQLIntegrityConstraintViolationException;

    Classification getOne(Classification query);

    int findDuplicateName(DuplicateClassificationDTO req) ;

    List<Classification> getQuestionClassificationList(Classification record) ;

}