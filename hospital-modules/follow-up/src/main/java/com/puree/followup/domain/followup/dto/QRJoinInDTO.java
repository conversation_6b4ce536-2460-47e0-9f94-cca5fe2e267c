package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户扫描二维码加入随访 DTO
 * <AUTHOR>
 * @date 2024-05-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class QRJoinInDTO {

    /**
     * 随访ID
     */
    private Long id;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 入组类型：auto:自动入组 、patientAgree:需患者同意
     */
    private String joinType;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 就诊人id
     */
    private Long patientId;

}