package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 患者随访事件执行历史 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
public class PatientTaskEventHistoryListVO {

    /**
     * 事件执行记录ID
     */
    private Long id;

    /**
     * 任务执行ID
     */
    private Long taskHistoryId;

    /**
     * 执行事件
     */
    private TaskEventVO events;

    /**
     * 账号
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 事件完成状态： iscoming:未开始、 ongoing:待完成、 finished:已完成、 hasexpired:已过期、 terminated:已终止
     */
    private String eventFinishStatus;

    /**
     * 事件完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventExecuteTime;
}