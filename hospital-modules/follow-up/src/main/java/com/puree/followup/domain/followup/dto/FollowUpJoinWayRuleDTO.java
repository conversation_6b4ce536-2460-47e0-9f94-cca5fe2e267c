package com.puree.followup.domain.followup.dto;

import com.puree.followup.domain.followup.dto.joinrule.JoinRuleDTO;
import com.puree.followup.domain.followup.model.joinrule.JoinRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 随访-入组方式 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpJoinWayRuleDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 入组方式标识：diagnosis.处方诊断 pushData.数据推送 goods.商品 drug.药品 servicePackage.服务包 healthRecord.健康档案 doctor.所属医生
     */
    private String joinType;

    /**
     * 入组开关：close:关闭 enable:开启(任一) all:满足全部
     */
    private String joinSwitch;

    /**
     * 入组规则
     */
    private JoinRuleDTO joinRule;

}