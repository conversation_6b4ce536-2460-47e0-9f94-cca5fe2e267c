package com.puree.followup.notification.assembler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.puree.followup.domain.followup.dto.FollowUpEventBusinessDTO;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.business.api.model.BusPartnersVO;
import com.puree.hospital.common.notification.constant.TemplateMessageConstants;
import com.puree.hospital.common.notification.domain.bo.WxOfficialAccountTemplateData;
import com.puree.hospital.common.notification.domain.bo.WxUniAppTemplateData;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 随访问卷通知组装器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 18:41
 */
public class FollowUpSendQuestionNotificationAssembler extends BaseFollowUpNotificationAssembler {

    public FollowUpSendQuestionNotificationAssembler(BaseFollowUpEvent event, FollowUpEventBusinessDTO businessDTO) {
        super(event, businessDTO);
    }

    /**
     * 获取微信公众号模板参数
     *
     * @return 微信公众号模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxOfficialAccountTemplateParam() {
        Map<String, WxOfficialAccountTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.THING2, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME));
        templateParam.put(TemplateMessageConstants.TIME3, new WxOfficialAccountTemplateData(DateUtil.format(getEvent().getEventTime(), DatePattern.NORM_DATETIME_FORMAT)));
        templateParam.put(TemplateMessageConstants.CONST4, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_EVENT_QUESTION));
        return convert(templateParam);
    }

    /**
     * 获取微信小程序模板参数
     *
     * @return 小程序模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxUniAppTemplateParam() {
        Map<String, WxUniAppTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.NAME3, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME));
        templateParam.put(TemplateMessageConstants.THING2, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_WX_UNI_UNIFIED_MSG));
        templateParam.put(TemplateMessageConstants.THING4, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_EVENT_QUESTION));
        return convert(templateParam);
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxOfficialAccountMsgKey() {
        return TemplateMessageConstants.WX_OFFICIAL_ACCOUNT_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxUniAppMsgKey() {
        return TemplateMessageConstants.WX_UNI_APP_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取微信公众号重定向页面
     *
     * @param wechatConfig 医院微信公众号配置
     * @param partnersVO   合作机构方信息
     * @return 重定向页面
     */
    @Override
    public String getWxOfficialAccountRedirectPage(BusHospitalWechatConfig wechatConfig, BusPartnersVO partnersVO) {
        return followUpJoinDefaultRedirectPage();
    }

    /**
     * 获取微信小程序重定向页面
     *
     * @return 重定向页面
     */
    @Override
    public String getWxUniAppRedirectPage(BusHospitalWechatConfig wechatConfig) {
        return followUpJoinDefaultRedirectPage();
    }

    /**
     * 默认跳转页面
     */
    private String followUpJoinDefaultRedirectPage() {
        if (getBusinessDTO().isContentIsTooLong()) {
            // 问卷ID + 患者ID + 患者随访事件执行历史表
            return String.format(TemplateMessageConstants.FOLLOW_UP_QUESTION_OVER_URL, getBusinessDTO().getQuestionId(), getBusinessDTO().getPatientId(), getBusinessDTO().getTaskEventHistoryId());
        } else {
            return String.format(TemplateMessageConstants.FOLLOW_UP_QUESTION_URL, getBusinessDTO().getQuestionId(), getBusinessDTO().getPatientId(), getBusinessDTO().getTaskEventHistoryId());
        }
    }
}
