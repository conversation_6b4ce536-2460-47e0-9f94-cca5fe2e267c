package com.puree.followup.question.domain.model.topic;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * @Date 2024/7/21 12:03
 * <AUTHOR>
 * @Description: 问卷题目-答案
 * @Version 1.0
 */

@NoArgsConstructor
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class QuestionFormatAnswer {

    //题目的id
    private String id;

    //题目的类型
    private Integer type;

    //是否为数字类型
    private Boolean isNum = false;

    //患者指标或报告的名称
    private String reportName;

    //患者报告的项目名称
    private String reportItemName;

    //题目的答案
    private List<Object> answer = new ArrayList<>();

    //mysql中把json转换成表结构时，用于标识表中每条记录的唯一id
    private String randomId;
}
