package com.puree.followup.queue.producer.event.tutorial;

import com.puree.hospital.common.redis.mq.RedisStreamProducer;
import com.puree.hospital.common.redis.mq.annotation.RedisProducer;
import com.puree.hospital.tool.api.model.event.TutorialArticlePushEvent;

/**
 *  患教推送事件生产者
 * <AUTHOR>
 * @date 2025/2/28 14:34
 */
@RedisProducer(topic = TutorialArticlePushEvent.TOPIC)
public class TutorialArticlePushEventProducer extends RedisStreamProducer<TutorialArticlePushEvent> {
}
