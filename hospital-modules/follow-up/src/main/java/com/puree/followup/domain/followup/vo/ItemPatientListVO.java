package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 分项下的患者随访记录表 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ItemPatientListVO {

    /**
     * 患者随访记录ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者手机号
     */
    private String patientPhone;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者性别
     */
    private String patientSex;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date itemBeginTime;

    /**
     *  分项状态：followuping:进行中、finished:已结束、terminated:提前终止
     */
    private String itemStatus;

    /**
     * 结束/终止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date itemEndTime;

    /**
     * 分项下的任务总数
     */
    private Integer totalTasks;

    /**
     * 分项下进行中的任务数(也即是待完成的，今天的)
     */
    private Integer ongoingTasks;

    /**
     * 分项下已完成任务数
     */
    private Integer finishedTasks;

    /**
     * 分项下已过期任务数
     */
    private Integer hasExpiredTasks;

    /**
     * 分项的随访进度(百分比)
     */
    private BigDecimal finishedFollowUpRate;

}