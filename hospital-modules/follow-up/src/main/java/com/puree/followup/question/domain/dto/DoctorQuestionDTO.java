package com.puree.followup.question.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @ClassName: DoctorQuestionDTO
 * @Date 2024/7/23 18:10
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
@Data
public class DoctorQuestionDTO {

    private Integer pageSize ;
    private Integer pageNum ;

    private Long hospitalId ;

    private Long doctorId ;
    private String doctorName ;

    /**
     * 医助ID
     */
    private Long  assistantId;
    /**
     * 医助姓名
     */
    private String assistantName ;

    private Long questionId ;
    private String questionName ;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date submitTimeStart ;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date submitTimeEnd ;

    /**
    * 表示患者的 名字 或者 手机号
    */
    private String keyword ;

    private Long groupId ;

    private Long userId ;
    private Long patientId ;



}
