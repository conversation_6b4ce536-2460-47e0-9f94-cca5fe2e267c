package com.puree.followup.domain.medical.dto;

import com.puree.followup.domain.medical.constant.RegularRecordParentEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName ReportSummarySortDTO
 * <AUTHOR>
 * @Description 排序新增或修改 DTO
 * @Date 2024/5/23 17:55
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ReportSummarySortDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 排序值
     */
    @Min(value = 1,message = "排序值 不能小于1")
    @NotNull(message = "排序值 不能为空")
    private Integer sortNum;
    /**
     * 排序类型;BM 身体质量指数,BP 血压,BS 血糖,BT 体温,BL 血脂,UA 尿酸,BO 血氧
     */
    @NotNull(message = "排序类型 不能为空")
    private RegularRecordParentEnum sortType;
    /**
     * 医院ID
     */
    @NotNull(message = "医院ID 不能为空")
    @Min(value = 1,message = "医院ID 格式错误")
    private Long hospitalId;
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID 不能为空")
    @Min(value = 1,message = "用户ID 格式错误")
    private Long userId;
}
