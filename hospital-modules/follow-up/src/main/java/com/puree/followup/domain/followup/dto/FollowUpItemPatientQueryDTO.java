package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 随访分项患者查询 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemPatientQueryDTO {

    /**
     * 患者的随访记录ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 患者姓名/手机号
     */
    private String patientKeyword;

    /**
     *  分项状态：followuping:进行中、finished:已结束、terminated:提前终止
     */
    private String itemStatus;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date itemBeginTime;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date itemEndTime;

    /**
     * 每页多少个
     */
    private Integer pageSize;

    /**
     * 多少页
     */
    private Integer pageNum;

}