package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  事件完成状态：0.未开始 1.消息未读/问卷未填写 2.消息已读/问卷已填写 3.已过期 4.已终止
 */
public enum PatientEventStatusEnum {

    ISCOMING(0, "iscoming", "未开始"),
    ONGOING(1, "ongoing", "待完成"),
    FINISHED(2, "finished", "已完成"),
    HASEXPIRED(3, "hasexpired", "已过期"),
    TERMINATED(4, "terminated","已终止");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    PatientEventStatusEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static PatientEventStatusEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        PatientEventStatusEnum[] metaArr = PatientEventStatusEnum.values();
        for (PatientEventStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static PatientEventStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        PatientEventStatusEnum[] metaArr = PatientEventStatusEnum.values();
        for (PatientEventStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
