package com.puree.followup.domain.drools;

import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.followup.api.model.medical.json.ExamItemJson;
import com.puree.hospital.followup.api.model.medical.json.ExamSummaryJson;
import com.puree.hospital.followup.api.model.medical.json.InspectItemJson;
import com.puree.hospital.followup.api.model.medical.json.InspectSummaryJson;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName HealthRecordVO
 * <AUTHOR>
 * @Description 更多条件入组 DTO
 * @Date 2024/7/31 18:47
 * @Version 1.0
 */
@Data
public class HealthRecordDTO {

    /**
     * 患者基本信息
     */
    private BusPatientFamilyVo patientFamilyVo;
    /**
     * 检查项
     */
    private Map<String,ExamItemJson> examItem;
    /**
     * 检测项
     */
    private Map<String,InspectItemJson> inspectItem;
    /**
     *  检查项小结
     */
    private ExamSummaryJson[] examSummary;
    /**
     * 检验项小结
     */
    private InspectSummaryJson[] inspectSummary;
    /**
     * 上一份体检报告
     */
    private ExamReportInfoDTO infoDTO;
}
