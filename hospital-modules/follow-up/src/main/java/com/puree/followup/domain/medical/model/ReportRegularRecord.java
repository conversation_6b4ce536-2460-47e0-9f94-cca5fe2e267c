package com.puree.followup.domain.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.followup.domain.medical.constant.RegularRecordEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportRegular
 * <AUTHOR>
 * @Description 体检报告常规记录表
 * @Date 2024/5/13 17:55
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportRegularRecord {
    /**
     * 主键
     */
    private Long id;
    /**
     * 项目类型;TALL为 身高,WEIGHT为 体重,BMI为 体重指数,SYSTOLIC为 收缩压,DIASTOLIC为 舒张压,PULSE_RATE为 脉搏,BLOOD_SUGAR为 血糖,BODY_TEMPERATURE为 体温,TOTAL_CHOLESTEROL为 总胆固醇,TRIGLYCERIDES为 甘油三脂,HDL为 高密度脂蛋白,LDL为 低密度脂蛋白,URIC_ACID为 尿酸,BLOOD_OXYGEN为 血氧，HEART_RATE 心率
     */
    private RegularRecordEnum itemType;
    /**
     * 项目值
     */
    private String itemValue;
    /**
     * 项目值单位
     */
    private String itemUnit;
    /**
     * 来源类型;DATA_PUSH 数据推送，PATIENT_ADDED 患者添加，BACKGROUND_ADD 后台添加或医生添加，DOCTOR_ADDED 医生添加
     */
    private RecordSourceEnum source ;
    /**
     * 体检数据记录ID
     */
    private Long recordId;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 患者身份证号
     */
    private String patientIdNumber;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标记;0未删除1已删除
     */
    private Boolean isDelete;
}
