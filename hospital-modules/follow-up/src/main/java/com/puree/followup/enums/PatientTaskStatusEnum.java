package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  患者任务完成状态：0.未开始(即将开始，未来的) 1.进行中(未完成、待完成，今天的) 2.已完成 3.已过期 4.已终止
 */
public enum PatientTaskStatusEnum {

    ISCOMING(0, "iscoming", "即将开始"),
    ONGOING(1, "ongoing", "待完成"),
    FINISHED(2, "finished", "已完成"),
    HASEXPIRED(3, "hasexpired", "已过期"),
    TERMINATED(4, "terminated","已终止");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    PatientTaskStatusEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static PatientTaskStatusEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        PatientTaskStatusEnum[] metaArr = PatientTaskStatusEnum.values();
        for (PatientTaskStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static PatientTaskStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        PatientTaskStatusEnum[] metaArr = PatientTaskStatusEnum.values();
        for (PatientTaskStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
