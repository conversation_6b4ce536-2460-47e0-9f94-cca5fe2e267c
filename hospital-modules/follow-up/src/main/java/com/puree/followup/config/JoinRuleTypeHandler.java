package com.puree.followup.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.domain.followup.model.joinrule.JoinRule;
import com.puree.followup.domain.followup.model.joinrule.JoinRuleHealthRecord;
import com.puree.followup.domain.followup.model.joinrule.JoinRulePushData;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.util.CollectionUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 增加该TypeHandler的目的是处理入组规则中，类路径发生变更("ruleClass": "com.puree.hospital.app.api.model.BusPatientFamilyVo")导致的反序列化失败
 * <AUTHOR>
 */
public class JoinRuleTypeHandler extends BaseTypeHandler<JoinRule> {
    private static final ObjectMapper mapper = new ObjectMapper();
    private Class<JoinRule> clazz;

    public JoinRuleTypeHandler(Class<JoinRule> clazz) {
        if (clazz == null) throw new IllegalArgumentException("Type argument cannot be null");
        this.clazz = clazz;
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, JoinRule parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, this.toJson(parameter));
    }

    @Override
    public JoinRule getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return this.toObject(rs.getString(columnName), clazz);
    }

    @Override
    public JoinRule getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return this.toObject(rs.getString(columnIndex), clazz);
    }

    @Override
    public JoinRule getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return this.toObject(cs.getString(columnIndex), clazz);
    }

    private String toJson(JoinRule object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private JoinRule toObject(String content, Class<JoinRule> clazz) {
        if (content != null && !content.isEmpty()) {
            try {
                JoinRule t = mapper.readValue(content, clazz);
                return t;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } else {
            return null;
        }
    }

    static {
        //mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }
}
