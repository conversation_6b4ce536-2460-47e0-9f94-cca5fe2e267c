package com.puree.followup.domain.medical.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName InspectExamItemDTO
 * <AUTHOR>
 * @Description 参数
 * @Date 2024/7/11 16:16
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InspectExamItemDTO {
    /**
     * 名称
     */
    @NotBlank(message = "名称 不能为空")
    private String name;
    /**
     * 类型（检查单，化验单）
     */
    @NotBlank(message = "类型（检查单，化验单） 不能为空")
    private InspectExamTypeDTO type;
}
