package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者随访备注记录表 DTO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientFollowUpRemarkDTO {

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 备注记录的内容
     */
    private String content;

    /**
     * 每页多少个
     */
    private Integer pageSize;

    /**
     * 多少页
     */
    private Integer pageNum;


}