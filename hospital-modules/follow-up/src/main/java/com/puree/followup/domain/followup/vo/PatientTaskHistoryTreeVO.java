package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 患者随访任务执行历史表 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientTaskHistoryTreeVO {

    /**
     * 只给前端组件使用，没有其他用途
     */
    private String id;

    /**
     * 日期
     */
    private String showData;

    /**
     * 是否为今天
     */
    private String isToday;

    /**
     * 任务列表
     */
    private List<PatientTaskHistoryVO> children;

}