package com.puree.followup.domain.followup.dto.joinrule;

import com.puree.followup.domain.drools.constant.ComparisonOperatorEnum;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import lombok.Data;

/**
 * @ClassName JoinRuleTwo
 * <AUTHOR>
 * @Description 入组方式2 数据推送
 * @Date 2024/4/16 15:57
 * @Version 1.0
 */
@Data
public class JoinRuleTwoDTO {

    /**
     * 第一级选项
     */
    private String firstLevelOption;
    /**
     * 第二级选项
     */
    private String secondLevelOptions;
    /**
     * 比较运算符
     */
    private ComparisonOperatorEnum comparisonOperatorEnum;
    /**
     * 预期值
     */
    private String expectedValue;
    /**
     * 额外的预期值，用于区间内的比较
     */
    private String additionalExpectedValue;


}
