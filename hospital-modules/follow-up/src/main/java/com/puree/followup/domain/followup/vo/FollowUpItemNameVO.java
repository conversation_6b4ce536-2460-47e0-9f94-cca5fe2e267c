package com.puree.followup.domain.followup.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 随访-分项名称列表 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemNameVO {

    /**
     * 分项ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项名称
     */
    private String name;

    /**
     * 分项排序
     */
    private Integer sortNum;

}