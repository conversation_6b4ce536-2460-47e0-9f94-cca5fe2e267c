package com.puree.followup.domain.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.medical.constant.RegularRecordEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName ReportRegularRecordSummary
 * <AUTHOR>
 * @Description 体检报告常规记录汇总表;需要配合 bus_ehr_scope 表使用（数据范围值）
 * @Date 2024/5/13 17:39
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportRegularRecordSummary {
    /**
     * 主键
     */
    private Long id;
    /**
     * 身高
     */
    private BigDecimal tall;
    /**
     * 体重
     */
    private BigDecimal weight;
    /**
     * 体重指数
     */
    private BigDecimal bmi;
    /**
     * 收缩压
     */
    private BigDecimal systolic;
    /**
     * 舒张压
     */
    private BigDecimal diastolic;
    /**
     * 脉搏
     */
    private BigDecimal pulseRate;
    /**
     * 血糖
     */
    private BigDecimal bloodSugar;
    /**
     * 血糖类型，FASTING_BLOOD_SUGAR 空腹，POSTPRANDIAL_BLOOD_SUGAR 餐后，RANDOM_BLOOD_SUGAR 随机
     */
    private RegularRecordEnum bloodSugarType;
    /**
     * 体温
     */
    private BigDecimal bodyTemperature;
    /**
     * 总胆固醇
     */
    private BigDecimal totalCholesterol;
    /**
     * 甘油三脂
     */
    private BigDecimal triglycerides;
    /**
     * 高密度脂蛋白
     */
    private BigDecimal hdl;
    /**
     * 低密度脂蛋白
     */
    private BigDecimal ldl;
    /**
     * 尿酸
     */
    private BigDecimal uricAcid;
    /**
     * 血氧
     */
    private BigDecimal bloodOxygen;
    /**
     * 心率
     */
    private BigDecimal heartRate;
    /**
     * 血型
     */
    private String bloodType;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 患者身份证
     */
    private String patientIdNumber;
    /**
     * 身体质量指数测量时间
     */
    private Date bodyMassTime;
    /**
     * 血压测量时间
     */
    private Date bpTime;
    /**
     * 血糖测量时间
     */
    private Date bsTime;
    /**
     * 体温测量时间
     */
    private Date btTime;
    /**
     * 血脂测量时间
     */
    private Date blTime;
    /**
     * 尿酸测量时间
     */
    private Date uaTime;
    /**
     * 血氧测量时间
     */
    private Date boTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 收缩压(左)
     */
    private BigDecimal leftSystolic;

    /**
     * 舒张压(左)
     */
    private BigDecimal leftDiastolic;

    /**
     * IAD
     */
    private BigDecimal iad;
}
