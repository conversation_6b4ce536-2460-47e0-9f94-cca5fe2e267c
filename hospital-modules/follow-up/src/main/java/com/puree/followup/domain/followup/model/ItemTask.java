package com.puree.followup.domain.followup.model;

import lombok.Data;

import java.util.List;

/**
 * @ClassName: ItemTask
 * @Date 2024/4/10 12:17
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
public class ItemTask {

    /**
     *  任务id（后端使用）
     */
    private Long id;

    /**
     *  任务序号(前端使用)
     */
    private Integer taskNum;

    /**
     *  任务名称
     */
    private String taskName ;

    /**
     * 0---自定义 : 加入第x天
     * 1---周期循环 : 每x天循环
     * 2---固定周循环 : 每x周循环
     *
     * FollowUpIntervalTypeEnum
     */
    private Integer intervalType;

    private Integer intervals ;

    /**
     * 固定周循环 : 星期y
     * 字符串数组
     */
    private String weekDays ;

    /**
     *  提醒时间 例如 10:30
     */
    private String executeTime ;

    /**
     * 版本号
     */
    private Integer revision;

    /**
     * 任务编辑时的行为：add、update、del
     */
    private String action;

    /**
     *  事件列表
     */
    private List<TaskEvent> taskEventList ;

}
