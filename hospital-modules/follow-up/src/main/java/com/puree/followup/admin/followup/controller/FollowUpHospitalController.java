package com.puree.followup.admin.followup.controller;

import cn.hutool.core.date.DateUtil;
import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.domain.followup.dto.FollowUpDTO;
import com.puree.followup.domain.followup.dto.FollowUpItemOverviewDTO;
import com.puree.followup.domain.followup.dto.FollowUpItemPatientQueryDTO;
import com.puree.followup.domain.followup.dto.FollowUpPatientQueryDTO;
import com.puree.followup.domain.followup.dto.FollowUpQueryDTO;
import com.puree.followup.domain.followup.dto.FollowUpSwitchDTO;
import com.puree.followup.domain.followup.dto.PatientFollowUpRemarkDTO;
import com.puree.followup.domain.followup.dto.PatientTaskEventHistoryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskHistoryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskHistoryQueryDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.PatientFollowUpItemRecord;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.vo.FollowUpItemNameVO;
import com.puree.followup.domain.followup.vo.FollowUpListVO;
import com.puree.followup.domain.followup.vo.FollowUpVO;
import com.puree.followup.domain.followup.vo.ItemPatientListVO;
import com.puree.followup.domain.followup.vo.ItemPatientProgressVO;
import com.puree.followup.domain.followup.vo.ItemTaskListVO;
import com.puree.followup.domain.followup.vo.PatientFollowUpRemarkVO;
import com.puree.followup.domain.followup.vo.PatientItemOverviewVO;
import com.puree.followup.domain.followup.vo.PatientListVO;
import com.puree.followup.domain.followup.vo.PatientOverviewVO;
import com.puree.followup.domain.followup.vo.PatientStatVO;

import com.puree.followup.domain.followup.vo.PatientTaskHistoryTreeVO;
import com.puree.followup.domain.followup.vo.TaskEventVO;
import com.puree.followup.enums.FollowUpJoinStatusEnum;
import com.puree.followup.enums.PatientTaskStatusEnum;
import com.puree.followup.question.service.FollowUpQuestionService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.tool.api.model.Article;
import com.puree.hospital.tool.api.model.ArticleAdminPageVO;
import com.puree.hospital.tool.api.model.ArticlePageDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2024/4/7 16:10
 * @description 医院后台 随访 控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/hospital-admin")
public class FollowUpHospitalController extends BaseController {

    @Value("${task.history.query.maxIntervalDays:31}")
    private Integer maxIntervalDays ; //查询患者随访记录时的最大时间跨度
    private final IFollowUpService followUpService;
    private final PatientFollowUpRecordMapper patientFollowUpRecordMapper;
    private final PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper;
    private final FollowUpMapper followUpMapper;
    private final FollowUpQuestionService followUpQuestionService;

    /**
     * 医院后台 分页查询当前医院已发布的患教列表
     *
     * @param dto 查询条件
     * @return 患教列表
     */
    @GetMapping("/articles")
    @Log(title = "医院后台-分页获取患教列表", businessType = BusinessType.QUERY)
    public Paging<List<ArticleAdminPageVO>> articlePage(ArticlePageDTO dto) {
        return followUpService.articlePage(dto);
    }

    /**
     * 医院后台 获取患教详情
     *
     * @param id 患教id
     * @return 患教详情
     */
    @GetMapping("/article/info/{id}")
    @Log(title = "医院后台-获取患教详情", businessType = BusinessType.QUERY)
    public AjaxResult<Article> articleInfo(@PathVariable("id") Long id) {
        return followUpService.articleInfo(id);
    }

    /**
     * 医院后台 分页查询随访列表
     *
     * @param dto 查询条件
     * @return 随访列表
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:page")
    @GetMapping
    @Log(title = "医院后台-分页获取随访列表", businessType = BusinessType.QUERY)
    public Paging<List<FollowUpListVO>> hospitalPageList(FollowUpQueryDTO dto) {
        return PageUtil.buildPage(followUpService.hospitalPageList(dto));
    }

    /**
     * 医院后台 全量查询随访列表（只获取随访基本信息列表，不做统计等复杂查询操作）
     *
     * @param dto 查询条件
     * @return 随访列表
     */
    @GetMapping("/list")
    @Log(title = "医院后台-获取随访列表", businessType = BusinessType.QUERY)
    public AjaxResult<List<FollowUpListVO>> hospitalList(FollowUpQueryDTO dto) {
        return AjaxResult.success(followUpService.hospitalList(dto));
    }

    /**
     * 医院后台 根据随访id查询随访详情
     *
     * @param id 随访id
     * @return 随访详情
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:info")
    @GetMapping("/{id}")
    @Log(title = "医院后台-随访详情", businessType = BusinessType.QUERY)
    public AjaxResult<FollowUpVO> getInfo(@PathVariable("id") Long id) {
        FollowUp followUp = followUpMapper.getById(id);
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        return AjaxResult.success(followUpService.getInfo(followUp));
    }

    /**
     * 医院后台 添加随访
     *
     * @param dto 随访详情
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:add")
    @Log(title = "医院后台-添加随访", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Long> add(@Valid @RequestBody FollowUpDTO dto) {
        //数据校验
        if(CollectionUtils.isEmpty(dto.getSubitemList()))
            throw new ServiceException("请填写至少一个分项");
        Long followUpId = followUpService.hospitalAdd(dto);
        followUpQuestionService.bindFollowUpQuestion(followUpId);
        return AjaxResult.success(followUpId);
    }

    /**
     * 医院后台 修改随访
     *
     * @param dto 随访详情
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:edit")
    @Log(title = "医院后台-修改随访", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Boolean> edit(@Valid @RequestBody FollowUpDTO dto) {
        //数据校验（分项至少一个）
        if(CollectionUtils.isEmpty(dto.getSubitemList()))
            throw new ServiceException("请填写至少一个分项");
        FollowUp followUp = followUpMapper.getById(dto.getId());
        if(followUp == null)
            throw new ServiceException("随访不存在");
        dto.setRedisKey("edit:" + SecurityUtils.getHospitalId() + ":" + dto.getId());
        Boolean result = followUpService.hospitalEdit(dto, followUp);
        followUpQuestionService.bindFollowUpQuestion(dto.getId());
        return AjaxResult.success(result);
    }

    /**
     * 医院后台 根据随访id删除随访
     *
     * @param id       随访id
     * @param revision 乐观锁
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:remove")
    @Log(title = "医院后台-删除随访", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult<Boolean> remove(@PathVariable("id") Long id,
                                      @RequestParam("revision") Integer revision) {
        FollowUp followUp = followUpMapper.getById(id);
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        return AjaxResult.success(followUpService.hospitalRemove(id, revision));
    }

    /**
     * 医院后台 随访复制
     *
     * @param dto 随访详情
     * @return
     */
    @Log(title = "医院后台-随访复制", businessType = BusinessType.INSERT)
    @PostMapping("/copy")
    public AjaxResult<Boolean> copy(@RequestBody FollowUpDTO dto) {
        return AjaxResult.success(followUpService.copy(dto));
    }

    /**
     * 医院后台 修改随访启用状态
     *
     * @param dto 随访详情
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:enable")
    @Log(title = "医院后台-修改随访启用状态", businessType = BusinessType.UPDATE)
    @PutMapping("/enable/{id}")
    public AjaxResult<Boolean> enableSwitch(@PathVariable("id") Long id, @RequestBody FollowUpSwitchDTO dto) {
        dto.setId(id);
        FollowUp followUp = followUpMapper.getById(dto.getId());
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        return AjaxResult.success(followUpService.hospitalEnableSwitch(dto, followUp));
    }

    /**
     * 医院后台 修改随访发布状态
     *
     * @param dto 随访详情
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:publish")
    @Log(title = "医院后台-修改随访发布状态", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{id}")
    public AjaxResult<Boolean> publishSwitch(@PathVariable("id") Long id, @RequestBody FollowUpSwitchDTO dto) {
        dto.setId(id);
        FollowUp followUp = followUpMapper.getById(dto.getId());
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        return AjaxResult.success(followUpService.hospitalPublishSwitch(dto, followUp));
    }

    /**
     * 医院后台 获取随访基本信息
     *
     * @param id  随访id
     * @return
     */
    @GetMapping("/basic-info/{id}")
    @Log(title = "医院后台-获取随访基本信息", businessType = BusinessType.QUERY)
    public AjaxResult<FollowUpVO> getFollowUpBasicInfo(@PathVariable("id") Long id) {
        FollowUp followUp = followUpMapper.getById(id);
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        return AjaxResult.success(followUpService.getFollowUpBasicInfo(followUp));
    }

    //--------------------------------------------------分割线-------------------------

    /**
     * 医院后台 获取随访下的分项名称列表
     *
     * @param followUpId 随访id
     * @return 分项名称列表
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:subitem-name:list")
    @GetMapping("/subitem-name/{followUpId}")
    @Log(title = "医院后台-获取随访下的分项名称列表", businessType = BusinessType.QUERY)
    public AjaxResult<List<FollowUpItemNameVO>> getSubitemNameList(@PathVariable("followUpId") Long followUpId) {
        return AjaxResult.success(followUpService.getSubitemNameList(followUpId));
    }

    /**
     * 医院后台 随访患者数据统计
     *
     * @param followUpId 随访id
     * @return 随访患者数据
     */
    @GetMapping("/patient/stat/{followUpId}")
    @Log(title = "医院后台-随访患者数据统计", businessType = BusinessType.QUERY)
    public AjaxResult<PatientStatVO> getPatientStatistics(@PathVariable("followUpId") Long followUpId) {
        return AjaxResult.success(followUpService.getPatientStatistics(followUpId));
    }

    /**
     * 医院后台 分页获取患者总览列表
     *
     * @param dto 查询条件
     * @return 患者总览列表
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:patient:page")
    @GetMapping("/patient")
    @Log(title = "医院后台-分页获取患者总览列表", businessType = BusinessType.QUERY)
    public Paging<List<PatientListVO>> hospitalPatientPage(FollowUpPatientQueryDTO dto) {
        return PageUtil.buildPage(followUpService.hospitalPatientPage(dto));
    }

    /**
     * 医院后台 患者总览-终止随访
     *
     * @param id 患者随访记录id
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:patient:terminate")
    @PutMapping("/terminate/{id}")
    @Log(title = "医院后台-患者总览-终止随访", businessType = BusinessType.UPDATE)
    public AjaxResult<Boolean> terminateFollowUp(@PathVariable("id") Long id) {

        PatientFollowUpRecord followUpRecord = patientFollowUpRecordMapper.getById(id);
        if(followUpRecord == null || followUpRecord.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("患者随访不存在");

        //只终止随访中的
        if(!followUpRecord.getJoinStatus().equals(FollowUpJoinStatusEnum.FOLLOWUPING.getIndex()))
            throw new ServiceException("随访状态不正确");

        return AjaxResult.success(followUpService.terminateFollowUp(id));
    }

    /**
     * 医院后台 分页获取分项下的随访患者列表
     *
     * @param dto 查询条件
     * @return 患者总览列表
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:subitem-patient:page")
    @GetMapping("/subitem-patient")
    @Log(title = "医院后台-分页获取分项下的随访患者列表", businessType = BusinessType.QUERY)
    public Paging<List<ItemPatientListVO>> subitemPatientPage(FollowUpItemPatientQueryDTO dto) {
        return PageUtil.buildPage(followUpService.hospitalSubitemPatientPage(dto));
    }

    /**
     * 医院后台 分项患者列表-终止分项
     *
     * @param id         患者随访记录id
     * @param subitemIds 分项id列表
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:subitem-patient:terminate")
    @PutMapping("/subitem-terminate/{id}")
    @Log(title = "医院后台-分项患者列表-终止分项", businessType = BusinessType.UPDATE)
    public AjaxResult<Boolean> terminateSubitem(@PathVariable("id") Long id, @RequestBody List<Long> subitemIds) {
        return AjaxResult.success(followUpService.terminateSubitem(id, subitemIds));
    }

    /**
     * 医院后台 获取患者的各分项完成情况列表
     *
     * @param dto 查询条件
     * @return 患者的各分项完成情况列表
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:subitem-patient-progress:list")
    @GetMapping("/subitem-patient/progress")
    @Log(title = "医院后台-获取患者的各分项完成情况列表", businessType = BusinessType.QUERY)
    public AjaxResult<List<ItemPatientProgressVO>> subitemPatientProgressList(FollowUpItemPatientQueryDTO dto) {
        return AjaxResult.success(followUpService.hospitalSubitemPatientProgressList(dto));
    }


    /**
     * 医院后台 患者概览
     *
     * @param id 患者随访记录id
     * @return 患者概览
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:patient:overview")
    @GetMapping("/patient/overview")
    @Log(title = "医院后台-患者概览", businessType = BusinessType.QUERY)
    public AjaxResult<PatientOverviewVO> patientOverview(@RequestParam("id") Long id) throws ExecutionException, InterruptedException {
        return AjaxResult.success(followUpService.patientOverview(id));
    }

    /**
     * 医院后台 患者概览-分项下的任务列表
     *
     * @param dto 查询参数
     * @return 任务列表
     */
    @GetMapping("/patient/item-overview")
    @Log(title = "医院后台-患者概览-分项下任务列表", businessType = BusinessType.QUERY)
    public AjaxResult<PatientItemOverviewVO> patientItemOverview(FollowUpItemOverviewDTO dto) {

        if(dto.getBeginDate() == null || dto.getEndDate() == null){
            throw new ServiceException("起止时间不完整");
        }

        Integer intervalDays = (int) DateUtil.betweenDay(dto.getBeginDate(),  dto.getEndDate(),true);
        if(intervalDays > maxIntervalDays)
            throw new ServiceException("查询时间范围超出" + maxIntervalDays + "天");

        return AjaxResult.success(followUpService.patientItemOverview(dto));
    }

    /**
     * 医院后台 分页查询患者的备注记录列表
     *
     * @param dto 查询条件
     * @return 患者的备注记录列表
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:remark-record:page")
    @GetMapping("/remark-record")
    @Log(title = "医院后台-分页查询患者的备注记录列表", businessType = BusinessType.QUERY)
    public Paging<List<PatientFollowUpRemarkVO>> patientRemarkRecordPage(PatientFollowUpRemarkDTO dto) {
        return PageUtil.buildPage(followUpService.hospitalPatientRemarkRecordPage(dto));
    }

    /**
     * 医院后台 给患者添加备注记录
     *
     * @param dto 备注记录
     * @return
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:remark-record:add")
    @Log(title = "医院后台-给患者添加备注记录", businessType = BusinessType.INSERT)
    @PostMapping("/remark-record")
    public AjaxResult<Boolean> addRemarkRecord(@Valid @RequestBody PatientFollowUpRemarkDTO dto) {
        return AjaxResult.success(followUpService.addRemarkRecord(dto));
    }

    /**
     * 医院后台 删除备注记录
     *
     * @param id       备注记录id
     * @param revision 乐观锁
     * @return
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:remark-record:remove")
    @Log(title = "医院后台-删除备注记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/remark-record/{id}")
    public AjaxResult<Boolean> removeRdmarkRecord(@PathVariable("id") Long id,
                                                  @RequestParam("revision") Integer revision) {
        return AjaxResult.success(followUpService.removeRdmarkRecord(id, revision));
    }

    /**
     * 医院后台 获取随访/分项下的任务列表（目前只做随访下的任务列表，任务id使用雪花算法）
     *
     * @param followUpId 随访id
     * @param subitemId  分项id
     * @return 任务列表
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:task:list")
    @GetMapping("/task")
    @Log(title = "医院后台-获取随访/分项下的任务列表", businessType = BusinessType.QUERY)
    public AjaxResult<List<ItemTaskListVO>> taskList(@RequestParam("followUpId") Long followUpId, @RequestParam(value = "subitemId", required = false) Long subitemId) {
        return AjaxResult.success(followUpService.taskList(followUpId, subitemId));
    }

    /**
     * 医院后台 获取患者的随访记录列表（按日期的列表树）
     *
     * @param dto 查询条件
     * @return 患者的随访记录列表
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:task-history:page")
    @GetMapping("/task-history-aut")
    @Log(title = "医院后台-获取患者的随访记录列表", businessType = BusinessType.QUERY)
    public AjaxResult<List<PatientTaskHistoryTreeVO>> taskHistoryPage(PatientTaskHistoryQueryDTO dto) {
        if(dto.getBeginTime() == null || dto.getEndTime() == null){
            throw new ServiceException("起止时间不完整");
        }

        PatientFollowUpRecord record = patientFollowUpRecordMapper.getById(dto.getRecordId());
        if(record == null || record.getIsDelete().equals(YesNoEnum.YES.getCode())){
            throw new ServiceException("随访记录不存在");
        }

        AjaxResult result = AjaxResult.success(new ArrayList<>());
        result.add("isEnd", true);
        result.add("maxIntervalDays", maxIntervalDays);

        //计算该患者的本次随访的最小开始时间和最大结束时间
        PatientFollowUpItemRecord itemRecordQuery = new PatientFollowUpItemRecord();
        itemRecordQuery.setFollowUpRecordId(dto.getRecordId());
        PatientFollowUpItemRecord itemRecords = patientFollowUpItemRecordMapper.getMaxBeginDayAndEndDay(itemRecordQuery);
        if(itemRecords == null)
            return result;
        //最小开始时间和最大结束时间
        Date minBeginDate = itemRecords.getBeginDay();
        Date maxEndDate = itemRecords.getEndDay();

        dto.setJoinInBeginDateStr(DateUtils.parse_yyyyMMdd(minBeginDate));
        dto.setJoinInEndDateStr(maxEndDate == null ? null : DateUtils.parse_yyyyMMdd(maxEndDate));

        Integer intervalDays = (int) DateUtil.betweenDay(dto.getBeginTime(),  dto.getEndTime(),true);
        if(intervalDays > maxIntervalDays)
            throw new ServiceException("查询时间范围超出" + maxIntervalDays + "天");

        result.add("beginDate", dto.getJoinInBeginDateStr());
        result.add("endDate", dto.getJoinInEndDateStr());

        //是否超出患者随访时间范围
        if(dto.getEndTime().before(minBeginDate) || (maxEndDate != null && maxEndDate.before(dto.getBeginTime())))
            return result;

        //对于状态查询的优化：如果查询已完成、已过期、未完成的任务，那么需要判断的开始时间与当前时间的大小，避免无意义的查询
        if(!StringUtils.isEmpty(dto.getTaskStatus())
                && !PatientTaskStatusEnum.ISCOMING.getName().equals(dto.getTaskStatus())
                && dto.getBeginTime().compareTo(new Date()) > 0)
            return result;

        return followUpService.taskHistoryPage(dto, record);
    }

    /**
     * 医院后台 终止任务
     *
     * @param isAll   是否终止患者未来全部同类型任务
     * @param dto  要终止的任务
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:task-event:terminate")
    @PutMapping("/task/terminate")
    @Log(title = "医院后台-终止任务", businessType = BusinessType.UPDATE)
    public AjaxResult<Boolean> terminateTask(@RequestParam("isAll") Boolean isAll, @RequestBody PatientTaskHistoryDTO dto) {
        return AjaxResult.success(followUpService.terminateTask(isAll, dto));
    }

    /**
     * 医院后台 终止事件(和玉总确认过：只终止任务，不终止事件)
     *
     * @param isAll   是否终止患者未来全部同类型事件
     * @param dto 要终止的事件
     * @return
     */
    @PreAuthorize(hasPermi = "follow-up:hospital:task-event:terminate")
    @PutMapping("/task/event/terminate")
    @Log(title = "医院后台-终止事件", businessType = BusinessType.UPDATE)
    public AjaxResult<Boolean> terminateEvent(@RequestParam("isAll") Boolean isAll,
                                              @RequestBody PatientTaskEventHistoryDTO dto) {
        return AjaxResult.success(followUpService.terminateEvent(isAll, dto));
    }


    /**
     * 医院后台 再次提醒（是指提醒某个事件，该事件必须是当天且待完成的事件）
     *
     * @param id 事件执行记录ID
     * @return
     */
    @PutMapping("/task/remind-again/{id}")
    @Log(title = "医院后台-再次提醒任务", businessType = BusinessType.QUERY)
    public AjaxResult<Boolean> remindTaskAgain(@PathVariable("id") Long id) {
        return AjaxResult.success(followUpService.remindTaskAgain(id));
    }


    /**
     * 医院后台 查看患者事件执行记录详情
     *
     * @param dto 查询详情
     * @return
     */
    @PostMapping("/task/event-history/info")
    @Log(title = "医院后台-查看患者事件执行记录详情", businessType = BusinessType.QUERY)
    public AjaxResult<TaskEventVO> getEventHistoryInfo(@RequestBody PatientTaskEventHistoryDTO dto) {
        return AjaxResult.success(followUpService.getEventHistoryInfo(dto));
    }

}