package com.puree.followup.domain.followup.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访分项任务配置表 DTO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemTaskDTO {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务排序
     */
    private Integer sortNum;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 提醒时间，格式为：HH:mm
     */
    private String executeTime;

//    /**
//     * 分项任务执行周期的类型：custom:自定义 、periodCycle:周期循环、weekCycle:固定周循环
//     */
//    private String intervalType;

    /**
     * 第几天/每隔几天循环/每隔几周循环
     */
    private Integer intervals;

    /**
     * 周循环时记录一周内循环的哪些天(周1-周7)，逗号隔开
     */
    private String weekDays;

    /**
     * 执行事件
     */
    private List<TaskEvent> events;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 分项编辑时的行为：add、update、del
     */
    private String action;

}