package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/7/7 17:55
 * @Description  入组原因：0.自动添加 1.手动添加
 */
public enum FollowUpJoinReasonTypeEnum {

    AUTO(0, "auto", "(自动添加)"),
    MANUAL(1, "manual", "(手动添加)");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpJoinReasonTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpJoinReasonTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpJoinReasonTypeEnum[] metaArr = FollowUpJoinReasonTypeEnum.values();
        for (FollowUpJoinReasonTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpJoinReasonTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpJoinReasonTypeEnum[] metaArr = FollowUpJoinReasonTypeEnum.values();
        for (FollowUpJoinReasonTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
