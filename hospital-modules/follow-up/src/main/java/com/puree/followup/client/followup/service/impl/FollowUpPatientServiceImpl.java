package com.puree.followup.client.followup.service.impl;

import cn.hutool.http.HttpStatus;
import com.puree.followup.admin.classification.mapper.ClassificationMapper;
import com.puree.followup.admin.followup.mapper.FollowUpInviteMapper;
import com.puree.followup.admin.followup.mapper.FollowUpItemMapper;
import com.puree.followup.admin.followup.mapper.FollowUpItemTaskMapper;
import com.puree.followup.admin.followup.mapper.FollowUpJoinRuleMapper;
import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRemarkMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskEventHistoryMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskHistoryMapper;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.admin.followup.service.IPatientTaskHistoryService;
import com.puree.followup.client.followup.service.IFollowUpPatientService;
import com.puree.followup.domain.followup.dto.FollowUpInsertDTO;
import com.puree.followup.domain.followup.dto.PatientFollowUpQueryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskEventHistoryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskHistoryQueryDTO;
import com.puree.followup.domain.followup.dto.QRJoinInDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.FollowUpItem;
import com.puree.followup.domain.followup.model.FollowUpItemTask;
import com.puree.followup.domain.followup.model.PatientFollowUpItemRecord;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.PatientJoinInResult;
import com.puree.followup.domain.followup.model.PatientTaskEventHistory;
import com.puree.followup.domain.followup.model.PatientTaskHistory;
import com.puree.followup.domain.followup.model.TaskAndEventStatus;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.domain.followup.vo.FollowUpInvitePageVO;
import com.puree.followup.domain.followup.vo.PatientFollowUpListVO;
import com.puree.followup.domain.followup.vo.PatientTaskEventHistoryVO;
import com.puree.followup.domain.followup.vo.PatientTaskHistoryTreeVO;
import com.puree.followup.domain.followup.vo.PatientTaskHistoryVO;
import com.puree.followup.domain.followup.vo.TaskEventVO;
import com.puree.followup.enums.FollowUpExpireTypeEnum;
import com.puree.followup.enums.FollowUpJoinReasonTypeEnum;
import com.puree.followup.enums.FollowUpJoinStatusEnum;
import com.puree.followup.enums.FollowUpJoinTypeEnum;
import com.puree.followup.enums.FollowUpSelectPatientTypeEnum;
import com.puree.followup.enums.PatientEventStatusEnum;
import com.puree.followup.enums.PatientTaskStatusEnum;
import com.puree.followup.enums.RecommendSourceEnum;
import com.puree.followup.enums.TaskEventTypeEnum;
import com.puree.followup.enums.TaskMessageTypeEnum;
import com.puree.followup.enums.TaskRemindStatusEnum;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.hospital.app.api.RemoteDoctorService;
import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.app.api.model.BusDoctorVo;
import com.puree.hospital.business.api.RemoteBusPatientService;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospital;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.text.UUID;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import com.puree.hospital.five.api.RemoteServicePackService;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import com.puree.hospital.followup.api.model.AppletQrCodeVO;
import com.puree.hospital.shop.api.RemoteShopGoodsService;
import com.puree.hospital.shop.api.model.BusShopGoods;
import com.puree.hospital.tool.api.RemoteArticleService;
import com.puree.hospital.tool.api.model.Article;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/4/29 14:54
 * @description  患者随访 服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FollowUpPatientServiceImpl implements IFollowUpPatientService {

    private final FollowUpMapper followUpMapper;
    private final FollowUpJoinRuleMapper followUpJoinRuleMapper;
    private final FollowUpItemMapper followUpItemMapper;
    private final FollowUpInviteMapper followUpInviteMapper;
    private final PatientFollowUpRecordMapper patientFollowUpRecordMapper;
    private final PatientFollowUpRemarkMapper patientFollowUpRemarkMapper;
    private final PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper;
    private final PatientTaskEventHistoryMapper patientTaskEventHistoryMapper;
    private final PatientTaskHistoryMapper patientTaskHistoryMapper;
    private final FollowUpItemTaskMapper followUpItemTaskMapper;
    private final ClassificationMapper classificationMapper;
    private final RemoteArticleService remoteArticleService;
    private final RemoteBusPatientService remoteBusPatientService;
    private final IPatientTaskHistoryService patientTaskHistoryService;
    private final IFollowUpService followUpService;
    private final RemotePatientFamilyService remotePatientFamilyService;
    private static final String USER_NOT_FOUND = "userNotFound";
    @Value("${task.history.query.maxIntervalDays:31}")
    private Integer maxIntervalDays ; //查询患者随访记录时的最大时间跨度
    private final RemoteDoctorService remoteDoctorService;
    private final RemoteHospitalService remoteHospitalService;
    private final RemoteShopGoodsService remoteShopGoodsService;
    private final RemoteServicePackService remoteServicePackService;
    @Autowired
    @Lazy
    private IFollowUpPatientService followUpPatientService;

    /**
     * 患者端 事件完成状态修改
     *
     * @param dto 事件详情
     * @param status 事件完成状态
     * @return
     */
    @Override
    public Boolean updateEventStatus(PatientTaskEventHistoryDTO dto, TaskAndEventStatus status) {
        Long id = dto.getId();
        Long hospitalId = dto.getHospitalId();
        log.debug("事件ID：{}", id);
        PatientTaskEventHistory eventHistory = patientTaskEventHistoryMapper.getById(id);
        if(eventHistory == null)
            throw new ServiceException("随访任务不存在");

        //已完成、已过期、已终止的任务不做更新
        PatientTaskHistory taskHistory = patientTaskHistoryMapper.getById(eventHistory.getTaskHistoryId());
        status.setEventStatus(PatientEventStatusEnum.getByIndex(eventHistory.getEventFinishStatus()).getName());
        status.setTaskStatus(PatientTaskStatusEnum.getByIndex(taskHistory.getTaskStatus()).getName());

        if(taskHistory.getTaskStatus().equals(PatientTaskStatusEnum.FINISHED.getIndex())
                || taskHistory.getTaskStatus().equals(PatientTaskStatusEnum.HASEXPIRED.getIndex())
                ||taskHistory.getTaskStatus().equals(PatientTaskStatusEnum.TERMINATED.getIndex())){
            return false;
        }

        //更新事件状态和任务状态
        if(eventHistory.getEventFinishStatus().equals(PatientEventStatusEnum.ISCOMING.getIndex())
                || eventHistory.getEventFinishStatus().equals(PatientEventStatusEnum.ONGOING.getIndex())){

            PatientTaskEventHistory update = new PatientTaskEventHistory();
            update.setId(eventHistory.getId());
            update.setEventFinishStatus(PatientEventStatusEnum.FINISHED.getIndex());
            if(FillQuestionStatusEnum.FILLING.getIndex().equals(dto.getFillStatus())) //当事件类型为问卷时，且状态为填写中，那么该事件的状态是已进行中(未完成)
                update.setEventFinishStatus(PatientEventStatusEnum.ONGOING.getIndex());
            update.setEventExecuteTime(new Date());
            patientTaskEventHistoryMapper.updateById(update);
            status.setEventStatus(PatientEventStatusEnum.FINISHED.getName());
            status.setEventStatus(PatientEventStatusEnum.getByIndex(update.getEventFinishStatus()).getName());
            log.debug("更新事件状态结果：{}", eventHistory);

            //更新任务状态
            FollowUpItemTask itemTask = followUpItemTaskMapper.getById(taskHistory.getTaskId());
            Boolean result = updateTaskStatus(taskHistory, itemTask);
            status.setTaskStatus(PatientTaskStatusEnum.getByIndex(taskHistory.getTaskStatus()).getName());
            return result;
        }
        return false;
    }


    /**
     * 患者端 事件完成状态批量修改(非问卷)
     *
     * @param ids 事件记录id列表
     * @param hospitalId 医院id
     * @return 是否更新成功
     */
    @Override
    public Boolean updateEventsStatus(List<Long> ids, Long hospitalId) {
        log.debug("事件完成状态批量处理：ids列表：{}，hospitalId：{}", ids, hospitalId);
        //关联查询：获取事件执行历史记录列表
        PatientTaskEventHistory query = new PatientTaskEventHistory();
        query.setEventHistoryIds(ids);
        query.setHospitalId(hospitalId);
        List<PatientTaskEventHistory> eventHistories = patientTaskEventHistoryMapper.getListByIds(query);
        if(CollectionUtils.isEmpty(eventHistories)){
            return false;
        }
        //存放需要更新任务状态的任务执行历史记录(使用Map存储，是因为不同的事件可能在同一个任务下)
        Map<Long, PatientTaskHistory> updateTasksMap = new HashMap<>();
        List<Long> updateEventIds = new ArrayList<>();//存放需要更新事件状态的事件执行历史记录列表
        for (PatientTaskEventHistory eventHistory : eventHistories) {
            //已完成、已过期、已终止的任务不做更新
            if(eventHistory.getTaskStatus().equals(PatientTaskStatusEnum.FINISHED.getIndex())
                    || eventHistory.getTaskStatus().equals(PatientTaskStatusEnum.HASEXPIRED.getIndex())
                    ||eventHistory.getTaskStatus().equals(PatientTaskStatusEnum.TERMINATED.getIndex())){
                continue;
            }
            //只更新“未开始”和“待完成”的两种事件状态
            if(!eventHistory.getEventFinishStatus().equals(PatientEventStatusEnum.ISCOMING.getIndex())
                    && !eventHistory.getEventFinishStatus().equals(PatientEventStatusEnum.ONGOING.getIndex())) {
                continue;
            }
            //要更新的事件
            updateEventIds.add(eventHistory.getId());
            //要更新的任务
            PatientTaskHistory updateTask = new PatientTaskHistory();
            updateTask.setId(eventHistory.getTaskHistoryId());
            updateTask.setHospitalId(hospitalId);
            updateTask.setTaskId(eventHistory.getTaskId());
            updateTask.setTaskStatus(eventHistory.getTaskStatus());
            updateTasksMap.put(eventHistory.getTaskHistoryId(), updateTask);
        }

        //如果没有需要更新的事件或任务状态，则直接返回
        if(CollectionUtils.isEmpty(updateEventIds)){
            log.debug("没有需要更新的事件或任务：ids列表：{}，hospitalId：{}", ids, hospitalId);
            return true;
        }
        //批量更新事件状态为：已完成
        PatientTaskEventHistory update = new PatientTaskEventHistory();
        update.setIds(updateEventIds);
        update.setEventFinishStatus(PatientEventStatusEnum.FINISHED.getIndex());
        update.setEventExecuteTime(new Date());
        patientTaskEventHistoryMapper.updateByIds(update);
        //异步处理任务状态的更新
        followUpPatientService.syncUpdateTaskStatus(updateTasksMap, hospitalId);
        return true;
    }

    /**
     * 异步处理任务状态的更新
     *
     * @param updateTasksMap map结构存储的任务列表
     * @param hospitalId 医院id
     * @return
     */
    @Async("taskExecutor")
    @Override
    public void syncUpdateTaskStatus(Map<Long, PatientTaskHistory> updateTasksMap, Long hospitalId) {
        if(CollectionUtils.isEmpty(updateTasksMap)){
            return;
        }
        //获取task的ID列表，并批量获取task任务列表
        Set<Long> taskIds = updateTasksMap.values().stream()
                .map(PatientTaskHistory::getTaskId)
                .collect(Collectors.toSet());
        List<FollowUpItemTask> itemTasks = followUpItemTaskMapper.getEventsListByIds(new ArrayList<>(taskIds));

        //转换成map结构，方便后续操作
        Map<Long, FollowUpItemTask> itemTaskMap = itemTasks.stream()
                .collect(Collectors.toMap(FollowUpItemTask::getId, item -> item));

        //更新任务状态
        updateTasksMap.forEach((id, taskHistory) -> {
            updateTaskStatus(taskHistory, itemTaskMap.get(taskHistory.getTaskId()));
        });
    }


    /**
     * 患者端 二维码加入随访
     *
     * @param followUp 随访信息
     * @return 是否加入成功
     */
    @Override
    public AjaxResult<AppletQrCodeVO> qrcodeJoinIn(FollowUp followUp, QRJoinInDTO dto) {
        //获取就诊人id：获取该账户下只为本人的那个就诊人
        Long patientId = remotePatientFamilyService.queryInfoByUserId(dto.getUserId(), followUp.getHospitalId()).getData();
        if(patientId == null)
            return AjaxResult.error(USER_NOT_FOUND);

        FollowUpInsertDTO insertDTO = new FollowUpInsertDTO();
        insertDTO.setFollowUpId(followUp.getId());
        insertDTO.setHospitalId(followUp.getHospitalId());
        insertDTO.setUserId(dto.getUserId());//患者id（账户id）
        insertDTO.setPatientId(patientId);//就诊人id
        insertDTO.setJoinType(FollowUpJoinTypeEnum.getByName(dto.getJoinType()).getIndex() + 2);
        insertDTO.setJoinStatus(FollowUpJoinTypeEnum.getByName(dto.getJoinType()).equals(FollowUpJoinTypeEnum.AUTO) ? FollowUpJoinStatusEnum.FOLLOWUPING.getIndex() : FollowUpJoinStatusEnum.INVITING.getIndex());
        insertDTO.setJoinReason(FollowUpJoinReasonTypeEnum.MANUAL.getDesc() + FollowUpSelectPatientTypeEnum.SMALL_PROGRAM_CODE.getDesc());
        insertDTO.setIsRepeatedMsgSend(Boolean.TRUE);
        //构建rediskey
        insertDTO.setRedisKey("joinin:" + dto.getHospitalId() + ":" + dto.getId() + ":" + dto.getUserId() + ":" + patientId);
        //加入随访
        PatientJoinInResult result = followUpService.patientJoinIn(insertDTO);
        AppletQrCodeVO vo = new AppletQrCodeVO();
        vo.setFollowUpRecordId(result.getFollowUpRecordId());
        vo.setPatientId(result.getPatientId());
        vo.setUserId(result.getUserId());
        if(result.getIsSuccess())
            return AjaxResult.success(vo, "随访加入成功");

        return AjaxResult.error(HttpStatus.HTTP_OK,vo,result.getMessage());
    }

    /**
     * 患者端 获取患者加入的随访列表
     *
     * @param dto 查询条件
     * @return 患者加入的随访列表
     */
    @Override
    public List<PatientFollowUpListVO> followUpList(PatientFollowUpQueryDTO dto) {
        PatientFollowUpRecord query = new PatientFollowUpRecord();
        query.setHospitalId(dto.getHospitalId());
        query.setPatientId(dto.getPatientId());
        query.setUserId(SecurityUtils.getUserId());
        //获取已加入的随访列表（不包括邀请中和已拒绝的）。
        //注意：患者可能反复加入多次同一个随访，这个时候需要去重处理，就是只展示一次该随访
        List<PatientFollowUpListVO> vos = patientFollowUpRecordMapper.hasJoinedFollowUp(query);

        //基于followUpId属性作为键进行去重（使用LinkedHashMap保证其有序）
        Map<Long, PatientFollowUpListVO> uniqueFollowUpById = new LinkedHashMap<>();
        for (PatientFollowUpListVO vo : vos) {
            //如果非长期随访，获取随访的“加入随访X天结束”
            if(!FollowUpExpireTypeEnum.LONG_TERM.getIndex().equals(vo.getExpireType())){
                vo.setDays(vo.getExpireDay());
            }
            // 如果Map中不存在该age的Student，则添加进去
            uniqueFollowUpById.putIfAbsent(vo.getFollowUpId(), vo);
        }

        // 将Map转换回List
        vos = new ArrayList<>(uniqueFollowUpById.values());

        return vos;
    }

    /**
     * 获取患者的健康随访列表（按日期的列表树）
     *
     * @param dto 查询条件
     * @return 患者的随访记录列表
     */
    @Override
    public AjaxResult<List<PatientTaskHistoryTreeVO>> taskHistory(PatientTaskHistoryQueryDTO dto) {

        //返回结果
        List<PatientTaskHistoryTreeVO> treeVOS = new ArrayList<>();
        List<PatientTaskHistoryVO> vos = patientTaskHistoryService.getPatientTaskAndEventList(dto);
        Map<String, List<PatientTaskHistoryVO>> groupedByBeginDay = vos.stream().collect(Collectors.groupingBy(PatientTaskHistoryVO::getBeginDay));
        List<Map.Entry<String, List<PatientTaskHistoryVO>>> sortedEntries = new ArrayList<>(groupedByBeginDay.entrySet());
        sortedEntries.sort(Map.Entry.comparingByKey());

        //返回结果封装
        handleTreeVOS(sortedEntries, treeVOS);
        log.debug("treeVOS：{}", treeVOS);
        AjaxResult result = AjaxResult.success(treeVOS);
        result.add("beginDate", dto.getJoinInBeginDateStr());
        result.add("endDate", dto.getJoinInEndDateStr());
        result.add("isEnd", false);
        result.add("maxIntervalDays", maxIntervalDays);
        return result;
    }

    /**
     * 封装日期树返回结果
     *
     * @param sortedEntries 按日期分组的列表
     * @param treeVOS 结果集
     */
    public void handleTreeVOS(List<Map.Entry<String, List<PatientTaskHistoryVO>>> sortedEntries, List<PatientTaskHistoryTreeVO> treeVOS){
        //记录今天在列表中的索引值
        Integer todayIndex = 0;
        //遍历列表的时候，记录距离今天最小的天数的绝对值
        Integer minBetweenDays = Integer.MAX_VALUE;
        Date today = DateUtils.clearTime(new Date());
        for(int i = 0 ; i < sortedEntries.size() ; i++){
            Map.Entry<String, List<PatientTaskHistoryVO>> entry = sortedEntries.get(i);
            PatientTaskHistoryTreeVO treeVO = new PatientTaskHistoryTreeVO();
            treeVO.setId(UUID.randomUUID().toString());
            treeVO.setShowData(entry.getKey());

            //排序：按任务开始时间升序展示并且过滤掉已终止的任务
            List<PatientTaskHistoryVO> values = entry.getValue();
            if(!org.springframework.util.CollectionUtils.isEmpty(values))
                values = values.stream()
                        .sorted(Comparator.naturalOrder())
                        .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(values))//任务为空的日期不展示
                continue;

            //关于患者端的事件状态：需要根据任务和事件状态来联合判断事件的状态，规则如下
            //0、任务未开始：事件都为即将开始状态
            //1、任务进行中：事件按照自己的状态
            //2、任务已完成：事件状态也都为已完成
            //3、任务已过期：未完成的事件为已过期，已完成的事件为已完成
            //4、已终止：未完成的事件为已终止，已完成的时间为已完成
            values.forEach(task -> {
                List<PatientTaskEventHistoryVO> events = task.getChildren();
                if(task.getTaskStatus().equals(PatientTaskStatusEnum.ISCOMING.getName()))
                    events.forEach(event -> event.setPatientEventFinishStatus(PatientEventStatusEnum.ISCOMING.getName()));
                else if(task.getTaskStatus().equals(PatientTaskStatusEnum.ONGOING.getName()) || task.getTaskStatus().equals(PatientTaskStatusEnum.FINISHED.getName()))
                    events.forEach(event -> event.setPatientEventFinishStatus(event.getEventFinishStatus()));
                else if(task.getTaskStatus().equals(PatientTaskStatusEnum.HASEXPIRED.getName()))
                    events.forEach(event -> {
                        if(event.getEventFinishStatus().equals(PatientEventStatusEnum.ONGOING.getName()))
                            event.setPatientEventFinishStatus(PatientEventStatusEnum.HASEXPIRED.getName());
                        else
                            event.setPatientEventFinishStatus(event.getEventFinishStatus());
                    });
                else if(task.getTaskStatus().equals(PatientTaskStatusEnum.TERMINATED.getName()))
                    events.forEach(event -> {
                        if(event.getEventFinishStatus().equals(PatientEventStatusEnum.ONGOING.getName()))
                            event.setPatientEventFinishStatus(PatientEventStatusEnum.TERMINATED.getName());
                        else
                            event.setPatientEventFinishStatus(event.getEventFinishStatus());
                    });
            });

            treeVO.setChildren(values);
            treeVOS.add(treeVO);
            Integer betweenDays = DateUtils.getBetweenDays(entry.getKey(), today);
            if(betweenDays < minBetweenDays){
                minBetweenDays = betweenDays;
                todayIndex = treeVOS.size()-1;
            }
        }
        if(!CollectionUtils.isEmpty(treeVOS)){
            PatientTaskHistoryTreeVO vo = treeVOS.get(todayIndex);
            vo.setIsToday("isToday");
        }
    }

    /**
     * 提前完成事件
     *
     * @param dto 要提前完成的事件
     * @return 是否成功
     */
    @Override
    public Boolean finishEventAhead(PatientTaskEventHistoryDTO dto, TaskAndEventStatus status) {

        //1、插入随访任务历史记录表
        PatientTaskHistory taskHistory = new PatientTaskHistory();
        BeanUtils.copyProperties(dto, taskHistory);
        taskHistory.setId(null);
        FollowUpItemTask itemTask = followUpItemTaskMapper.getById(dto.getTaskId());
        taskHistory.setTaskName(itemTask.getTaskName());
        taskHistory.setBeginDay(dto.getTaskBeginDay());
        taskHistory.setTaskRemindStatus(TaskRemindStatusEnum.NOTREMIND.getIndex());
        taskHistory.setTaskStatus(PatientTaskStatusEnum.ONGOING.getIndex());
        taskHistory.setIsTerminateFuture(YesNoEnum.NO.getCode());
        taskHistory.setCreateBy(SecurityUtils.getUsername());
        taskHistory.setCreateTime(new Date());
        taskHistory.setUpdateBy(SecurityUtils.getUsername());
        taskHistory.setUpdateTime(new Date());
        taskHistory.setIsDelete(YesNoEnum.NO.getCode());
        try{
            patientTaskHistoryMapper.insert(taskHistory);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){ //唯一索引冲突
            log.warn("随访任务历史表重复插入异常, 医院id：{}, followUpRecordId：{}, 任务id：{} , ", taskHistory.getHospitalId(), taskHistory.getFollowUpRecordId(), taskHistory.getTaskId(), e);
            //使用唯一索引查询
            PatientTaskHistory query = new PatientTaskHistory();
            query.setHospitalId(taskHistory.getHospitalId());
            query.setFollowUpRecordId(taskHistory.getFollowUpRecordId());
            query.setTaskId(taskHistory.getTaskId());
            query.setBeginDay(taskHistory.getBeginDay());
            taskHistory = patientTaskHistoryMapper.getOne(query);
        }

        //2、插入随访事件历史记录表
        PatientTaskEventHistory eventHistory = new PatientTaskEventHistory();
        BeanUtils.copyProperties(dto, eventHistory);
        eventHistory.setId(null);
        eventHistory.setTaskHistoryId(taskHistory.getId());
        eventHistory.setEventType(TaskEventTypeEnum.getEnumByName(dto.getEventType()).getIndex());
        eventHistory.setEventExecuteTime(new Date());
        eventHistory.setRemindTime(dto.getRemindTime());
        eventHistory.setEventRemindStatus(TaskRemindStatusEnum.NOTREMIND.getIndex());
        eventHistory.setEventFinishStatus(PatientEventStatusEnum.FINISHED.getIndex());
        if(FillQuestionStatusEnum.FILLING.getIndex().equals(dto.getFillStatus())) //当事件类型为问卷时，且状态为填写中，那么该事件的状态是已进行中(未完成)
            eventHistory.setEventFinishStatus(PatientEventStatusEnum.ONGOING.getIndex());
        eventHistory.setIsTerminateFuture(YesNoEnum.NO.getCode());
        eventHistory.setCreateBy(SecurityUtils.getUsername());
        eventHistory.setCreateTime(new Date());
        eventHistory.setUpdateBy(SecurityUtils.getUsername());
        eventHistory.setUpdateTime(new Date());
        eventHistory.setIsDelete(YesNoEnum.NO.getCode());
        try{
            patientTaskEventHistoryMapper.insert(eventHistory);
            dto.setId(eventHistory.getId());
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){ //唯一索引冲突
            log.warn("随访事件历史表重复插入异常, 医院id：{}, taskHistoryId：{}, 事件id：{} , ", eventHistory.getHospitalId(), eventHistory.getTaskHistoryId(), eventHistory.getEventId(), e);

            PatientTaskEventHistory query = new PatientTaskEventHistory();
            query.setHospitalId(taskHistory.getHospitalId());
            query.setTaskHistoryId(taskHistory.getId());
            query.setEventId(dto.getEventId());
            PatientTaskEventHistory oldEventHistory = patientTaskEventHistoryMapper.getOne(query);
            if(!eventHistory.getEventFinishStatus().equals(oldEventHistory.getEventFinishStatus())){
                oldEventHistory.setEventFinishStatus(eventHistory.getEventFinishStatus());
                //设置事件完成时间
                if(PatientEventStatusEnum.FINISHED.getIndex().equals(eventHistory.getEventFinishStatus())
                        && Objects.isNull(oldEventHistory.getEventExecuteTime())){
                    oldEventHistory.setEventExecuteTime(new Date());
                }
                patientTaskEventHistoryMapper.updateById(oldEventHistory);
            }
            eventHistory = oldEventHistory;
            dto.setId(eventHistory.getId());
        }

        //3、更新任务状态为已完成
        updateTaskStatus(taskHistory, itemTask);
        status.setEventStatus(PatientEventStatusEnum.getByIndex(eventHistory.getEventFinishStatus()).getName());
        status.setTaskStatus(PatientTaskStatusEnum.getByIndex(taskHistory.getTaskStatus()).getName());
        return true;
    }


    //更新任务状态
    public Boolean updateTaskStatus(PatientTaskHistory taskHistory, FollowUpItemTask itemTask){
        //获取该任务下的各事件执行情况
        PatientTaskEventHistory eventHistoryQuery = new PatientTaskEventHistory();
        eventHistoryQuery.setHospitalId(taskHistory.getHospitalId());
        eventHistoryQuery.setTaskHistoryId(taskHistory.getId());
        List<PatientTaskEventHistory> events = patientTaskEventHistoryMapper.getList(eventHistoryQuery);
        int eventNum = 1;
        if(itemTask != null)
            eventNum = itemTask.getEvents().size();
        if(eventNum == events.size()){ //确保所有的事件都落库
            Boolean isFinished = true;
            for (PatientTaskEventHistory event : events) {
                if (!event.getEventFinishStatus().equals(PatientEventStatusEnum.FINISHED.getIndex()))
                    isFinished = false;
            }
            //更新任务状态为已完成
            if (isFinished && (taskHistory.getTaskStatus().equals(PatientTaskStatusEnum.ISCOMING.getIndex())
                    || taskHistory.getTaskStatus().equals(PatientTaskStatusEnum.ONGOING.getIndex()))) {
                taskHistory.setTaskStatus(PatientTaskStatusEnum.FINISHED.getIndex());
                patientTaskHistoryMapper.updateById(taskHistory);
                log.info("更新任务状态：{}", taskHistory);
            }
        }
        return true;
    }



    /**
     * 查看患者事件执行记录详情
     *
     * @param dto 查询详情
     * @return 详情
     */
    @Override
    public TaskEventVO getEventHistoryInfo(PatientTaskEventHistoryDTO dto) {
        TaskEvent event = dto.getEvents();
        TaskEventVO vo = new TaskEventVO();
        BeanUtils.copyProperties(event, vo);
        vo.setShowData(event.getMessage());

        //1、发送消息且内容为图片
        if(TaskEventTypeEnum.MESSAGE.getIndex().equals(event.getEventType()) && TaskMessageTypeEnum.PIC.getIndex().equals(event.getMessageType()))
            vo.setShowData("图片");

        //2、发送患教
        if(TaskEventTypeEnum.TUTORIAL.getIndex().equals(event.getEventType())){
            Article article = remoteArticleService.overviewArticle(event.getSendId()).getData();
            vo.setArticle(article);
            vo.setShowData(article == null ? null : article.getTitle());
        }

        //3、TODO 发送问卷


        //4、发送推荐，包括四种类型： 推荐医生、推荐科室、推荐商品、推荐服务包
        if(TaskEventTypeEnum.RECOMMEND.getIndex().equals(event.getEventType()) && StringUtils.isNotEmpty(event.getRecommendId())){
            switch(RecommendSourceEnum.getRecommendSourceEnumByIndex(event.getRecommendSourceId())) {
                case DOCTOR: //推荐医生
                    BusDoctorVo busDoctorVo = remoteDoctorService.personalData(SecurityUtils.getHospitalId(), Long.valueOf(event.getRecommendId())).getData();
                    if(busDoctorVo != null) {
                        vo.setRecommendName(busDoctorVo.getFullName());
                        vo.setDoctorTitleValue(busDoctorVo.getTitleValue());
                        BusHospital busHospital = remoteHospitalService.getHospitalInfo(dto.getHospitalId()).getData();
                        if(busHospital != null)
                            vo.setHospitalNameByDoctor(busHospital.getHospitalName());
                    }
                    break;
                case DEPARTMENT: //推荐科室
                    BusHospital busHospital = remoteHospitalService.getHospitalInfo(SecurityUtils.getHospitalId()).getData();
                    if(busHospital != null)
                        vo.setHospitalName(busHospital.getHospitalName());
                    break;
                case GOODS: //推荐商品
                    BusShopGoods goods = remoteShopGoodsService.getDetail(Long.valueOf(event.getRecommendId())).getData();
                    vo.setGoods(goods);
                    break;
                case SERVICE_PACK: //推荐服务包
                    List<BusFiveServicePackVO> packs = remoteServicePackService.getServicePacksByIds(event.getRecommendId()).getData();
                    if(!CollectionUtils.isEmpty(packs))
                        vo.setServicePack(packs.get(0));
                    break;
            }
        }

        //保存任务和事件的完成状态
        TaskAndEventStatus taskAndEventStatus = new TaskAndEventStatus();

        //更新消息和患教的完成状态
        //1、提前完成（只有今天&&未提醒的消息可以提前完成，也即是未落库的事件）  注意有个细节：1.0版本随访的代码调整之后的逻辑是：今天超过提醒时间的任务(会落库)，也可以符合如下判断条件
        if(dto.getEventRemindStatus().equals(TaskRemindStatusEnum.NOTREMIND.getName())
                && dto.getEventFinishStatus().equals(PatientEventStatusEnum.ONGOING.getName())){
            log.debug("提前主动完成事件：{}", dto);
            finishEventAhead(dto, taskAndEventStatus);
        }
        //2、正常完成：对于那些已提醒且未完成的任务
        if(dto.getEventRemindStatus().equals(TaskRemindStatusEnum.REMINDED.getName())
                && dto.getEventFinishStatus().equals(PatientEventStatusEnum.ONGOING.getName())){
            log.debug("完成事件：{}", dto);
            updateEventStatus(dto, taskAndEventStatus);
        }

        //事件完成后，设置事件和任务的最终状态
        if(StringUtils.isEmpty(taskAndEventStatus.getEventStatus())){
            taskAndEventStatus.setEventStatus(dto.getEventFinishStatus());
            taskAndEventStatus.setTaskStatus(PatientTaskStatusEnum.FINISHED.getName());
            PatientTaskEventHistory eventHistory = patientTaskEventHistoryMapper.getById(dto.getId());
            if(eventHistory != null){
                //已完成、已过期、已终止的任务不做更新
                PatientTaskHistory taskHistory = patientTaskHistoryMapper.getById(eventHistory.getTaskHistoryId());
                taskAndEventStatus.setTaskStatus(PatientTaskStatusEnum.getByIndex(taskHistory.getTaskStatus()).getName());
            }
        }
        vo.setEventStatus(taskAndEventStatus.getEventStatus());
        vo.setTaskStatus(taskAndEventStatus.getTaskStatus());
        handleEventAndTaskStatus(vo);
        vo.setId(dto.getId());
        return vo;
    }

    /**
     * 患者端 随访邀请页
     *
     * @param record 患者随访记录信息
     * @param hospitalId 医院id
     * @return 随访邀请结果
     */
    @Override
    public FollowUpInvitePageVO invitePage(PatientFollowUpRecord record, Long hospitalId) {

        FollowUp followUp = followUpMapper.getById(record.getFollowUpId());

        //组装随访邀请页数据
        FollowUpInvitePageVO result = new FollowUpInvitePageVO();
        result.setId(followUp.getId());
        result.setRecordId(record.getId());
        result.setName(followUp.getName());
        result.setDesc(followUp.getDesc());
        result.setHospitalId(hospitalId);
        result.setUserId(record.getUserId());
        result.setPatientId(record.getPatientId());
        result.setJoinStatus(FollowUpJoinStatusEnum.getByIndex(record.getJoinStatus()).getName());
        result.setIsEnable(followUp.getIsEnable().equals(YesNoEnum.NO.getCode()) ? false : true);
        result.setIsPublish(followUp.getIsPublish().equals(YesNoEnum.NO.getCode()) ? false : true);
        return result;
    }

    /**
     * 患者端 随访邀请页 患者拒绝加入随访
     *
     * @param record 患者加入的随访记录id
     * @param hospitalId 医院id
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refuseJoining(PatientFollowUpRecord record, Long hospitalId) {
        if(record.getJoinStatus().equals(FollowUpJoinStatusEnum.INVITING.getIndex())){
            //更新随访状态为已拒绝
            record.setJoinStatus(FollowUpJoinStatusEnum.REFUSED.getIndex());
            record.setEndTime(new Date());
            record.setUpdateTime(new Date());
            patientFollowUpRecordMapper.updateById(record);
            return true;
        }
        return false;
    }

    /**
     * 患者端 随访邀请页 患者确认加入随访
     *
     * @param record 患者随访记录信息
     * @param followUp 要加入的随访信息
     * @return 操作结果
     */
    @Override
    public Boolean confirmJoining(PatientFollowUpRecord record, FollowUp followUp) {

        //更新随访状态为随访中
        record.setJoinStatus(FollowUpJoinStatusEnum.FOLLOWUPING.getIndex());
        record.setJoinTime(new Date()); //患者确认加入随访的时间为入组的时间
        record.setUpdateTime(new Date());
        patientFollowUpRecordMapper.updateById(record);

        //患者加入分项
        //1、计算随访开始/结束时间，以及分项开始/结束时间，这个时间是相对于此刻(患者确认加入随访的当天)
        Date followUpBeginDate = DateUtils.getToday();
        //获取随访下的分项列表（有效分项）
        List<FollowUpItem> selectedItems = followUpService.getValidFollowUpItem(followUp, followUpBeginDate, null);
//        if(CollectionUtils.isEmpty(selectedItems))
//            throw new ServiceException("随访加入失败，无法获取到可用分项");

        //2、加入分项
        List<PatientFollowUpItemRecord> itemRecords = new ArrayList<>();
        selectedItems.forEach(selectedItem -> {
            PatientFollowUpItemRecord itemRecord = followUpService.getFollowUpItemRecord(selectedItem, record, followUp.getExpireType(), followUp.getCreateBy());
            itemRecords.add(itemRecord);
        });
        if(!CollectionUtils.isEmpty(itemRecords)) {
            patientFollowUpItemRecordMapper.batchInsert(itemRecords);
        }
        //3、查询患者加入本随访当天且超过提醒时间的任务列表，并使其入库
        followUpService.updateUnremindedTasks(record, new Date());
        return true;
    }

    /**
     * 患者端 根据事件记录id查看事件和任务的状态
     *
     * @param id 事件记录id
     * @return
     */
    @Override
    public TaskEventVO getEventAndTaskStatus(Long id) {

        TaskEventVO vo = new TaskEventVO();

        PatientTaskEventHistory eventHistory = patientTaskEventHistoryMapper.getById(id);
        if(eventHistory == null)
            throw new ServiceException("任务不存在");

        //已完成、已过期、已终止的任务不做更新
        PatientTaskHistory taskHistory = patientTaskHistoryMapper.getById(eventHistory.getTaskHistoryId());

        vo.setEventStatus(PatientEventStatusEnum.getByIndex(eventHistory.getEventFinishStatus()).getName());
        vo.setTaskStatus(PatientTaskStatusEnum.getByIndex(taskHistory.getTaskStatus()).getName());

        handleEventAndTaskStatus(vo);
        return vo;
    }

    /**
     * 任务和事件的完成状态处理
     *
     * @param vo 任务和事件的完成状态
     * @return
     */
    private void handleEventAndTaskStatus(TaskEventVO vo){
        //关于患者端的事件状态：需要根据任务和事件状态来联合判断事件的状态，规则如下
        //0、任务未开始：事件都为即将开始状态
        //1、任务进行中：事件按照自己的状态
        //2、任务已完成：事件状态也都为已完成
        //3、任务已过期：未完成的事件为已过期，已完成的事件为已完成
        //4、已终止：未完成的事件为已终止，已完成的时间为已完成
        if(vo.getTaskStatus().equals(PatientTaskStatusEnum.ISCOMING.getName()))
            vo.setEventStatus(PatientEventStatusEnum.ISCOMING.getName());
        else if(vo.getTaskStatus().equals(PatientTaskStatusEnum.HASEXPIRED.getName()))
            if(vo.getEventStatus().equals(PatientEventStatusEnum.ONGOING.getName()))
                vo.setEventStatus(PatientEventStatusEnum.HASEXPIRED.getName());
            else
                vo.setEventStatus(PatientEventStatusEnum.FINISHED.getName());
        else if(vo.getTaskStatus().equals(PatientTaskStatusEnum.TERMINATED.getName()))
            if(vo.getEventStatus().equals(PatientEventStatusEnum.ONGOING.getName()))
                vo.setEventStatus(PatientEventStatusEnum.TERMINATED.getName());
            else
                vo.setEventStatus(PatientEventStatusEnum.FINISHED.getName());
    }


    /**
     * 患者问卷随访记录-》提交问卷-》更新任务和事件状态
     *
     * @param dto 问卷事件详情
     * @return  患者随访事件记录id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long handleTaskAndEventHistory(PatientTaskEventHistoryDTO dto) {

        PatientTaskEventHistory eventHistory = patientTaskEventHistoryMapper.getById(dto.getId());
        FollowUpItemTask itemTask = followUpItemTaskMapper.getById(dto.getTaskId());
        List<TaskEvent> taskEvents = itemTask.getEvents();
        for (TaskEvent taskEvent : taskEvents) {
            if(taskEvent.getId().equals(dto.getEventId())){
                dto.setEvents(taskEvent);
                break;
            }
        }

        PatientFollowUpItemRecord itemRecord = patientFollowUpItemRecordMapper.getById(dto.getItemRecordId());
        dto.setFollowUpId(itemRecord.getFollowUpId());
        dto.setFollowUpRecordId(itemRecord.getFollowUpRecordId());

        if(eventHistory != null)
            dto.setTaskHistoryId(eventHistory.getTaskHistoryId());

        dto.setItemId(itemRecord.getItemId());
        dto.setHospitalId(itemRecord.getHospitalId());
        dto.setUserId(itemRecord.getUserId());
        dto.setPatientId(itemRecord.getPatientId());
        dto.setRemindTime(DateUtils.timeJoint(dto.getTaskBeginDay(), itemTask.getExecuteTime()));
        dto.setEventType(TaskEventTypeEnum.getEnumByIndex(dto.getEvents().getEventType()).getName());

        TaskEventVO vo = getEventHistoryInfo(dto);

        return vo.getId();
    }

}