package com.puree.followup.domain.medical.query;

import com.puree.followup.domain.medical.constant.IsHandleEnum;
import com.puree.followup.domain.medical.constant.RecordTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ReportRecordJoinInQuery
 * <AUTHOR>
 * @Description 患者体检报告入组查询条件封装
 * @Date 2024/5/28 16:46
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportRecordJoinInQuery {

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 创建时间
     */
    private Date beginDate;

    /**
     * 创建时间
     */
    private Date endDate;

    /**
     * 要排除的就诊人id列表
     */
    private List<Long> excludePatientIds;

    /**
     * 一级选项名称
     */
    private String firstLevelName;

    /**
     * 二级选项名称
     */
    private String secondLevelName;

    /**
     * 总检建议sql
     */
    private String suggestSql;

    /**
     * 检验sql
     */
    private String inspectSql;

    /**
     * 检查sql
     */
    private String examSql;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 就诊人id
     */
    private Long familyId;
}
