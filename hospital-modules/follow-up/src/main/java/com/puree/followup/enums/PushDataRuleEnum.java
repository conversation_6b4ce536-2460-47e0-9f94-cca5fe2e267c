package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/5/27 11:55
 * @Description  患者报告 规则枚举
 */
public enum PushDataRuleEnum {

    REPORT(0, "体检报告", "report"),
    SUMMARY(1, "小结", "summary"),
    SUGGEST(2, "总检建议", "suggest");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String field;

    PushDataRuleEnum(Integer index, String name, String field) {
        this.index = index;
        this.name = name;
        this.field = field;
    }

    public static PushDataRuleEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        PushDataRuleEnum[] metaArr = PushDataRuleEnum.values();
        for (PushDataRuleEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static PushDataRuleEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        PushDataRuleEnum[] metaArr = PushDataRuleEnum.values();
        for (PushDataRuleEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
