package com.puree.followup.domain.drools;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.followup.domain.drools.builder.DrlQueryBuilder;
import com.puree.followup.domain.drools.builder.FunctionBuilder;
import com.puree.followup.domain.drools.builder.GlobalBuilder;
import com.puree.followup.domain.drools.builder.ImportBuilder;
import com.puree.followup.domain.drools.builder.RuleBuilder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName StrRuleBuild
 * <AUTHOR>
 * @Description 字符串 drl 构建
 * @Date 2024/4/1 19:00
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class StringDrlBuilder {
    /**
     * 包名 key
     */
    private final static String PACKAGE_KEY = "package";
    /**
     * Drools 的逻辑包名
     */
    private String packageName;
    /**
     * Drools 的导入类 或 静态方法
     */
    private List<ImportBuilder> imports;
    /**
     * 全局变量
     */
    private List<GlobalBuilder> globals;
    /**
     * todo 后面再写
     * 自定义函数
     */
    private List<FunctionBuilder> functions;
    /**
     * todo 后面再写
     * 查询
     */
    private List<DrlQueryBuilder> querys;
    /**
     * 规则体
     */
    private List<RuleBuilder> rules;

    public String builderDrlString() {
        StrBuilder builder = new StrBuilder();
        // 包名
        if (StrUtil.isEmpty(packageName)) {
            throw new RuntimeException("packageName is null");
        }
        builder.append(PACKAGE_KEY).append(StrPool.C_SPACE).append(packageName).append(StrPool.C_LF);
        builder.append(StrPool.C_LF);
        // 导入类
        if (ArrayUtil.isNotEmpty(imports)) {
            for (ImportBuilder item : imports) {
                builder.append(item.builderImportString());
            }
        }
        builder.append(StrPool.C_LF);
        // 全局变量
        if (ArrayUtil.isNotEmpty(globals)) {
            for (GlobalBuilder item : globals) {
                builder.append(item.builderGlobalString());
            }
        }
        builder.append(StrPool.C_LF);
        // 规则体
        if (ArrayUtil.isNotEmpty(rules)) {
            for (RuleBuilder item : rules) {
                builder.append(item.builderRuleString()).append(StrPool.C_LF);
            }
        }

        return builder.toString();
    }
}
