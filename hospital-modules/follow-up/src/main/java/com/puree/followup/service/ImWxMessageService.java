package com.puree.followup.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.PatientFollowUpItemRecord;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.enums.FollowUpJoinSourceTypeEnum;
import com.puree.followup.enums.ImageTextEnum;
import com.puree.followup.enums.RecommendSourceEnum;
import com.puree.followup.enums.TaskEventTypeEnum;
import com.puree.followup.helper.HospitalWxNameHelper;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.followup.question.util.SendQuestionToPatientUtil;
import com.puree.followup.queue.producer.SmsProducer;
import com.puree.followup.queue.producer.event.followup.FollowUpSendImageEventProducer;
import com.puree.followup.queue.producer.event.followup.FollowUpSendQuestionEventProducer;
import com.puree.followup.queue.producer.event.followup.FollowUpSendRemindEventProducer;
import com.puree.followup.queue.producer.event.followup.FollowUpSendTextEventProducer;
import com.puree.followup.queue.producer.event.tutorial.TutorialArticlePushEventProducer;
import com.puree.hospital.business.api.RemoteHospitalSmsConfigService;
import com.puree.hospital.business.api.model.BusHospitalSmsConfigVo;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.common.core.constant.TemplateMsgConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.redis.mq.RedisStreamProducer;
import com.puree.hospital.five.api.RemoteServicePackService;
import com.puree.hospital.five.api.model.BusFiveServicePack;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendImageEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendQuestionEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendRemindEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendTextEvent;
import com.puree.hospital.im.api.model.FollowUpImWxDTO;
import com.puree.hospital.shop.api.RemoteShopGoodsService;
import com.puree.hospital.shop.api.model.BusShopGoods;
import com.puree.hospital.tool.api.model.event.TutorialArticlePushEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * date 2024/5/7 15:29
 *
 * <AUTHOR> jian
 * @version 1.0
 * @description 随访消息事件通知
 */
@Service
@RefreshScope
@Slf4j
public class ImWxMessageService {

    @Autowired
    private RemoteHospitalSmsConfigService remoteHospitalSmsConfigService;
    @Autowired @Lazy
    private SmsProducer smsProducer ;
    @Autowired
    private RemoteShopGoodsService remoteShopGoodsService;
    @Autowired
    private RemoteServicePackService remoteServicePackService;
    @Autowired
    private ImService imService;
    @Autowired
    private QuestionMapper questionMapper;
    @Autowired
    private SendQuestionToPatientUtil sendQuestionToPatientUtil;

    @Resource @Lazy
    private ApplicationEventPublisher publisher;
    @Resource @Lazy
    private FollowUpSendTextEventProducer followUpTextEventProducer;
    @Resource @Lazy
    private FollowUpSendImageEventProducer followUpImageEventProducer;
    @Resource @Lazy
    private FollowUpSendRemindEventProducer followUpRemindEventProducer;
    @Resource @Lazy
    private FollowUpSendQuestionEventProducer followUpQuestionEventProducer;
    @Resource @Lazy
    private TutorialArticlePushEventProducer tutorialArticlePushEventProducer;

    @Resource
    private HospitalWxNameHelper hospitalWxNameHelper;

    /*
     * 发送im 微信公众号  短信
     */
    public void sendImWxMessage(FollowUp followUp, PatientFollowUpItemRecord patientItem, TaskEvent event, PatientFollowUpRecord patientFollowUpRecord, Long patientTaskEventHistoryId) {
        sendImWxMessage(followUp, patientItem, event, patientFollowUpRecord, patientTaskEventHistoryId, Boolean.TRUE);
    }


    /**
     *  构建发送IM消息、微信消息、和短信消息的参数
     * @param followUp      随访
     * @param patientItem   患者随访分项
     * @param event         事件
     * @param patientFollowUpRecord 患者随访记录
     * @param patientTaskEventHistoryId 患者随访事件历史id
     * @return FollowUpImWxDTO
     */
    private FollowUpImWxDTO buildDefaultFollowUpImWxParam(FollowUp followUp, PatientFollowUpItemRecord patientItem, TaskEvent event, PatientFollowUpRecord patientFollowUpRecord, Long patientTaskEventHistoryId) {
        FollowUpImWxDTO followUpImWxDTO = new FollowUpImWxDTO();
        followUpImWxDTO.setIsSendIm(YesNoEnum.YES.getCode());
        followUpImWxDTO.setPatientId(patientItem.getUserId());
        followUpImWxDTO.setFamilyId(patientItem.getPatientId());
        followUpImWxDTO.setFollowUpName(followUp.getName());
        followUpImWxDTO.setFollowUpId(followUp.getId());
        followUpImWxDTO.setFollowUpRecordId(patientItem.getFollowUpRecordId());
        followUpImWxDTO.setDesc(followUp.getDesc());
        followUpImWxDTO.setHospitalId(patientItem.getHospitalId());
        followUpImWxDTO.setEventId(patientTaskEventHistoryId);
        followUpImWxDTO.setExtraRemind(event.getExtraRemind());
        followUpImWxDTO.setPhoneNum(patientFollowUpRecord.getPhoneNum());
        BusHospitalWechatConfig hospitalWechatConfig = this.getHospitalWechatConfig(patientItem.getHospitalId());
        followUpImWxDTO.setWxName(hospitalWechatConfig.getOfficialAccountName());
        followUpImWxDTO.setImType(FollowUpJoinSourceTypeEnum.findImChatType(patientFollowUpRecord.getSourceType()));
        followUpImWxDTO.setGroupId(patientFollowUpRecord.getGroupId());
        return followUpImWxDTO;
    }

    /*
     * 发送im 微信公众号  短信
     */
    public void sendImWxMessage(FollowUp followUp, PatientFollowUpItemRecord patientItem, TaskEvent event, PatientFollowUpRecord patientFollowUpRecord, Long patientTaskEventHistoryId, boolean hasTransactional) {
        FollowUpImWxDTO followUpImWxDTO = buildDefaultFollowUpImWxParam(followUp, patientItem, event, patientFollowUpRecord, patientTaskEventHistoryId);

        if (TaskEventTypeEnum.MESSAGE.getIndex().equals(event.getEventType())) {
            // 文本
            if (ImageTextEnum.TEXT.getIndex().equals(event.getMessageType())) {
                followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_TEXT_TYPE);
                followUpImWxDTO.setContent(event.getMessage());
            }
            // 图片
            if (ImageTextEnum.IMAGE.getIndex().equals(event.getMessageType())) {
                followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_IMAGE_TYPE);
                followUpImWxDTO.setUrl(event.getPicAddr());
            }
            sendFollowUpEventImWxMsg(followUpImWxDTO, hasTransactional);
        } else if (TaskEventTypeEnum.TUTORIAL.getIndex().equals(event.getEventType())) {
            buildTutorialDTO(followUpImWxDTO, event);
            log.debug("患教发送Im信息请求参数：{}", followUpImWxDTO);
            sendFollowUpEventImWxMsg(followUpImWxDTO, hasTransactional);
        } else if (TaskEventTypeEnum.QUESTIONNAIRE.getIndex().equals(event.getEventType())) {
            Question questionQuery = new Question();
            questionQuery.setId(event.getSendId());
            questionQuery.setIsDelete(YesNoEnum.NO.getCode());
            Question questionDO = questionMapper.selectById(questionQuery);
            if (null != questionDO) {
                followUpImWxDTO.setId(event.getSendId());
                followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE);
                followUpImWxDTO.setName(questionDO.getName());
                followUpImWxDTO.setQuestionOutNum(sendQuestionToPatientUtil.handleQuestionOutNum(questionDO.getQuestionContent()));
                followUpImWxDTO.setDesc(questionDO.getDesc());
                sendFollowUpEventImWxMsg(followUpImWxDTO, hasTransactional);
            }
        } else if (TaskEventTypeEnum.RECOMMEND.getIndex().equals(event.getEventType())) {
            if (null != event.getRecommendSourceId()) {
                this.sendForRecommend(followUp, patientItem, event, patientFollowUpRecord, patientTaskEventHistoryId, hasTransactional);
            }
        }
    }

    /**
     * 对于推介医生，科室的发送
     */
    private void sendForRecommend(FollowUp followUp, PatientFollowUpItemRecord patientItem, TaskEvent event, PatientFollowUpRecord patientFollowUpRecord, Long patientTaskEventHistoryId, boolean hasTransactional) {
        FollowUpImWxDTO followUpImWxDTO = buildDefaultFollowUpImWxParam(followUp, patientItem, event, patientFollowUpRecord, patientTaskEventHistoryId);
        if (RecommendSourceEnum.DOCTOR.getIndex().equals(event.getRecommendSourceId())) {
            followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_RECOMMENDED_DOCTOR_TYPE);
            followUpImWxDTO.setDoctorName(event.getRecommendName());
            followUpImWxDTO.setId(Long.valueOf(event.getRecommendId()));
            followUpImWxDTO.setDepartmentName(event.getDoctorDept());
            followUpImWxDTO.setPhoto(event.getDoctorPhoto());
        } else if (RecommendSourceEnum.DEPARTMENT.getIndex().equals(event.getRecommendSourceId())) {
            followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_RECOMMENDED_DEPARTMENT_TYPE);

            String[] split1 = event.getRecommendName().split(",");
            followUpImWxDTO.setDepartmentName(split1[split1.length - 1].trim());

            String[] split2 = event.getRecommendId().split(",");
            followUpImWxDTO.setDepartmentId(Long.valueOf(split2[split2.length - 1].trim()));

        } else if (RecommendSourceEnum.GOODS.getIndex().equals(event.getRecommendSourceId())) {
            followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_RECOMMENDED_GOODS_TYPE);
            R<BusShopGoods> r = remoteShopGoodsService.getDetail(Long.valueOf(event.getRecommendId()));
            if (null != r.getData()) {
                BusShopGoods goods = r.getData();
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", goods.getId());
                jsonObject.put("brandName", goods.getBrandName());
                jsonObject.put("img", goods.getImg());
                jsonObject.put("name", goods.getName());
                jsonObject.put("quantity", 1);
                jsonObject.put("sellingPrice", goods.getSellingPrice());
                jsonObject.put("specification", goods.getSpecification());
                jsonObject.put("title", goods.getTitle());
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(jsonObject);
                followUpImWxDTO.setGoods(jsonArray.toJSONString());
            }
        } else if (RecommendSourceEnum.SERVICE_PACK.getIndex().equals(event.getRecommendSourceId())) {
            followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_RECOMMENDED_SERVICE_TYPE);
            BusFiveServicePack fiveServicePackDTO = new BusFiveServicePack();
            fiveServicePackDTO.setId(Long.valueOf(event.getRecommendId()));
            fiveServicePackDTO.setHospitalId(patientItem.getHospitalId());
            R<BusFiveServicePackVO> r = remoteServicePackService.feignQueryInfo(fiveServicePackDTO);
            if (null != r.getData()) {
                BusFiveServicePackVO fiveServicePackVO = r.getData();
                followUpImWxDTO.setServicePackName(fiveServicePackVO.getServicePackName());
                followUpImWxDTO.setId(fiveServicePackVO.getId());
                followUpImWxDTO.setServicePackImg(fiveServicePackVO.getServicePackImg());
                followUpImWxDTO.setSuitablePopulation(fiveServicePackVO.getSuitablePopulation());
                followUpImWxDTO.setServicePeriodAmount(fiveServicePackVO.getServicePeriodAmount());
                followUpImWxDTO.setDoctorId(fiveServicePackVO.getLeaderId());
            }
        }
        sendFollowUpEventImWxMsg(followUpImWxDTO, hasTransactional);
    }

    /**
     *  从公众号配置中获取公众号名称
     *   - 如未设置取医院名称
     * @param hospitalId    医院Id
     * @return  公众号配置
     */
    public BusHospitalWechatConfig getHospitalWechatConfig(Long hospitalId) {
        return hospitalWxNameHelper.getHospitalWechatConfig(hospitalId);
    }

    private void sendFollowUpEventImWxMsg(FollowUpImWxDTO followUpImWxDTO, boolean hasTransactional) {
        if (StrUtil.isBlank(followUpImWxDTO.getType())) {
            return;
        }
        // im
        if (YesNoEnum.YES.getCode().equals(followUpImWxDTO.getIsSendIm())) {
            imService.sendImMessage(followUpImWxDTO);
        }
        boolean sendWechatMsg = false;
        boolean sendShortMsg = false;
        if (StrUtil.isNotBlank(followUpImWxDTO.getExtraRemind())) {
            String[] split = followUpImWxDTO.getExtraRemind().split(",");
            if (split.length >= 2) {
                sendWechatMsg = "1".equals(split[0].trim());
                sendShortMsg = "1".equals(split[1].trim());
            }
        }
        // 发送微信通知判断
        if (sendWechatMsg) {
            // 进行发送类型判断
            if (TemplateMsgConstants.FOLLOW_UP_TEXT_TYPE.equals(followUpImWxDTO.getType())) {
                // 组装文本事件
                FollowUpSendTextEvent followUpSendTextEvent = new FollowUpSendTextEvent();
                followUpSendTextEvent.setPatientFollowUpRecordId(followUpImWxDTO.getFollowUpRecordId());
                sendFollowUpMessageHandler(hasTransactional, followUpSendTextEvent, publisher, followUpTextEventProducer);
            } else if (TemplateMsgConstants.FOLLOW_UP_IMAGE_TYPE.equals(followUpImWxDTO.getType())) {
                // 组装图片事件
                FollowUpSendImageEvent followUpSendImageEvent = new FollowUpSendImageEvent();
                followUpSendImageEvent.setPatientFollowUpRecordId(followUpImWxDTO.getFollowUpRecordId());
                sendFollowUpMessageHandler(hasTransactional, followUpSendImageEvent, publisher, followUpImageEventProducer);
            } else if (TemplateMsgConstants.FOLLOW_UP_FORWARD_TUTORIAL_TYPE.equals(followUpImWxDTO.getType())) {
                // 组装患教事件
                TutorialArticlePushEvent tutorialArticlePushEvent = new TutorialArticlePushEvent();
                tutorialArticlePushEvent.setArticleId(followUpImWxDTO.getTutorialId());
                // 上面已经转换过，此处为患者Id
                tutorialArticlePushEvent.setPatientId(followUpImWxDTO.getPatientId());
                tutorialArticlePushEvent.setHospitalId(followUpImWxDTO.getHospitalId());
                HashMap<String, Object> attachment = new HashMap<>();
                attachment.put("eventId", followUpImWxDTO.getEventId());
                tutorialArticlePushEvent.setAttachment(attachment);
                sendFollowUpMessageHandler(hasTransactional, tutorialArticlePushEvent, publisher, tutorialArticlePushEventProducer);
            } else if (TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE.equals(followUpImWxDTO.getType())) {
                // 组装问卷事件
                FollowUpSendQuestionEvent followUpSendQuestionEvent = new FollowUpSendQuestionEvent();
                followUpSendQuestionEvent.setPatientFollowUpRecordId(followUpImWxDTO.getFollowUpRecordId());
                followUpSendQuestionEvent.setPatientTaskEventHistoryId(followUpImWxDTO.getEventId());
                sendFollowUpMessageHandler(hasTransactional, followUpSendQuestionEvent, publisher, followUpQuestionEventProducer);
            }
        }
        // 发送短信判断
        if (sendShortMsg) {
            String hospitalSign = "";
            R<BusHospitalSmsConfigVo> r = remoteHospitalSmsConfigService.selectOne(followUpImWxDTO.getHospitalId(), null);
            if (null != r.getData()) {
                hospitalSign = r.getData().getHospitalSign();
            }
            followUpImWxDTO.setHospitalSign(hospitalSign);
            // 医院短信模板不为空 且 属于一下其中一个类型即发送短信
            if (StrUtil.isNotBlank(followUpImWxDTO.getHospitalSign()) && StrUtil.equalsAny(followUpImWxDTO.getType(),
                    TemplateMsgConstants.FOLLOW_UP_TEXT_TYPE, TemplateMsgConstants.FOLLOW_UP_IMAGE_TYPE,
                    TemplateMsgConstants.FOLLOW_UP_FORWARD_TUTORIAL_TYPE, TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE)) {
                smsProducer.send(followUpImWxDTO);
            }
        }
    }

    /**
     *  根据是否开启事务选择发送消息方式
     * @param hasTransactional  是否有事务传播
     * @param event             事件
     * @param publisher         Spring事件发布器
     * @param product           自定义事件发布器
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    private <P extends RedisStreamProducer> void sendFollowUpMessageHandler(boolean hasTransactional, Object event, ApplicationEventPublisher publisher, P product) {
        if (hasTransactional) {
            publisher.publishEvent(event);
        } else {
            product.send(event);
        }
    }

    public void sendUnifiedRemindTodoMsg(Long hospitalId, String phoneNum, Long userId, Long patientId, Long followUpId, Long followUpRecordId) {
        FollowUpImWxDTO followUpImWxDTO = new FollowUpImWxDTO();
        followUpImWxDTO.setIsSendIm(YesNoEnum.NO.getCode());
        followUpImWxDTO.setFollowUpId(followUpId);
        followUpImWxDTO.setFollowUpRecordId(followUpRecordId);
        followUpImWxDTO.setPatientId(userId);
        followUpImWxDTO.setFamilyId(patientId);
        followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_SUMMARY_REMIND_TYPE);
        followUpImWxDTO.setHospitalId(hospitalId);
        BusHospitalWechatConfig hospitalWechatConfig = getHospitalWechatConfig(hospitalId);
        followUpImWxDTO.setWxName(hospitalWechatConfig.getOfficialAccountName());
        followUpImWxDTO.setPhoneNum(phoneNum);

        String hospitalSign = "";
        R<BusHospitalSmsConfigVo> r = remoteHospitalSmsConfigService.selectOne(followUpImWxDTO.getHospitalId(), null);
        if (null != r.getData()) {
            hospitalSign = r.getData().getHospitalSign();
        }
        followUpImWxDTO.setHospitalSign(hospitalSign);

        // 组装随访事件提醒通知
        FollowUpSendRemindEvent followUpSendRemindEvent = new FollowUpSendRemindEvent();
        followUpSendRemindEvent.setPatientFollowUpRecordId(followUpRecordId);
        followUpRemindEventProducer.send(followUpSendRemindEvent);

        if (StrUtil.isNotBlank(followUpImWxDTO.getHospitalSign())) {
            smsProducer.send(followUpImWxDTO);
        }

    }

    public void sendShortMsgForQuestion(FollowUpImWxDTO followUpImWxDTO) {

        if (StrUtil.isBlank(followUpImWxDTO.getType()) || StrUtil.isBlank(followUpImWxDTO.getPhoneNum())) {
            return;
        }

        String hospitalSign = "";
        R<BusHospitalSmsConfigVo> r = remoteHospitalSmsConfigService.selectOne(followUpImWxDTO.getHospitalId(), null);
        if (null != r.getData()) {
            hospitalSign = r.getData().getHospitalSign();
        }
        followUpImWxDTO.setHospitalSign(hospitalSign);

        BusHospitalWechatConfig hospitalWechatConfig = this.getHospitalWechatConfig(followUpImWxDTO.getHospitalId());
        followUpImWxDTO.setWxName(hospitalWechatConfig.getOfficialAccountName());

        if (StrUtil.isNotBlank(followUpImWxDTO.getHospitalSign())) {
            smsProducer.send(followUpImWxDTO);
        }

    }

    /**
     * 构建患教信息推送
     *
     * @param followUpImWxDTO 随访消息DTO
     * @param event           随访事件
     */
    private void buildTutorialDTO(FollowUpImWxDTO followUpImWxDTO, TaskEvent event) {
        followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_FORWARD_TUTORIAL_TYPE);
        followUpImWxDTO.setAssistantFlag("0");
        followUpImWxDTO.setTutorialType("0");
        followUpImWxDTO.setTutorialId(event.getSendId());
        followUpImWxDTO.setName(event.getSendName());
        // 字段没用上，先注释
        /*AjaxResult<Article> result = remoteArticleService.overviewArticle(event.getSendId());
        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            String msg = Objects.nonNull(result) ? result.getMsg() : "";
            log.error("查询患教信息失败:{}", msg);
            return;
        }
        Article article = result.getData();
        followUpImWxDTO.setFrontCover(article.getFrontCover());
        followUpImWxDTO.setArticleType(article.getArticleType());
        long readingVolumeCardinality = Objects.nonNull(article.getReadingVolumeCardinality()) ? article.getReadingVolumeCardinality() : 0L;
        followUpImWxDTO.setPatientTutorialSource(article.getPatientTutorialSource());
        Long hospitalId = Objects.nonNull(article.getHospitalId()) ? article.getHospitalId() : followUpImWxDTO.getHospitalId();

        ArticleUserBehaviorDTO articleUserBehaviorDTO = new ArticleUserBehaviorDTO();
        articleUserBehaviorDTO.setArticleId(article.getId());
        articleUserBehaviorDTO.setHospitalId(hospitalId);
        //阅读数量
        articleUserBehaviorDTO.setEventBehaviorType(3);
        AjaxResult<Integer> behaviorResult = remoteArticleService.patientBehaviorCount(articleUserBehaviorDTO);
        if (Objects.nonNull(behaviorResult) && behaviorResult.isSuccess()) {
            readingVolumeCardinality = readingVolumeCardinality + (Objects.nonNull(behaviorResult.getData()) ? behaviorResult.getData() : 0);
        }
        followUpImWxDTO.setReadingVolumeCardinality(readingVolumeCardinality);
        if (Objects.nonNull(article.getHospitalId())) {
            R<BusHospital> hospitalResult = remoteHospitalService.getHospitalInfo(article.getHospitalId());
            if (Objects.nonNull(hospitalResult) && hospitalResult.isSuccess() && Objects.nonNull(hospitalResult.getData())) {
                BusHospital busHospital = hospitalResult.getData();
                followUpImWxDTO.setHospitalName(busHospital.getHospitalName());
                followUpImWxDTO.setHospitalPhoto(busHospital.getHospitalPhoto());
            }
        }
        if (Objects.nonNull(article.getDoctorId())) {
            R<BusDoctorVo> doctorVoResult = remoteDoctorService.personalData(hospitalId, article.getDoctorId());
            if (Objects.nonNull(doctorVoResult) && doctorVoResult.isSuccess() && Objects.nonNull(doctorVoResult.getData())) {
                BusDoctorVo busDoctorVo = doctorVoResult.getData();
                followUpImWxDTO.setDoctorName(busDoctorVo.getFullName());
                followUpImWxDTO.setDoctorPhoto(busDoctorVo.getPhoto());
                followUpImWxDTO.setDoctorTitle(busDoctorVo.getTitleValue());
            }
        }*/
    }

}
