package com.puree.followup.question.domain.model.topic;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.followup.question.enums.QuestionTypeEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Date 2024/7/19 12:03
 * <AUTHOR>
 * @Description: 问卷题目
 * @Version 1.0
 */

@NoArgsConstructor
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class QuestionTopic {

    /**
     * 问卷题目
     */
    private List<ListDTO> list;
    /**
     * 体检报告列表
     */
    private List<ReportListDTO> reportList;
    /**
     * 患者指标列表
     */
    private List<TargetListDTO> targetList;
    /**
     * 评估列表，好像未使用
     */
    private List<EvaluateListDTO> evaluateList;

    @Data
    public static class ListDTO {
        private String id;
        /**
         * 题目描述
         */
        private String desc;
        /**
         * 题目类型枚举 {@link QuestionTypeEnum}
         */
        private Integer type;
        /**
         *
         */
        private BigDecimal score;
        /**
         * 题目名称
         */
        private String title;
        /**
         * 选项
         */
        private List<OptionsDTO> options;
        private Integer pattern;
        private Integer require;
        /**
         * 附件类型
         */
        private Integer annexType;

        @Data
        public static class OptionsDTO {
            private String id;
            /**
             *
             */
            private BigDecimal score;
            /**
             * 选项值
             */
            private String value;
            /**
             * 选项类型{@link QuestionTypeEnum}
             */
            private Integer optType;
            private Integer pattern;
            private Integer require;
            /**
             * 子节点
             */
            private List<ListDTO> children;
            /**
             * 是否存在子节点
             */
            private Boolean isHaveChildren;
        }
    }

    @Data
    public static class ReportListDTO {
        private String id;
        private BigDecimal score;
        private Integer require;
        /**
         * 化验单、检查单
         */
        private String firstReport;
        /**
         * 报告名称
         */
        private String secondReport;
    }

    @Data
    public static class TargetListDTO {
        private String id;
        private BigDecimal score;
        private Integer require;
        /**
         * 一级选项
         */
        private String firstLevelOption;
        /**
         * 二级选项
         */
        private String secondLevelOptions;
    }

    @Data
    public static class EvaluateListDTO {

    }

}
