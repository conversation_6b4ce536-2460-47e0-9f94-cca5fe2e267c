package com.puree.followup.domain.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.medical.constant.RecordFileOperationType;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportFile
 * <AUTHOR>
 * @Description 体检报告文件记录表
 * @Date 2024/5/13 17:58
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportFileRecord {

    /**
     * 主键
     */
    private Long id;
    /**
     * 来源类型;DATA_PUSH 数据推送，PATIENT_ADDED 患者添加，BACKGROUND_ADD 后台添加或医生添加，DOCTOR_ADDED 医生添加
     */
    private RecordSourceEnum source;
    /**
     * 项目文件链接
     */
    private String reportFile;
    /**
     * 项目文件名称
     */
    private String reportFileName;
    /**
     * 机构ID
     */
    private String orgId;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 体检数据记录ID
     */
    private Long recordId;
    /**
     * 操作类型
     */
    private RecordFileOperationType operationType;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * XK 或 EHOME 的数据来源 ID
     */
    private Long dataSourceId;
    /**
     * CheckOrganizationEnum
     */
    private Integer dataSource;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 患者身份证号
     */
    private String patientIdNumber;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 检测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
}
