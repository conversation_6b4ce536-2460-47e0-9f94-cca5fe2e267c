package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  分项编辑时的行为：add、update、del
 */
public enum FollowUpEditActionEnum {

    ADD(0, "add", "新增"),
    UPDATE(1, "update", "修改"),
    DEL(2, "del", "删除"),;

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;


    @Getter
    @Setter
    private String desc;


    FollowUpEditActionEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpEditActionEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpEditActionEnum[] metaArr = FollowUpEditActionEnum.values();
        for (FollowUpEditActionEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpEditActionEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpEditActionEnum[] metaArr = FollowUpEditActionEnum.values();
        for (FollowUpEditActionEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
