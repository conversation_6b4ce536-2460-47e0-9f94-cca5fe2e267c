package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 随访表列表 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpListVO {


    /**
     * ID
     */
    private Long id;

    /**
     * 随访名称
     */
    private String name;

    /**
     * 随访描述
     */
    private String desc;

    /**
     * 备注说明(仅内部可见)
     */
    private String remark;

    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 启用标识：false.否 true.是
     */
    private Boolean isEnable;

    /**
     * 发布标识：false.否 true.是
     */
    private Boolean isPublish;

    /**
     * 随访中患者数
     */
    private Integer ongoingPatients;

    /**
     * 已完成患者数
     */
    private Integer finishedPatients;

    /**
     * 预警患者数
     */
    private Integer warningPatients;

    /**
     * 总患者
     */
    private Integer totalPatients;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}