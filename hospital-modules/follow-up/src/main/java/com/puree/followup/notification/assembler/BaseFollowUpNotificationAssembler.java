package com.puree.followup.notification.assembler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.puree.followup.domain.followup.dto.FollowUpEventBusinessDTO;
import com.puree.hospital.common.core.enums.ImTypeEnum;
import com.puree.hospital.common.notification.assembler.INotificationAssembler;
import com.puree.hospital.common.notification.constant.TemplateMessageConstants;
import com.puree.hospital.common.notification.domain.bo.WxOfficialAccountTemplateData;
import com.puree.hospital.common.notification.domain.bo.WxUniAppTemplateData;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 基础的随访事件通知组装器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 18:27
 */
@Getter
public abstract class BaseFollowUpNotificationAssembler implements INotificationAssembler {

    private final BaseFollowUpEvent event;

    private final FollowUpEventBusinessDTO businessDTO;

    public BaseFollowUpNotificationAssembler(BaseFollowUpEvent event, FollowUpEventBusinessDTO businessDTO) {
        this.event = event;
        this.businessDTO = businessDTO;
    }

    /**
     * 微信公众号默认模板参数
     * @return  Map
     */
    public Map<String, Map<String, Object>> getDefaultWxOfficialAccountTemplateParam() {
        Map<String, WxOfficialAccountTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.THING2, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME));
        templateParam.put(TemplateMessageConstants.TIME3, new WxOfficialAccountTemplateData(DateUtil.format(getEvent().getEventTime(), DatePattern.NORM_DATETIME_FORMAT)));
        templateParam.put(TemplateMessageConstants.CONST4, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_WX_UNI_UNIFIED_MSG));
        return convert(templateParam);
    }

    /**
     * 微信小程序默认模板参数
     * @return  Map
     */
    public Map<String, Map<String, Object>> getDefaultWxUniAppTemplateParam() {
        Map<String, WxUniAppTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.THING4, new WxUniAppTemplateData(TemplateMessageConstants.CONSULTATION_REMIND_UNIFIED_TITLE));
        templateParam.put(TemplateMessageConstants.NAME3, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME));
        templateParam.put(TemplateMessageConstants.THING2, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_WX_UNI_UNIFIED_MSG));
        return convert(templateParam);
    }

    /**
     *  获取随访发送图片或者文字事件微信小程序跳转页面
     * @return  pagePath
     */
    public String getFollowUpTextOrImageRedirectPage() {
        String page;
        if (ImTypeEnum.ONE_TO_ONE.getIndex().equals(businessDTO.getImType())) {
            page = String.format(TemplateMessageConstants.FOLLOW_UP_EVENT_TEXT_IMAGE_URL, businessDTO.getFollowUpId(), businessDTO.getFollowUpRecordId(), businessDTO.getPatientId(), businessDTO.getHospitalId());
        } else if (ImTypeEnum.SINGLE_GROUP.getIndex().equals(businessDTO.getImType())) {
            page = TemplateMessageConstants.IM_CHAT_ROOM_URL + "groupId=" + businessDTO.getGroupId() ;
        } else if (ImTypeEnum.MULTIPLE_GROUP.getIndex().equals(businessDTO.getImType())) {
            page = TemplateMessageConstants.IM_CHAT_ROOM_GROUP_URL + "groupId=" + businessDTO.getGroupId() ;
        } else {
            // 其他情况默认跳转到微信小程序首页
            page = WX_UNI_APP_HOME_PAGE;
        }
        return page;
    }

}
