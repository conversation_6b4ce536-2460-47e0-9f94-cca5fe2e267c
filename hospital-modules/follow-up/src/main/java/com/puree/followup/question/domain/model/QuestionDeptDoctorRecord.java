package com.puree.followup.question.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: QuestionDeptDoctorRecord
 * @Date 2024/6/12 15:02
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class QuestionDeptDoctorRecord {

    /**
     * ID
     */
    private Long id;

    /**
     * questionId
     */
    private Long questionId ;

    /**
     * 可用科室级联id, [2, 4]
     */
    private List<Long> departmentIds ;

    /**
     * 可用科室id
     */
    private Long departmentId ;

    /**
     * 可用医生id
     */
    private Long doctorId ;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0;








}
