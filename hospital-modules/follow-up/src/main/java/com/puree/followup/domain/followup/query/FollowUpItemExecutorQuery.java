package com.puree.followup.domain.followup.query;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.SmartExecutorConditionTypeHandler;
import com.puree.followup.config.TaskEventTypeHandler;
import com.puree.followup.domain.followup.model.SmartExecutorCondition;
import com.puree.followup.domain.followup.model.TaskEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访-分项-智能执行配置 Query
 * <AUTHOR>
 * @date 2024-07-23 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemExecutorQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 问卷json字符串
     */
    private String questionJson;

    /**
     * 问卷查询的sql语句
     */
    private String questionSql;

}