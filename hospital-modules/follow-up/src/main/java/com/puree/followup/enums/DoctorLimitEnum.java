package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  可用医生：0.不限 1.自定义
 */
public enum DoctorLimitEnum {

    UNLIMITED(0, "unlimited", "不限"),
    CUSTOM(1, "custom", "自定义");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    DoctorLimitEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static DoctorLimitEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        DoctorLimitEnum[] metaArr = DoctorLimitEnum.values();
        for (DoctorLimitEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static DoctorLimitEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        DoctorLimitEnum[] metaArr = DoctorLimitEnum.values();
        for (DoctorLimitEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
