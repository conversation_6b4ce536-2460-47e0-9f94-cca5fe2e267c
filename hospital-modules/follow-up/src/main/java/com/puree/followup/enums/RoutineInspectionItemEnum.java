package com.puree.followup.enums;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName InternalMedItemEnum
 * <AUTHOR>
 * @Description 常规检查项目枚举
 * @Date 2024/3/27 14:58
 * @Version 1.0
 */
@Getter
public enum RoutineInspectionItemEnum {

    //内科
    HEART(0, "internal_heart", "心", "内科心检查"),
    LIVER( 1, "internal_liver", "肝","内科肝检查"),
    SPLEEN( 2, "internal_spleen", "脾","内科脾检查"),
    LUNGS( 3, "internal_lungs", "肺","内科肺检查"),
    KIDNEY( 4, "internal_kidney","肾", "内科肾检查"),
    //外科
    SKIN( 5, "surgery_skin", "皮肤","外科皮肤检查"),
    LIMB_JOINTS( 6, "surgery_limb_joints", "关节","外科关节检查"),
    DIGITAL_ANAL_EXAMINATION( 7, "surgery_digital_anal_examination", "肛门指检","外科肛门检查"),
    MAMMARY_GLAND( 8, "surgery_mammary_gland", "乳房","外科乳房检查"),
    LYMPH_NODES( 9, "surgery_lymph_nodes", "淋巴","外科淋巴检查"),
    THYROID( 10, "surgery_thyroid", "甲状腺","外科甲状腺检查"),
    //眼科
    OPTICAL_EXAMINATION( 11, "ophthalmology_optical_examination", "视力","眼科视力检查"),
    COLOR_DISCRIMINATION( 12, "ophthalmology_color_discrimination", "辨色力","眼科颜色辨别检查"),
    FUNDUS( 13, "ophthalmology_fundus", "眼底","眼科眼底检查"),
    SLIT_LAMP( 14, "ophthalmology_slit_lamp", "裂隙灯","眼科裂隙灯检查"),
    INTRACORNEAL_PRESSURE( 15, "ophthalmology_intraocular_pressure", "眼压","眼科眼压检查"),
    //耳鼻喉
    HEARING( 16, "otolaryngology_hearing", "听力","耳鼻喉科听力检查"),
    EXTERNAL_AUDITORY_CANAL( 17, "otolaryngology_external_auditory_canal", "外耳道","耳鼻喉科外耳道检查"),
    EARDRUM( 18, "otolaryngology_eardrum", "鼓膜","耳鼻喉科鼓膜检查"),
    PHARYNX( 19, "otolaryngology_pharynx", "咽","耳鼻喉科咽检查"),
    TONSIL( 20, "otolaryngology_tonsil", "扁桃体","耳鼻喉科扁桃体检查"),
    THROAT( 21, "otolaryngology_throat", "喉","耳鼻喉科喉检查"),
    EXTERNAL_NOSE( 22, "otolaryngology_external_nose", "外鼻","耳鼻喉科外鼻检查"),
    NASAL_CAVITY( 23, "otolaryngology_nasal_cavity", "鼻腔","耳鼻喉科鼻腔检查"),
    //口腔科
    LIP( 24, "stomatology_lip", "唇","口腔科唇检查"),
    PALATE( 25, "stomatology_palate", "上颚","口腔科上颚检查"),
    TEETH( 26, "stomatology_teeth", "牙齿","口腔科牙齿检查"),
    PERIODONTAL( 27, "stomatology_periodontal", " 牙周","口腔科牙周检查"),
    ORAL_MUCOSA( 28, "stomatology_oral_mucosa", "口腔黏膜","口腔科口腔黏膜检查"),
    TONGUE( 29, "stomatology_tongue", "舌头","口腔科舌头检查"),
    SUBMANDIBULAR_LYMPH_NODES( 30, "stomatology_submandibular_lymph_nodes", "下颌淋巴","口腔科下颌淋巴检查"),
    TEMPOROMANDIBULAR_JOINT( 31, "stomatology_temporomandibular_joint", "颞颌关节","口腔科颞颌关节检查"),

    ;

    /**
     * @Param code
     * @Return com.puree.followup.enums.RoutineInspectionItemEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/3/31 16:15
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RoutineInspectionItemEnum getByCode(String code) {
        if (code == null){
            return null;
        }
        return Arrays.stream(RoutineInspectionItemEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(),code))
                .findAny()
                .orElse(null);
    }

    RoutineInspectionItemEnum( Integer number,String code,String name , String desc) {
        this.name = name;
        this.number = number;
        this.code = code;
        this.desc = desc;
    }

    /**
     * 编号
     */
    private Integer number;
    /**
     * 代码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String desc;
}
