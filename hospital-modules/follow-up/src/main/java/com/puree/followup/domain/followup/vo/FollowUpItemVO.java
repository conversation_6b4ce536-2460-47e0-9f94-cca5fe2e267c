package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.followup.dto.FollowUpItemTaskDTO;
import com.puree.followup.domain.followup.dto.FollowUpSmartExecutorDTO;
import com.puree.followup.domain.followup.model.ItemTask;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 随访-分项 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项名称
     */
    private String name;

    /**
     * 分项排序
     */
    private Integer sortNum;

    /**
     * 分项执行时间的类型：longTerm:全周期 、joinIn:加入随访后第X至Y天 、eventJoinIn:通过事件加入后至Y天
     */
    private String executeType;

    /**
     * 分项执行时间：加入随访第X天开始
     */
    private Integer beginDay;

    /**
     * 分项执行时间：加入随访至Y天结束，或者表示通过事件加入至Y天结束
     */
    private Integer endDay;

    /**
     * 提醒时间的类型：0.统一提醒 1.单独提醒
     */
    private Integer remindTimeType;

    /**
     * 提醒时间，格式为：HH:mm
     */
    private String remindTime;

    /**
     * 分项任务执行周期的类型：custom:自定义 、periodCycle:周期循环、weekCycle:固定周循环
     */
    private String intervalType;

    /**
     * 分项任务(包括执行事件)
     */
    private List<FollowUpItemTaskVO> tasks;

    /**
     * 智能执行列表
     */
    private List<FollowUpSmartExecutorVO> smartExecutors;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}