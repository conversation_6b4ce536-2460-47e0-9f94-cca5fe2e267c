package com.puree.followup.listener.question;

import com.puree.followup.queue.producer.event.question.QuestionInviteEventProducer;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <p>
 * 问卷事件监听器
 * 注意：此处之所以使用SpringEventListener, 原因是因为代码逻辑上存在事务嵌套问题，
 * 消息发送必须保证事务提交后再发送，因此需要依赖TransactionalEventListener，进行保证事务提交后在发送消息
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 14:23
 */
@Slf4j
@Component
public class QuestionEventListener {

    @Resource @Lazy
    private QuestionInviteEventProducer questionInviteEventProducer;

    @Async
    @TransactionalEventListener(classes = QuestionInviteEvent.class)
    public void questionInviteEvent(QuestionInviteEvent event) {
        log.info("问卷邀请事件监听：{}", event);
        questionInviteEventProducer.send(event);
    }

}
