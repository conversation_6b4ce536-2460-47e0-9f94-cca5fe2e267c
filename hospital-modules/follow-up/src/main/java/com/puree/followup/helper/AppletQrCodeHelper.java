package com.puree.followup.helper;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.puree.followup.domain.followup.model.QrCodeRequest;
import com.puree.hospital.common.api.constant.HttpStatus;
import com.puree.hospital.common.api.constant.SecurityConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.tool.api.RemoteBaseWxService;
import com.puree.hospital.tool.api.model.dto.WxAccessTokenDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * 小程序码辅助工具
 *
 * <AUTHOR>
 * @date 2025/4/27 15:50:59
 */
@Slf4j
@Component
@RefreshScope
public class AppletQrCodeHelper {

    @Resource
    private OSSUtil ossUtil;

    @Value("${wx.uni-app.env:release}")
    private String uniAppEnv;

    @Resource
    private RemoteBaseWxService remoteBaseWxService;

    private static final String APPLET_QR_CODE_PATH = "https://api.weixin.qq.com/wxa/getwxacode?access_token=";

    /**
     * 获取access token
     * @param hospitalId 医院id
     * @return access token
     */
    public String getAccessToken(Long hospitalId) {
        // 获取access token
        WxAccessTokenDTO wxAccessTokenDTO = new WxAccessTokenDTO();
        wxAccessTokenDTO.setHospitalId(hospitalId);
        wxAccessTokenDTO.setClientType(ClientTypeEnum.WX_UNI_APP);
        R<String> result = remoteBaseWxService.getAccessToken(wxAccessTokenDTO, SecurityConstants.INNER);
        if (Objects.isNull(result) || !result.isSuccess()) {
            log.error("获取微信access token失败, 响应信息:{}", result);
            throw new ServiceException("获取微信access token失败");
        }
        return result.getData();
    }

    /**
     * 生成小程序码
     * @param accessToken access token
     * @param path  跳转path
     * @return InputStream
     */
    public InputStream generateAppletQrCode(String accessToken, String path)  {
        String url = APPLET_QR_CODE_PATH + accessToken;
        if (log.isDebugEnabled()) {
            log.debug("url：{}, path：{}", url, path);
        }
        QrCodeRequest qrCodeRequest = new QrCodeRequest();
        qrCodeRequest.setPath(path);
        qrCodeRequest.setEnvVersion(uniAppEnv);
        if (log.isDebugEnabled()) {
            log.debug("小程序版本：{}", uniAppEnv);
        }
        // 构建HTTP请求
        HttpRequest request = HttpUtil.createPost(url)
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .body(JSON.toJSONString(qrCodeRequest))
                .setChunkedStreamingMode(DecimalFormat.INTEGER_FIELD);
        HttpResponse response = request.execute();
        if (response.getStatus() != HttpStatus.SUCCESS) {
            log.error("获取小程序码失败, 响应信息:{}", response);
            throw new ServiceException("获取小程序码失败");
        }
        return response.bodyStream();
    }

    /**
     * 上传文件到OSS
     * @param inputStream 文件流
     * @param prefix 文件前缀
     * @param suffix 文件后缀
     * @return 小程序码地址
     * @throws Exception 抛异常
     */
    public String updateHead(InputStream inputStream, String prefix, String suffix) throws Exception {
        if (inputStream == null) {
            throw new Exception("file不能为空");
        }
        return ossUtil.simpleUpload(inputStream, prefix, suffix);
    }


}
