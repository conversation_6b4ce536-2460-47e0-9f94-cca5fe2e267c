package com.puree.followup.admin.classification.service;


import com.puree.followup.domain.classification.dto.ClassificationDTO;
import com.puree.followup.domain.classification.vo.ClassificationVO;
import com.puree.followup.question.domain.dto.DuplicateClassificationDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/7 15:53
 * @description 随访-分类 服务类
 */
public interface IClassificationService {

    List<ClassificationVO> hospitalPageList(ClassificationDTO dto);

    ClassificationVO getInfo(Long id);

    Boolean hospitalAdd(ClassificationDTO dto);

    Boolean hospitalEdit(ClassificationDTO dto);

    Boolean hospitalRemove(Long id, Integer revision);

    List<ClassificationVO> listQuestionClassification(ClassificationDTO dto) ;

    Boolean addQuestionClassification(ClassificationDTO dto) ;

    Boolean editQuestionClassification(ClassificationDTO dto) ;

    Boolean deleteQuestionClassification(Long id, Integer revision) ;

    Boolean checkDuplicateClassification(DuplicateClassificationDTO req) ;

}
