package com.puree.followup.client.followup.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.client.followup.service.IFollowUpPatientService;
import com.puree.followup.domain.followup.dto.PatientFollowUpQueryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskEventHistoryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskHistoryQueryDTO;
import com.puree.followup.domain.followup.dto.QRJoinInDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.PatientFollowUpItemRecord;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.TaskAndEventStatus;
import com.puree.followup.domain.followup.vo.FollowUpInvitePageVO;
import com.puree.followup.domain.followup.vo.PatientFollowUpListVO;
import com.puree.followup.domain.followup.vo.PatientTaskHistoryTreeVO;
import com.puree.followup.domain.followup.vo.TaskEventVO;
import com.puree.followup.enums.FollowUpJoinStatusEnum;
import com.puree.followup.enums.PatientTaskStatusEnum;
import com.puree.followup.enums.TaskRemindStatusEnum;
import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.DateUtils;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.followup.api.model.AppletQrCodeVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/29 16:10
 * @description 患者端 随访 控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/patient-client")
public class FollowUpPatientController extends BaseController {

    private final IFollowUpService followUpService;
    private final IFollowUpPatientService followUpPatientService;
    private final FollowUpMapper followUpMapper;
    private final RemotePatientFamilyService remotePatientFamilyService;
    private static final String USER_NOT_FOUND = "userNotFound";
    @Value("${task.history.query.maxIntervalDays:31}")
    private Integer maxIntervalDays ; //查询患者随访记录时的最大时间跨度
    private final PatientFollowUpRecordMapper patientFollowUpRecordMapper;
    private final PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper;

    /**
     * 患者端 事件完成状态修改(非问卷)
     *
     * @param id 患者随访事件记录id
     * @param hospitalId 医院id
     * @return
     */
    @Log(title = "患者端-事件完成状态修改", businessType = BusinessType.UPDATE)
    @PutMapping("/event-finish/{id}")
    public AjaxResult<Boolean> updateEventStatus(@PathVariable("id") Long id, @RequestParam("hospitalId") Long hospitalId) {
        PatientTaskEventHistoryDTO dto = new PatientTaskEventHistoryDTO();
        dto.setId(id);
        dto.setHospitalId(hospitalId);
        return AjaxResult.success(followUpPatientService.updateEventStatus(dto, new TaskAndEventStatus()));
    }

    /**
     * 患者端 事件完成状态批量修改(非问卷)
     *
     * @param ids 患者随访事件记录id列表
     * @param hospitalId 医院id
     * @return
     */
    @Log(title = "患者端-事件完成状态修改(批量)", businessType = BusinessType.UPDATE)
    @PutMapping("/finish-events")
    public AjaxResult<Boolean> updateEventStatus(@RequestBody List<Long> ids, @RequestParam("hospitalId") Long hospitalId) {
        if(CollectionUtils.isEmpty(ids)){
            log.error("事件完成状态批量修改ids列表为空，hospitalId：{}", hospitalId);
            return AjaxResult.success(false);
        }
        return AjaxResult.success(followUpPatientService.updateEventsStatus(ids, hospitalId));
    }

    /**
     * 患者端 扫码加入随访
     *
     * @param dto
     * @return
     */
    @Log(title = "患者端-扫码加入随访", businessType = BusinessType.UPDATE)
    @PostMapping("/qrcode")
    public AjaxResult<AppletQrCodeVO> qrcodeJoinIn(@RequestBody QRJoinInDTO dto) {
        FollowUp followUp = followUpMapper.getById(dto.getId());
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            return AjaxResult.error("随访不存在");
        if(followUp.getIsEnable().equals(YesNoEnum.NO.getCode()))
            return AjaxResult.error("随访未启用");
        if(followUp.getIsPublish().equals(YesNoEnum.NO.getCode()))
            return AjaxResult.error("随访未发布");
        return followUpPatientService.qrcodeJoinIn(followUp, dto);
    }

    /**
     * 患者端 获取患者加入的随访列表
     *
     * @param dto 查询条件
     * @return 患者加入的随访列表
     */
    @Log(title = "患者端-获取患者加入的随访列表", businessType = BusinessType.QUERY)
    @GetMapping("/follow-up")
    public AjaxResult<List<PatientFollowUpListVO>> followUpList(PatientFollowUpQueryDTO dto) {
        return AjaxResult.success(followUpPatientService.followUpList(dto));
    }

    /**
     * 患者端 获取患者的健康随访列表（按日期的列表树）
     *
     * @param dto 查询条件
     * @return 患者的随访记录列表
     */
    @Log(title = "患者端-获取患者的随访记录列表", businessType = BusinessType.QUERY)
    @GetMapping("/task-history-aut")
    public AjaxResult<List<PatientTaskHistoryTreeVO>> taskHistoryPage(PatientTaskHistoryQueryDTO dto) {
        if(dto.getBeginTime() == null || dto.getEndTime() == null)
            throw new ServiceException("起止时间不完整");

        if(dto.getFollowUpId() != null){
            FollowUp followUp = followUpMapper.getById(dto.getFollowUpId());
            if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
                throw new ServiceException("随访记录不存在");
        }

        AjaxResult result = AjaxResult.success(new ArrayList<>());
        result.add("isEnd", true);
        result.add("maxIntervalDays", maxIntervalDays);

        //查询患者随访分项列表
        PatientFollowUpItemRecord itemRecordQuery = new PatientFollowUpItemRecord();
        itemRecordQuery.setFollowUpId(dto.getFollowUpId());
        itemRecordQuery.setHospitalId(dto.getHospitalId());
        itemRecordQuery.setUserId(SecurityUtils.getUserId());//患者id
        itemRecordQuery.setPatientId(dto.getPatientId());//就诊人id
        PatientFollowUpItemRecord itemRecords = patientFollowUpItemRecordMapper.getMaxBeginDayAndEndDay(itemRecordQuery);
        if(itemRecords == null)
            return result;

        //最小开始时间和最大结束时间
        Date minBeginDate = itemRecords.getBeginDay();
        Date maxEndDate = itemRecords.getEndDay();

        dto.setJoinInBeginDateStr(DateUtils.parse_yyyyMMdd(minBeginDate));
        dto.setJoinInEndDateStr(maxEndDate == null ? null : DateUtils.parse_yyyyMMdd(maxEndDate));

        Integer intervalDays = (int) DateUtil.betweenDay(dto.getBeginTime(),  dto.getEndTime(),true);
        if(intervalDays > maxIntervalDays)
            throw new ServiceException("查询时间范围超出" + maxIntervalDays + "天");

        result.add("beginDate", dto.getJoinInBeginDateStr());
        result.add("endDate", dto.getJoinInEndDateStr());

        //是否超出患者随访时间范围
        if(dto.getEndTime().before(minBeginDate) || (maxEndDate != null && maxEndDate.before(dto.getBeginTime())))
            return result;

        //对于状态查询的优化：如果查询已完成、已过期、未完成的任务，那么需要判断的开始时间与当前时间的大小，避免无意义的查询
        if(!StringUtils.isEmpty(dto.getTaskStatus())
                && !PatientTaskStatusEnum.ISCOMING.getName().equals(dto.getTaskStatus())
                && dto.getBeginTime().compareTo(new Date()) > 0)
            return result;

        dto.setUserId(SecurityUtils.getUserId());
        return followUpPatientService.taskHistory(dto);
    }

    /**
     * 患者端 提前完成事件：该接口只针对那些还未发送提醒的任务
     *
     * @param dto  要提前完成的事件
     * @return  是否成功
     */
    @PutMapping("/ahead-of-schedule")
    @Log(title = "医院后台-提前完成事件", businessType = BusinessType.UPDATE)
    public AjaxResult<Boolean> finishEventAhead(@RequestBody PatientTaskEventHistoryDTO dto) {
        if(!dto.getEventRemindStatus().equals(TaskRemindStatusEnum.NOTREMIND.getName()))
            throw new ServiceException("事件状态错误");
        return AjaxResult.success(followUpPatientService.finishEventAhead(dto, new TaskAndEventStatus()));
    }

    /**
     * 患者端 查看患者事件执行记录详情
     *
     * @param dto 查询详情
     * @return
     */
    @PostMapping("/task/event-history/info")
    @Log(title = "患者端-查看患者事件执行记录详情", businessType = BusinessType.QUERY)
    public AjaxResult<TaskEventVO> getEventHistoryInfo(@RequestBody PatientTaskEventHistoryDTO dto) {
        return AjaxResult.success(followUpPatientService.getEventHistoryInfo(dto));
    }


    /**
     * 患者端 根据事件记录id查看事件和任务的状态
     *
     * @param event-history-id 事件记录id
     * @return
     */
    @GetMapping("/task/event-history/status/{eventHistoryId}")
    @Log(title = "患者端-根据实践记录id查看事件和任务的状态", businessType = BusinessType.QUERY)
    public AjaxResult<TaskEventVO> getEventAndTaskStatus(@PathVariable("eventHistoryId") Long id) {
        return AjaxResult.success(followUpPatientService.getEventAndTaskStatus(id));
    }

    /**
     * 患者端 随访邀请页
     *
     * @param followUpRecordId 患者加入的随访记录id
     * @param hospitalId 医院id
     * @return 随访邀请结果
     */
    @Log(title = "患者端-随访邀请页", businessType = BusinessType.QUERY)
    @GetMapping("/invite-page")
    public AjaxResult<FollowUpInvitePageVO> invitePage(@RequestParam("followUpRecordId") Long followUpRecordId, @RequestParam("hospitalId") Long hospitalId) {
        PatientFollowUpRecord record = patientFollowUpRecordMapper.getById(followUpRecordId);
        if(record == null)
            throw new ServiceException("随访记录不存在");
        return AjaxResult.success(followUpPatientService.invitePage(record, hospitalId));
    }

    /**
     * 患者端 随访邀请页 患者拒绝加入随访
     *
     * @param followUpRecordId 患者加入的随访记录id
     * @param hospitalId 医院id
     * @return 操作结果
     */
    @Log(title = "患者端-随访邀请页-拒绝加入随访", businessType = BusinessType.UPDATE)
    @PutMapping("/invite-refuse")
    public AjaxResult<Boolean> refuseJoining(@RequestParam("followUpRecordId") Long followUpRecordId, @RequestParam("hospitalId") Long hospitalId) {
        PatientFollowUpRecord record = patientFollowUpRecordMapper.getById(followUpRecordId);
        if(record == null)
            throw new ServiceException("随访记录不存在");
        return AjaxResult.success(followUpPatientService.refuseJoining(record, hospitalId));
    }

    /**
     * 患者端 随访邀请页 患者确认加入随访
     *
     * @param followUpRecordId 患者加入的随访记录id
     * @param hospitalId 医院id
     * @return 操作结果
     */
    @Log(title = "患者端-随访邀请页-确认加入随访", businessType = BusinessType.UPDATE)
    @PutMapping("/invite-confirm")
    public AjaxResult<Boolean> confirmJoining(@RequestParam("followUpRecordId") Long followUpRecordId, @RequestParam("hospitalId") Long hospitalId) {
        PatientFollowUpRecord record = patientFollowUpRecordMapper.getById(followUpRecordId);
        if(record == null)
            throw new ServiceException("随访记录不存在");

        if(!record.getJoinStatus().equals(FollowUpJoinStatusEnum.INVITING.getIndex()))
            throw new ServiceException("随访状态不正确");

        FollowUp followUp = followUpMapper.getById(record.getFollowUpId());
        if(followUp.getIsEnable().equals(YesNoEnum.NO.getCode()))
            throw new ServiceException("该随访未启用");
        if(followUp.getIsPublish().equals(YesNoEnum.NO.getCode()))
            throw new ServiceException("该随访未发布");

        return AjaxResult.success(followUpPatientService.confirmJoining(record, followUp));
    }


}