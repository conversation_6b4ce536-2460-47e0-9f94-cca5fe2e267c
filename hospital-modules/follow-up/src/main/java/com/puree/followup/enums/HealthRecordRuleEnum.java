package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/5/24 11:55
 * @Description  患者资料(健康档案)规则枚举
 */
public enum HealthRecordRuleEnum {

    AGE(0, "年龄", "date_of_birth"),
    SEX(1, "性别", "sex"),
    NATION(2, "民族", "nation"),
    MARITAL_STATUS(3, "婚姻状况", "marital_status"),
    WORK_UNIT(4, "工作单位", "work_unit"),
    LOCATION(5, "住址所在地区", "location"),
    DETAIL_ADDRESS(6, "详细地址", "detail_address"),
    PROFESSION(7, "职业", "profession"),
    EDUCATION_LEVEL(8, "文化程度", "education_level");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String field;

    HealthRecordRuleEnum(Integer index, String name, String field) {
        this.index = index;
        this.name = name;
        this.field = field;
    }

    public static HealthRecordRuleEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        HealthRecordRuleEnum[] metaArr = HealthRecordRuleEnum.values();
        for (HealthRecordRuleEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static HealthRecordRuleEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        HealthRecordRuleEnum[] metaArr = HealthRecordRuleEnum.values();
        for (HealthRecordRuleEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
