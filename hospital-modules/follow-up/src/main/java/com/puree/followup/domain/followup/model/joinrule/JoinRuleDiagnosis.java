package com.puree.followup.domain.followup.model.joinrule;

import lombok.Data;

/**
 * @Description: 入组方式-诊断
 */

@Data
public class JoinRuleDiagnosis {

    /**
     * 诊断id（中医或西医）
     */
    private Long id;

    /**
     * 症型id
     */
    private Long syndromeId;

    /**
     * 类型：TCM中医、MM西医
     */
    private String type;

    /**
     * 中医或西医的诊断名称
     */
    private String name;

    /**
     * 症型名称
     */
    private String syndromeName;



}
