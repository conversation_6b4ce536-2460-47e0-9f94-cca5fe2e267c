package com.puree.followup.question.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName: SendQuestionToPatientDTO
 * @Date 2024/6/21 11:59
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
public class SendQuestionToPatientDTO {

    /**
     * 问卷主键ID
     */
    private Long id ;


    /**
     * 不发送问卷的患者id
     */
    private List<Long> excludeIdList;

    private String snowId ;


}
