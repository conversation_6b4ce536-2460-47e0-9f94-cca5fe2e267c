package com.puree.followup.domain.drools.builder;

/**
 * @ClassName FunctionBuilder
 * <AUTHOR>
 * @Description 函数生成器
 * @Date 2024/4/3 18:32
 * @Version 1.0
 */
public class FunctionBuilder {
    /**
     * 函数开始
     */
    private final static String FUNCTION_START = "function";
    /**
     * return
     */
    private final static String RETURN = "return";
    /**
     * 方法体
     */
    private final static String METHOD_BODY = "{}";
    /**
     * 方法返回类型
     */
    private String functionReturnType;
    /**
     * 方法名称
     */
    private String functionName;
    /**
     * 方法内容
     */
    private String MethodContent;
}
