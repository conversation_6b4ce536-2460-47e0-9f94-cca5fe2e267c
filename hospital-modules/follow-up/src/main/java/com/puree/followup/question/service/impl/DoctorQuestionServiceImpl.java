package com.puree.followup.question.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.followup.question.domain.dto.DoctorQuestionDTO;
import com.puree.followup.question.domain.dto.PatientQuestionDTO;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.domain.vo.PatientQuestionVO;
import com.puree.followup.question.domain.vo.QuestionVO;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.enums.QuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.followup.question.service.DoctorQuestionService;
import com.puree.followup.question.service.PatientQuestionService;
import com.puree.followup.question.service.QuestionAdminService;
import com.puree.followup.question.service.SycQuestionStatusService;
import com.puree.followup.question.util.SendQuestionToPatientUtil;
import com.puree.followup.service.ImService;
import com.puree.followup.service.ImWxMessageService;
import com.puree.hospital.business.api.RemoteBizDiseaseService;
import com.puree.hospital.business.api.RemoteBusPatientService;
import com.puree.hospital.business.api.model.BusBizDepartment;
import com.puree.hospital.business.api.model.BusDoctorDepartment;
import com.puree.hospital.business.api.model.BusPatientDto;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.business.api.model.BusPatientVO;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.constant.TemplateMsgConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;
import com.puree.hospital.im.api.model.FollowUpImWxDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName: DoctorQuestionServiceImpl
 * @Date 2024/6/27 14:11
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Service
@Slf4j
public class DoctorQuestionServiceImpl implements DoctorQuestionService {

    @Autowired
    private QuestionMapper questionMapper ;
    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper ;
    @Autowired
    private RemoteBusPatientService remoteBusPatientService ;
    @Autowired
    private PatientQuestionService patientQuestionService ;
    @Autowired
    private RemoteBizDiseaseService remoteBizDiseaseService ;
    @Autowired
    private SendQuestionToPatientUtil sendQuestionToPatientUtil ;
    @Autowired
    private ImWxMessageService imWxMessageService;
    @Autowired
    private ImService imService ;
    @Autowired
    private QuestionAdminService questionAdminService ;
    @Autowired
    private SycQuestionStatusService sycQuestionStatusService ;

    @Resource @Lazy
    private ApplicationEventPublisher publisher;

    @Override
    public List<QuestionVO> doctorPageQuestion(DoctorQuestionDTO req){

        sycQuestionStatusService.sycQuestionStatus();

        BusDoctorDepartment doctorDepartmentDTO = new BusDoctorDepartment() ;
        doctorDepartmentDTO.setHospitalId(req.getHospitalId());
        doctorDepartmentDTO.setDoctorId(req.getDoctorId());
        R<List<BusBizDepartment>> remoteDoctorDepartment = remoteBizDiseaseService.findDoctorDepartment(doctorDepartmentDTO);
        if ( null==remoteDoctorDepartment || null==remoteDoctorDepartment.getData() || remoteDoctorDepartment.getData().isEmpty() ) {
            throw new ServiceException("没有查询到医生的科室信息") ;
        }
        List<Long> departmentIdList = remoteDoctorDepartment.getData().stream().map(BusBizDepartment::getId).collect(Collectors.toList());

        PageUtil.startPage();
        List<Long> questionIdList = questionMapper.doctorViewQuestionList(req.getHospitalId(), departmentIdList, req.getDoctorId(), req.getQuestionName());

        List<QuestionVO> resultList = new ArrayList<>() ;
        if ( null == questionIdList || questionIdList.isEmpty() ) {
            return resultList ;
        }

        Date now = new Date();
        for ( Long questionId : questionIdList) {

            Question questionQuery = new Question();
            questionQuery.setId(questionId);

            Question questionDO = questionMapper.selectById(questionQuery);
            if (null==questionDO) {
                continue;
            }

            QuestionVO questionVO = new QuestionVO();
            BeanUtils.copyProperties(questionDO, questionVO);

            PatientQuestionDTO patientQuestionDTO = new PatientQuestionDTO() ;
            patientQuestionDTO.setHospitalId(req.getHospitalId());
            patientQuestionDTO.setQuestionId(questionId);
            patientQuestionDTO.setDoctorId(req.getDoctorId());
            patientQuestionDTO.setIsDelete(YesNoEnum.NO.getCode());
            patientQuestionDTO.setFillStatusList(Arrays.asList(FillQuestionStatusEnum.FINISHED.getIndex()));
            int finishCopy = patientQuestionRecordMapper.countQuestionByFillStatus(patientQuestionDTO);
            questionVO.setFinishCopy(finishCopy);

            patientQuestionDTO.setSubmitTime(now);
            int dayAddedCopy = patientQuestionRecordMapper.countQuestionByFillStatus(patientQuestionDTO);
            questionVO.setDayAddedCopy(dayAddedCopy);

            resultList.add(questionVO) ;

        }

        return PageUtil.buildPage(questionIdList, resultList);

    }


    @Override
    public List<PatientQuestionRecord> doctorPagePatientQuestion(DoctorQuestionDTO req){

        sycQuestionStatusService.sycQuestionStatus();

        Question questionQuery = new Question();
        questionQuery.setId(req.getQuestionId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if ( QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus()) ) {
            throw new ServiceException("该问卷已禁用，无法查看");
        }

        if (null!=req.getSubmitTimeStart()) {
            req.setSubmitTimeStart(DateUtil.beginOfDay(req.getSubmitTimeStart())) ;
        }
        if (null!=req.getSubmitTimeEnd()) {
            req.setSubmitTimeEnd(DateUtil.endOfDay(req.getSubmitTimeEnd())) ;
        }

        List<Long> patientIdList = new ArrayList<>() ;
        if (StrUtil.isNotBlank(req.getKeyword())) {
            BusPatientDto patientDto = new BusPatientDto() ;
            patientDto.setKeyword(req.getKeyword());
            patientDto.setHospitalId(req.getHospitalId());
            patientDto.setIsVisible(Boolean.TRUE);
            R<List<BusPatientFamilyVo>> r = remoteBusPatientService.listFamilyInfo(patientDto);
            if ( null!=r && null!=r.getData() && !r.getData().isEmpty() ) {
                patientIdList = r.getData().stream().map(BusPatientFamilyVo::getId).collect(Collectors.toList()) ;
            }
        }

        PageUtil.startPage();
        List<PatientQuestionRecord> patientQuestionRecordList = patientQuestionRecordMapper.doctorFindPatientQuestion(req.getHospitalId(), req.getQuestionId(), req.getDoctorId(),
                FillQuestionStatusEnum.FINISHED.getIndex(), YesNoEnum.NO.getCode(), req.getSubmitTimeStart(), req.getSubmitTimeEnd(), patientIdList);

        if ( null==patientQuestionRecordList || patientQuestionRecordList.isEmpty() ) {
            return patientQuestionRecordList;
        }

        List<Long> familyIds = patientQuestionRecordList.stream().map(PatientQuestionRecord::getPatientId).distinct().collect(Collectors.toList());
        R<List<BusPatientFamilyVo>> r = remoteBusPatientService.getPatientsByFamilyIds(familyIds);
        if ( null==r || null==r.getData() || r.getData().isEmpty() ) {
            return PageUtil.buildPage(patientQuestionRecordList, patientQuestionRecordList);
        }

        List<BusPatientFamilyVo> patientFamilyVoList = r.getData();

        for (PatientQuestionRecord patientQuestionRecord : patientQuestionRecordList) {

            for (BusPatientFamilyVo patientFamilyVo : patientFamilyVoList) {

                if ( patientQuestionRecord.getUserId().equals(patientFamilyVo.getPatientId()) && patientQuestionRecord.getPatientId().equals(patientFamilyVo.getId()) ) {
                    patientQuestionRecord.setPatientSex(patientFamilyVo.getSex()) ;
                    patientQuestionRecord.setPatientName(patientFamilyVo.getName()) ;
                    patientQuestionRecord.setPatientAge(patientFamilyVo.getFamilyAge()) ;
                    break;
                }

            }

        }

        return PageUtil.buildPage(patientQuestionRecordList, patientQuestionRecordList);

    }

    @Override
    public PatientQuestionVO doctorDetailPatientQuestion(Long id) {
        sycQuestionStatusService.sycQuestionStatus();
        return patientQuestionService.patientDetailQuestion(id) ;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doctorSendQuestion(DoctorQuestionDTO req){

        Question questionQuery = new Question();
        questionQuery.setId(req.getQuestionId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question questionDO = questionMapper.selectById(questionQuery);
        if ( null== questionDO) {
            throw new ServiceException("没有问卷") ;
        }
        if ( !QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus()) ) {
            throw new ServiceException("问卷状态不是发布中，无法发送!") ;
        }

        sycQuestionStatusService.sycQuestionStatus();

        Date now = new Date();

        PatientQuestionRecord patientQuestionRecord = new PatientQuestionRecord();
        patientQuestionRecord.setHospitalId(req.getHospitalId()) ;
        patientQuestionRecord.setUserId(req.getUserId()) ;
        patientQuestionRecord.setPatientId(req.getPatientId());
        patientQuestionRecord.setQuestionId(req.getQuestionId()) ;
        patientQuestionRecord.setDoctorId(req.getDoctorId()) ;
        patientQuestionRecord.setDoctorName(req.getDoctorName()) ;
        patientQuestionRecord.setAssistantId(req.getAssistantId());
        patientQuestionRecord.setAssistantName(req.getAssistantName());
        patientQuestionRecord.setFillStatus(FillQuestionStatusEnum.INVITING.getIndex()) ;
        patientQuestionRecord.setGroupId(req.getGroupId()) ;
        patientQuestionRecord.setCreateBy(req.getDoctorName()) ;
        patientQuestionRecord.setUpdateBy(req.getDoctorName());
        patientQuestionRecord.setCreateTime(now) ;
        patientQuestionRecord.setUpdateTime(now) ;
        patientQuestionRecord.setIsDelete(YesNoEnum.NO.getCode()) ;
        try{
            patientQuestionRecordMapper.insert(patientQuestionRecord) ;
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            throw new ServiceException("问卷发送失败") ;
        }

        FollowUpImWxDTO followUpImWxDTO = new FollowUpImWxDTO();
        followUpImWxDTO.setId(req.getQuestionId());
        followUpImWxDTO.setIsSendIm(YesNoEnum.YES.getCode());
        followUpImWxDTO.setPatientId(req.getUserId());
        followUpImWxDTO.setFamilyId(req.getPatientId());
        followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE);
        followUpImWxDTO.setName(questionDO.getName());
        followUpImWxDTO.setDesc(questionDO.getDesc());
        followUpImWxDTO.setHospitalId(questionDO.getHospitalId());
        followUpImWxDTO.setPatientQuestionId(patientQuestionRecord.getId());
        followUpImWxDTO.setQuestionOutNum(sendQuestionToPatientUtil.handleQuestionOutNum(questionDO.getQuestionContent()));
        followUpImWxDTO.setSendDoctorName(StringUtils.isEmpty(req.getAssistantName()) ? req.getDoctorName() : req.getAssistantName());
        followUpImWxDTO.setDoctorName(req.getDoctorName());
        followUpImWxDTO.setGroupId(req.getGroupId());
        //如果医助发送的问卷，这里的发送人要设置为医助的id
        followUpImWxDTO.setDoctorId(req.getAssistantId() == null ? req.getDoctorId() : req.getAssistantId());

        try{

            imService.doctorSendQuestion(followUpImWxDTO);
            // 问卷邀请事件通知
            QuestionInviteEvent questionInviteEvent = new QuestionInviteEvent();
            questionInviteEvent.setPatientQuestionRecordId(patientQuestionRecord.getId());
            publisher.publishEvent(questionInviteEvent);

            R<List<BusPatientVO>> rPatientVO = remoteBusPatientService.getPhonesByPatientIds(Collections.singletonList(req.getUserId()));
            if ( null==rPatientVO || null==rPatientVO.getData() || rPatientVO.getData().isEmpty() ) {
                return ;
            }
            String userPhoneNumber = rPatientVO.getData().get(0).getPhoneNumber();
            followUpImWxDTO.setPhoneNum(userPhoneNumber);
            imWxMessageService.sendShortMsgForQuestion(followUpImWxDTO) ;

        }catch (Exception ex) {
            log.error("医生发送问卷im, wx-msg, short-msg失败", ex);
        }

    }

    @Override
    public QuestionVO doctorDetailQuestion(Long id){
        sycQuestionStatusService.sycQuestionStatus();
        return questionAdminService.adminQuestionDetail(id) ;
    }


}
