package com.puree.followup.domain.event;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 健康档案上传事件
 * </p>
 *
 * <AUTHOR>
 * @date 2024/9/4 16:02
 */
@Data
@AllArgsConstructor
public class MedicalReportUploadEvent implements Serializable {

    private static final long serialVersionUID = -6046310510271968591L;

    /**
     * 健康档案原始记录数据id
     */
    private Long reportRecordId;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 就诊人id
     */
    private Long patientFamilyId;
}
