package com.puree.followup.domain.medical.constant;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName IsHandleEnum
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/13 17:30
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum IsHandleEnum {

    PROCESSED(1, "PROCESSED", "PROCESSED", "已处理"),
    NOT_PROCESSED(2, "NOT_PROCESSED", "NOT_PROCESSED", "未处理"),
    ;
    /**
     * @Param code
     * @Return com.puree.followup.domain.medical.constant.RegularRecordEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/5/13 15:39
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static IsHandleEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        IsHandleEnum isHandleEnum = Arrays.stream(IsHandleEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
        return isHandleEnum;
    }

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;
}
