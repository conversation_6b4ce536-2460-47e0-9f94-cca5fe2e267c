package com.puree.followup.question.controller;

import com.puree.followup.question.domain.dto.PatientQuestionDTO;
import com.puree.followup.question.domain.vo.PatientQuestionVO;
import com.puree.followup.question.service.PatientQuestionService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: PatientQuestionController
 * @Date 2024/6/18 14:18
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/question/patient")
@Validated
public class PatientQuestionController {

    @Autowired
    private PatientQuestionService patientQuestionService ;


    @GetMapping("/page")
    public Paging patientPageQuestion(PatientQuestionDTO req) {
        if ( null==req.getPageNum() || null==req.getPageSize() || 0==req.getPageNum() || 0==req.getPageSize() ) {
            throw new ServiceException("分页参数缺失") ;
        }
        if ( null==req.getUserId()) {
            throw new ServiceException("userId缺失") ;
        }
        return PageUtil.buildPage(patientQuestionService.patientPageQuestion(req)) ;
    }

    @GetMapping("/detail/{id}")
    public AjaxResult patientDetailQuestion(@PathVariable("id") Long id) {
        return AjaxResult.success(patientQuestionService.patientDetailQuestion(id)) ;
    }

    @PostMapping("/fill")
    public AjaxResult fillQuestion( @RequestBody PatientQuestionDTO req) {
        patientQuestionService.fillQuestion(req) ;
        return AjaxResult.success() ;
    }

    @PostMapping("/submit")
    public AjaxResult submitQuestion( @RequestBody PatientQuestionDTO req) {
        patientQuestionService.submitQuestion(req) ;
        return AjaxResult.success() ;
    }

    @PostMapping("/revise")
    public AjaxResult reviseQuestion( @RequestBody PatientQuestionDTO req) {
        patientQuestionService.reviseQuestion(req) ;
        return AjaxResult.success() ;
    }

    /**
     * 患者端 随访患者填写随访问卷：根据事件记录id，查询问卷填写详情
     *
     * @param eventHistoryId 事件记录id
     * @param questionId 问卷id
     * @return 问卷详情
     */
    @GetMapping("/follow-up/info")
    public AjaxResult<PatientQuestionVO> followUpQuestionInfo(@RequestParam("eventHistoryId") Long eventHistoryId,
                                                              @RequestParam("questionId") Long questionId) {
        return AjaxResult.success(patientQuestionService.followUpQuestionInfo(eventHistoryId, questionId)) ;
    }

    /**
     * 患者端 随访患者填写随访问卷：开始填写
     *
     * @param dto 问卷及随访信息详情
     * @return 随访事件执行历史
     */
    @PostMapping("/follow-up/fill")
    public AjaxResult<Long> followUpFillQuestion( @RequestBody PatientQuestionDTO req) {
        return AjaxResult.success(patientQuestionService.followUpFillQuestion(req)) ;
    }

    /**
     * 患者端 随访患者填写随访问卷：暂存答案（不是用户最终提交，而是用户在填写的过程中，暂时保存用户所填写的部分答案）
     *
     * @param dto 问卷及随访信息详情
     * @return 随访事件执行历史
     */
    @PostMapping("/follow-up/temp-save")
    public AjaxResult<Boolean> tempSave(@RequestBody PatientQuestionDTO req) {
        return AjaxResult.success(patientQuestionService.tempSave(req)) ;
    }

    /**
     * 患者端 随访患者填写随访问卷：开始提交
     *
     * @param dto 问卷及随访信息详情
     * @return
     */
    @PostMapping("/follow-up/submit")
    public AjaxResult followUpSubmitQuestion( @RequestBody PatientQuestionDTO req) {
        patientQuestionService.followUpSubmitQuestion(req) ;
        return AjaxResult.success() ;
    }

    @PostMapping("/temp-save")
    public AjaxResult tempSaveQuestion( @RequestBody PatientQuestionDTO req) {
        patientQuestionService.tempSaveQuestion(req) ;
        return AjaxResult.success() ;
    }



}
