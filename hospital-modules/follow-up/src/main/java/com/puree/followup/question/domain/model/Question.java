package com.puree.followup.question.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: Question
 * @Date 2024/6/4 12:03
 * <AUTHOR> jian
 * @Description: 问卷实体类
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class Question {

    /**
     * ID
     */
    private Long id;

    /**
     * 问卷名称
     */
    private String name;

    /**
     * 问卷描述
     */
    private String desc;

    /**
     * 备注说明(仅内部可见)
     */
    private String remark;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 问卷状态: 0-关闭 1-启用 2-发布
     * QuestionStatusEnum
     */
    private Integer status ;

    /**
     * 显示题目标题 ：0.否 1.是
     */
    private Integer showOrder ;

    /**
     * 可用科室：0.不限 1.自定义
     */
    private Integer departmentLimit;

    /**
     * 可用医生：0.不限 1.自定义
     */
    private Integer doctorLimit;

    /**
     * 问卷关闭时间类型：0.长期 1.自定义 ; 通过随访打开问卷，不受问卷启用状态影响
     */
    private Integer closeType ;

    /**
     * 问卷关闭开始日期
     */
    private Date closeBeginDay ;

    /**
     * 每人最多可填问卷次数 ; 通过随访打开问卷，不受填写次数影响
     */
    private Integer fillTimes ;

    /**
     * 用户填写问卷后可更改, 0.否 1.是
     */
    private Integer canRevise ;

    /**
     * 积分问卷, 0.否 1.是
     */
    private Integer isScore ;

    /**
     * 问卷试题内容, 包含 :  题目， 患者指标， 患者报告
     */
    private String questionContent ;

    /**
     *  问卷总分评定
     */
    private List<ScoreValue> questionScoreValues;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0;

    /**
     * 是否按照状态排序
     */
    private Boolean statusOrder = false;

    /**
     * 小程序码地址
     */
    private String appletQrCode;


}
