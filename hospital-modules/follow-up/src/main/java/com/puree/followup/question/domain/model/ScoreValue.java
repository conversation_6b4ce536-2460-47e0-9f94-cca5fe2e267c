package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName: ScoreValue
 * @Date 2024/6/6 10:00
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ScoreValue {

    /**
     * 设定问卷在该级别的最低分数
     */
    private BigDecimal minScore ;

    /**
     * 设定问卷在该级别的最高分数
     */
    private BigDecimal maxScore ;

    /**
     * 评定结果
     */
    private String result ;

    /**
     * 评定详情
     */
    private String detail ;

    /**
     * 显示结果给患者:0-不显示 1-显示
     */
    private Integer isShow ;


}
