package com.puree.followup.domain.followup.vo;

import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import com.puree.hospital.shop.api.model.BusShopGoods;
import com.puree.hospital.tool.api.model.Article;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/15 11:03
 * @description 事件
 */
@Data
public class TaskEventVO {

    /**
     *  事件id（后端使用）
     */
    private Long id;

    /**
     *  事件序号(前端使用)
     */
    private Integer eventNum;

    /**
     * 1-发送消息    2-发送患教   3-发送问卷   4-发送推荐
     *
     * TaskEventTypeEnum
     */
    private Integer eventType ;

    /**
     * 1-文本  2-图片
     */
    private Integer messageType ;

    /**
     *  消息内容
     */
    private String message ;

    /**
     *  图片地址
     */
    private String picAddr;

    /**
     *  患教id， 问卷id
     */
    private Long sendId ;

    /**
     *  患教 name， 问卷 name
     */
    private String sendName ;

    /**
     *  患教详情
     */
    private Article article;

    /**
     *  额外提醒 : 公众号 + 短信 + 语音电话
     *  字符串数组, 1,0,1
     */
    private String extraRemind ;

    /**
     *  1-推介医生  2-推介科室  3-推介商品  4-推介服务包
     *
     */
    private Integer recommendSourceId ;

    /**
     * 医生id、科室id、商品id、服务包id   用string的原因是 科室存在级联关系
     */
    private String recommendId ;

    /**
     * 医生name 科室name
     */
    private String recommendName ;

    /**
     * 医生职称
     */
    private String doctorTitleValue ;

    /**
     * 医生所属科室
     */
    private String belongDepartmentName ;

    /**
     * 科室所属医院名称
     */
    private String hospitalName;

    /**
     * 医生所属医院名称
     */
    private String hospitalNameByDoctor;

    /**
     * 商品信息
     */
    private BusShopGoods goods;

    /**
     * 服务包信息
     */
    private BusFiveServicePackVO servicePack;

    /**
     * 版本号
     */
    private Integer revision;

    /**
     * 事件编辑时的行为：add、update、del
     */
    private String action;

    /**
     * 要展示的事件名称
     */
    private String showData;

    private String doctorPhoto ;

    private String doctorDept ;

    /**
     * 事件完成情况：ongoing.未完成 finished.已完成
     */
    private String eventStatus;

    /**
     * 事件所属任务完成情况：iscoming:未开始、 ongoing:待完成、 finished:已完成、 hasexpired:已过期、 terminated:已终止
     */
    private String taskStatus;

    /**
     * 问卷信息
     */
    private Question question ;

    /**
     * 问卷填写信息
     */
    private PatientQuestionRecord patientQuestionRecord ;


}
