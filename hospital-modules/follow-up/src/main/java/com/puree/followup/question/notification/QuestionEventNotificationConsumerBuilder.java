package com.puree.followup.question.notification;

import com.google.common.collect.Lists;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.notification.queue.consumer.AbstractNotificationConsumerBuilder;
import com.puree.hospital.common.notification.queue.consumer.DefaultNotificationConsumer;
import com.puree.hospital.common.notification.service.ITemplateMessageService;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 *  问卷事件消息通知构建器
 * <AUTHOR>
 * @date 2025/3/21 10:06
 */
@Slf4j
@Component
public class QuestionEventNotificationConsumerBuilder<E extends QuestionInviteEvent> extends AbstractNotificationConsumerBuilder<E> {

    @Resource(name = "questionWxOfficialAccountTemplateMessageService")
    private ITemplateMessageService<E> questionWxOfficialAccountTemplateMessageService;

    @Resource(name = "questionWxUniAppTemplateMessageService")
    private ITemplateMessageService<E> QuestionWxUniAppTemplateMessageService;

    @Override
    protected ITemplateMessageService<E> getWxOfficialAccountMessageService() {
        return questionWxOfficialAccountTemplateMessageService;
    }

    @Override
    protected ITemplateMessageService<E> getWxUniAppMessageService() {
        return QuestionWxUniAppTemplateMessageService;
    }

    @Override
    protected ITemplateMessageService<E> getWxZnxybUniAppMessageService() {
        return null;
    }

    @Override
    public List<DefaultNotificationConsumer<E>> buildConsumerList() {
        List<DefaultNotificationConsumer<E>> consumers = Lists.newArrayList();
        for (ClientTypeEnum clientType : Lists.newArrayList(ClientTypeEnum.WX_OFFICIAL_ACCOUNT, ClientTypeEnum.WX_UNI_APP)) {
            ITemplateMessageService<E> messageService = super.getMessageService(clientType);
            if (messageService == null) {
                continue;
            }
            String group = getGroup(clientType);
            //1.问卷邀请通知
            consumers.add(new DefaultNotificationConsumer<>(group, QuestionInviteEvent.TOPIC, messageService, QuestionInviteEvent.class));
        }
        return consumers;
    }
}
