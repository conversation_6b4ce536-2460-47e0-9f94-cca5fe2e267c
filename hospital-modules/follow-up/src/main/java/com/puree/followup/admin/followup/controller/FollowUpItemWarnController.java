package com.puree.followup.admin.followup.controller;

import com.puree.followup.admin.followup.mapper.FollowUpItemMapper;
import com.puree.followup.admin.followup.service.IFollowUpItemWarnService;
import com.puree.followup.domain.followup.dto.FollowUpItemWarnDTO;
import com.puree.followup.domain.followup.vo.FollowUpItemWarnVO;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 16:10
 * @description 医院后台 随访预警 控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/hospital-admin/warn")
public class FollowUpItemWarnController extends BaseController {

    private final IFollowUpItemWarnService followUpItemWarnService;
    private final FollowUpItemMapper followUpItemMapper;

    /**
     * 医院后台 分页查询随访预警列表
     *
     * @param dto 查询条件
     * @return 预警列表
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:warn:page")
    @GetMapping
    @Log(title = "医院后台-分页获取随访预警列表", businessType = BusinessType.QUERY)
    public Paging<List<FollowUpItemWarnVO>> hospitalPageList(FollowUpItemWarnDTO dto) {
        if(dto.getFollowUpRecordId() == null){
            throw new ServiceException("随访记录不存在");
        }
        return PageUtil.buildPage(followUpItemWarnService.warnPageList(dto));
    }


    /**
     * 医院后台 查询随访预警详情
     *
     * @param id 预警id
     * @return 预警详情
     */
//    @PreAuthorize(hasPermi = "follow-up:hospital:warn:info")
    @GetMapping("/{id}")
    @Log(title = "医院后台-查询随访预警详情", businessType = BusinessType.QUERY)
    public AjaxResult<FollowUpItemWarnVO> getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(followUpItemWarnService.getInfo(id));
    }

    /**
     * 医院后台 获取随访预警总数
     *
     * @param dto 查询条件
     * @return 获取随访预警总数
     */
    @GetMapping("/nums")
    @Log(title = "医院后台-获取随访预警总数", businessType = BusinessType.QUERY)
    public AjaxResult<Integer> hospitalListCount(FollowUpItemWarnDTO dto) {
        if(dto.getFollowUpRecordId() == null){
            throw new ServiceException("随访记录不存在");
        }
        return AjaxResult.success(followUpItemWarnService.hospitalListCount(dto));
    }

}