package com.puree.followup.domain.followup.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 患者随访记录表
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientFollowUpRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 入组类型：0.直接添加 1.邀请入组(需患者同意) 2.手动直接添加 3.手动邀请入组(需患者同意)
     */
    private Integer joinType;

    /**
     * 邀请时间
     */
    private Date inviteTime;

    /**
     * 入组/接受邀请时间
     */
    private Date joinTime;

    /**
     * 终止/完成/拒绝邀请时间
     */
    private Date endTime;

    /**
     * 终止人类型：1.医院后台 2.医生
     */
    private Integer terminatorType;

    /**
     * 终止人
     */
    private String terminator;

    /**
     * 医生id
     */
    private Long doctorId;

    /**
     * 科室id
     */
    private Long deptId;

    /**
     * 患者随访状态：0.邀请中 1.随访中 2.已完成 3.提前终止 4.已拒绝
     */
    private Integer joinStatus;

    /**
     * 入组原因
     */
    private String joinReason;

    /**
     * 患者入组的来源：0.默认入组  1.诊断入组  2.数据推送入组 3.商品入组 4.药品入组 5.服务包入组 6.健康档案入组 7.所属医生入组
     */
    private Integer sourceType;

    /**
     * 订单编号
     */
    private String orderNo = "0";

    /**
     * 群组id（服务包和处方诊断入组时所属的群组）
     */
    private Long groupId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0;

    private String phoneNum ;

    private List<Integer> joinStatusList ;


}