package com.puree.followup.question.domain.model.topic;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Date 2024/7/19 12:03
 * <AUTHOR>
 * @Description: 问卷题目-答案
 * @Version 1.0
 */

@NoArgsConstructor
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class QuestionTopicAnswer {

    /**
     * 体检报告列表
     */
    private List<ReportListDTO> reportList;
    /**
     * 患者指标列表
     */
    private List<TargetListDTO> targetList;
    /**
     * 问卷列表
     */
    private List<QuestionListDTO> questionList;

    @Data
    public static class ReportListDTO {
        private String id;
        /**
         * 答题结果
         */
        private String answer;
    }

    @Data
    public static class TargetListDTO {
        private String id;
        /**
         * 答题结果
         */
        private String answer;
    }

    @Data
    public static class QuestionListDTO {
        private String id;
        /**
         * 答题结果
         */
        private String answer;
    }

}
