package com.puree.followup.admin.followup.controller;

import com.puree.followup.admin.followup.mapper.FollowUpInviteMapper;
import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.service.IFollowUpAsyncService;
import com.puree.followup.admin.followup.service.IFollowUpItemService;
import com.puree.followup.admin.followup.service.IFollowUpJoinRuleService;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.admin.followup.service.IPatientFollowUpRecordService;
import com.puree.followup.admin.followup.service.IPatientManualJoinInService;
import com.puree.followup.domain.classification.dto.AppletQrCoedDTO;
import com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO;
import com.puree.followup.domain.followup.dto.PatientWithRuleDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.FollowUpInvite;
import com.puree.followup.enums.RuleFilterResultEnum;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/7 16:10
 * @description 医院后台 患者随访相关 控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/hospital-admin")
public class PatientFollowUpController extends BaseController {

    private final IFollowUpService followUpService;
    private final IPatientFollowUpRecordService patientFollowUpRecordService;
    private final IFollowUpJoinRuleService followUpJoinWayService;
    private final IFollowUpItemService followUpSubitemService;
    private final PatientFollowUpRecordMapper patientFollowUpRecordMapper;
    private final PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper;
    private final FollowUpMapper followUpMapper;
    private final FollowUpInviteMapper followUpInviteMapper;
    private final IPatientManualJoinInService patientManualJoinInService;
    private final IFollowUpAsyncService followUpAsyncService;


    /**
     * 医院后台 分页搜索患者（指定患者场景）
     *
     * @param id 随访id
     * @param keyword 搜索关键字
     * @return 患者列表
     */
    @PostMapping("/manual-users")
    @Log(title = "医院后台-患者分页查询", businessType = BusinessType.QUERY)
    public Paging<List<BusPatientFamilyVo>> userPage(@RequestParam("id") Long id,
                                                     @RequestParam("keyword") String keyword,
                                                     @RequestBody List<Long> delIds) {
        return patientManualJoinInService.userPage(id, keyword, delIds);
    }

    /**
     * 医院后台 添加患者（指定患者场景）
     *
     * @param id 随访id
     * @param keyword 搜索关键字
     * @param joinType 添加方式  auto:手动添加  patientAgree:手动邀请
     * @param delIds 移除的患者id列表（userId）
     * @return
     */
    @PostMapping("/users/add")
    @Log(title = "医院后台-添加患者", businessType = BusinessType.INSERT)
    public AjaxResult<Integer> addUsers(@RequestParam("id") Long id,
                                        @RequestParam("keyword") String keyword,
                                        @RequestParam("joinType") String joinType,
                                        @RequestBody List<Long> delIds) {

        FollowUp followUp = followUpMapper.getById(id);
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        if(followUp.getIsEnable().equals(YesNoEnum.NO.getCode()))
            throw new ServiceException("随访未启用");
        if(followUp.getIsPublish().equals(YesNoEnum.NO.getCode()))
            throw new ServiceException("随访未发布");
        return AjaxResult.success(patientManualJoinInService.addUsers(id, keyword, joinType, delIds));
    }

    /**
     * 医院后台 指定范围场景-筛选患者
     *
     * @param patientWithRuleDTO 入组规则
     * @return 添加患者的记录id
     */
    @PostMapping("/rule/filter")
    @Log(title = "医院后台-指定范围场景-筛选患者", businessType = BusinessType.QUERY)
    public AjaxResult<Long> getUsersWithRules(@RequestBody PatientWithRuleDTO patientWithRuleDTO) {
        FollowUp followUp = followUpMapper.getById(patientWithRuleDTO.getId());
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        if(followUp.getIsEnable().equals(YesNoEnum.NO.getCode()))
            throw new ServiceException("随访未启用");
        if(followUp.getIsPublish().equals(YesNoEnum.NO.getCode()))
            throw new ServiceException("随访未发布");

        //可以不填写任何条件：不填写任何条件，则表示筛选该医院下所有的患者
        if(patientWithRuleDTO.getJoinRuleList() == null)
            patientWithRuleDTO.setJoinRuleList(new ArrayList<>());

        return AjaxResult.success(patientManualJoinInService.getUsersWithRules(patientWithRuleDTO, followUp));
    }

    /**
     * 医院后台 指定范围场景-轮询查询患者筛选结果
     *
     * @param id 指定范围场景，筛选记录id
     * @return 结果： ongoing:数据准备中  finished：数据已准备完成  failed:数据准备失败
     */
    @GetMapping("/rule/filter/{id}")
    @Log(title = "医院后台-指定范围场景-轮询查询患者筛选结果", businessType = BusinessType.QUERY)
    public AjaxResult<String> getRecordResult(@PathVariable("id") Long id) {
        FollowUpInvite record = followUpInviteMapper.getById(id);
        if(record == null)
            return AjaxResult.success(RuleFilterResultEnum.FAILED.getName());

        return AjaxResult.success(patientManualJoinInService.getRecordResult(record));
    }

    /**
     * 医院后台 指定范围场景-分页查询患者
     *
     * @param id 指定范围场景，筛选记录id
     * @param delIds 删除掉的就诊人id列表
     * @return 患者列表
     */
    @PostMapping("/rule/manual-users")
    @Log(title = "医院后台-指定范围场景-分页查询患者", businessType = BusinessType.QUERY)
    public Paging<List<BusPatientFamilyVo>> getUsersByInviteId(@RequestParam("id") Long id, @RequestBody List<Long> delIds) {
        FollowUpInvite invite = followUpInviteMapper.getById(id);
        if(invite == null)
            throw new ServiceException("记录不存在");
        return patientManualJoinInService.getUsersByInviteId(invite, delIds);
    }

    /**
     * 医院后台 指定范围场景-患者入组
     *
     * @param id 指定范围场景，筛选记录id
     * @param delIds 删除掉的就诊人id列表
     * @return
     */
    @PostMapping("/rule/users/{id}")
    @Log(title = "医院后台-指定范围场景-患者入组", businessType = BusinessType.QUERY)
    public AjaxResult<Integer> addUsersWithRule(@PathVariable("id") Long id, @RequestBody List<Long> delIds) {
        FollowUpInvite record = followUpInviteMapper.getById(id);
        if(record == null)
            throw new ServiceException("记录不存在");
        return AjaxResult.success(patientManualJoinInService.addUsersWithRule(record, delIds));
    }

    /**
     * 医院后台 生成随访二维码
     *
     * @param id 随访id
     * @param joinType 入组类型 auto：自动入组  patientAgree：需患者同意
     * @return 二维码
     */
    @GetMapping("/qrcode")
    @Log(title = "医院后台-生成随访二维码", businessType = BusinessType.QUERY)
    public AjaxResult<String> getQrcode(@RequestParam("id") Long id, @RequestParam("joinType") String joinType) {
        FollowUp followUp = followUpMapper.getById(id);
        if(followUp == null || followUp.getIsDelete().equals(YesNoEnum.YES.getCode()))
            throw new ServiceException("随访不存在");
        if(followUp.getIsEnable().equals(YesNoEnum.NO.getCode()) || followUp.getIsPublish().equals(YesNoEnum.NO.getCode()))
            throw new ServiceException("随访状态不正确");
        return AjaxResult.success(patientManualJoinInService.getQrcode(followUp, joinType));
    }

    /**
     * 医院后台 生成随访小程序码
     *
     * @param appletQrCoedDTO 小程序码入参
     * @return 小程序码
     */
    @GetMapping("/applet-qrcode")
    @Log(title = "医院后台-生成随访小程序码", businessType = BusinessType.QUERY)
    public R<String> getAppletQrCode(AppletQrCoedDTO appletQrCoedDTO) {
        return R.ok(patientManualJoinInService.getAppletQrCode(appletQrCoedDTO));
    }



    //-------更多设置中：处方诊断、商品、药品、服务包、更多条件的自动入组---------------

    /**
     * 患者通过处方诊断、购买商品、购买药品、购买服务包的场景自动加入随访 （远程调用）
     *
     * @param dto 患者、诊断、订单信息
     * @return 是否成功加入随访
     */
    @PostMapping("/join-followup/auto")
    @Log(title = "患者通过处方诊断、购买商品、购买药品、购买服务包的场景加入随访 ", businessType = BusinessType.INSERT)
    public AjaxResult<Boolean> patientJoinInFollowUpWithAutoRule(@RequestBody PatientFollowUpJoinRuleDTO dto) {
        return AjaxResult.success(patientFollowUpRecordService.patientJoinInFollowUpWithAutoRule(dto));
    }

    /**
     * 用户通过购买商品、购买药品、购买服务包发生退款时，终止随访 （远程调用）
     *
     * @param dto 患者、诊断、订单信息
     * @return 是否成功终止随访
     */
    @PostMapping("/refund/terminate")
    @Log(title = "用户通过购买商品、购买药品、购买服务包发生退款时，终止随访", businessType = BusinessType.UPDATE)
    public AjaxResult<Boolean> terminateFollowUpWithRefund(@RequestBody PatientFollowUpJoinRuleDTO dto) {
        if(dto.getHospitalId() == null || StringUtils.isEmpty(dto.getOrderNo()))
            return AjaxResult.success(false);
        return AjaxResult.success(patientFollowUpRecordService.terminateFollowUpWithRefund(dto));
    }

}