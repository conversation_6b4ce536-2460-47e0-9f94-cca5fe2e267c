package com.puree.followup.admin.followup.service;

import com.puree.followup.domain.followup.dto.PatientFollowUpRecordDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;

/**
 * <AUTHOR>
 * @date 2024/4/8 15:53
 * @description 患者随访分项 服务类
 */
public interface IPatientFollowUpItemRecordService {

    /**
     * 患者加入随访里面的分项
     */
    void patientJoinFollowUpItem(PatientFollowUpRecordDTO patientFollowUpRecordDTO, FollowUp followUp, PatientFollowUpRecord patientFollowUpRecord) ;

}
