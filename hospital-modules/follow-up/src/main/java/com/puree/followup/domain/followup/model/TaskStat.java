package com.puree.followup.domain.followup.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class TaskStat {

    /**
     * 任务总数
     */
    private Integer totalTasks = 0;

    /**
     * 未开始的任务数
     */
    private Integer iscomingTasks = 0;

    /**
     * 进行中的任务数
     */
    private Integer ongoingTasks = 0;

    /**
     * 已完成任务数
     */
    private Integer finishedTasks = 0;

    /**
     * 已过期任务数
     */
    private Integer hasExpiredTasks = 0;

    /**
     * 已终止任务数
     */
    private Integer terminatedTasks = 0;

    /**
     * 随访进度(百分比)
     */
    private BigDecimal finishedFollowUpRate = new BigDecimal("0.00");

    /**
     * 任务完成率(百分比)
     */
    private BigDecimal finishedTaskRate = new BigDecimal("0.00");

    /**
     * 是否为长期随访：这个字段的含义有些特殊，它表示在一个长期随访下
     * 如果患者加入的这些分项中的执行类型都是自定义的，那么该随访就不是长期随访
     * 如果患者加入的这些分享中的执行类型至少有一个是周期循环的，那么该随访就是长期随访
     */
    private Boolean isLongTerm = false;


}