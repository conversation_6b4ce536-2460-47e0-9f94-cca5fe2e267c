package com.puree.followup.enums;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;

/**
 * @ClassName: TaskEventTypeEnum
 * @Date 2024/4/12 11:03
 * @<PERSON> he jian
 * @Description:
 * @Version 1.0
 */
public enum TaskMessageTypeEnum {

    //
    TEXT(1, "text", "文本") ,
    PIC(2, "pic", "图片");


    private Integer index;
    private String name ;
    private String desc;


    TaskMessageTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public static TaskMessageTypeEnum getEnumByName(String name) {
        if (StrUtil.isBlank(name)){
            return null;
        }
        TaskMessageTypeEnum taskEventTypeEnum = Arrays.stream(TaskMessageTypeEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getName(), name) )
                .findAny()
                .orElse(null);
        return taskEventTypeEnum;
    }

    public static TaskMessageTypeEnum getEnumByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        TaskMessageTypeEnum[] metaArr = TaskMessageTypeEnum.values();
        for (TaskMessageTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
