package com.puree.followup.helper;

import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskEventHistoryMapper;
import com.puree.followup.domain.followup.dto.FollowUpEventBusinessDTO;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.PatientTaskEventHistory;
import com.puree.followup.enums.FollowUpJoinSourceTypeEnum;
import com.puree.followup.notification.assembler.FollowUpJoinNotificationAssembler;
import com.puree.followup.notification.assembler.FollowUpSendImageNotificationAssembler;
import com.puree.followup.notification.assembler.FollowUpSendQuestionNotificationAssembler;
import com.puree.followup.notification.assembler.FollowUpSendRemindNotificationAssembler;
import com.puree.followup.notification.assembler.FollowUpSendTextNotificationAssembler;
import com.puree.hospital.common.notification.assembler.INotificationAssembler;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 随访相关的模板通知事件辅助工具
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 18:15
 */
@Component
@AllArgsConstructor(onConstructor = @__(@Autowired))
public class FollowUpTemplateMessageHelper {

    private final PatientFollowUpRecordMapper patientFollowUpRecordMapper;

    private final PatientTaskEventHistoryMapper patientTaskEventHistoryMapper;

    private final QuestionTemplateMessageHelper questionTemplateMessageHelper;

    /**
     *  获取默认的订单业务数据
     * @param event 事件对象
     * @return  问卷数据
     */
    public <E extends BaseFollowUpEvent> FollowUpEventBusinessDTO getBusinessData(E event) {
        if (event.getPatientFollowUpRecordId() == null) {
            return null;
        }
        FollowUpEventBusinessDTO businessDTO = null;
        PatientFollowUpRecord patientFollowUpRecord = patientFollowUpRecordMapper.getById(event.getPatientFollowUpRecordId());
        if (patientFollowUpRecord != null) {
            businessDTO = new FollowUpEventBusinessDTO();
            // 以前的familyId相当于是就诊人Id, patientId指患者Id；现在就诊人Id为patientId, 患者Id为userId
            businessDTO.setPatientId(patientFollowUpRecord.getPatientId());
            businessDTO.setUserId(patientFollowUpRecord.getUserId());
            businessDTO.setFollowUpId(patientFollowUpRecord.getFollowUpId());
            businessDTO.setHospitalId(patientFollowUpRecord.getHospitalId());
            businessDTO.setFollowUpRecordId(patientFollowUpRecord.getId());
            businessDTO.setGroupId(patientFollowUpRecord.getGroupId());
            businessDTO.setImType(FollowUpJoinSourceTypeEnum.findImChatType(patientFollowUpRecord.getSourceType()));
            businessDTO.setJoinType(patientFollowUpRecord.getJoinType());
            // 问卷相关业务数据
             if (event.getEventType().isSendQuestion()) {
                setQuestionBusinessData(businessDTO, event);
            }

        }
        return businessDTO;
    }

    /**
     * 设置患教相关的业务数据
     * @param businessDTO   业务数据对象
     * @param event         时间对象
     */
    private <E extends BaseFollowUpEvent> void setQuestionBusinessData(FollowUpEventBusinessDTO businessDTO, E event) {
        if (event.getPatientTaskEventHistoryId() != null) {
            PatientTaskEventHistory taskEventHistory = patientTaskEventHistoryMapper.getById(event.getPatientTaskEventHistoryId());
            if (taskEventHistory != null) {
                businessDTO.setQuestionId(taskEventHistory.getEvents().getSendId());
                businessDTO.setTaskEventHistoryId(event.getPatientTaskEventHistoryId());
                // 判断问卷内容是否超长
                businessDTO.setContentIsTooLong(questionTemplateMessageHelper.isContentTooLong(businessDTO.getQuestionId()));
            }
        }
    }

    /**
     *  获取模板消息组装器
     * @param event     事件
     * @param businessDTO  问卷业务数据
     * @return  消息通知组装对象
     */
    public <E extends BaseFollowUpEvent> INotificationAssembler getAssembler(E event, FollowUpEventBusinessDTO businessDTO) {
        switch (event.getEventType()) {
            case JOIN:
                return new FollowUpJoinNotificationAssembler(event, businessDTO);
            case SEND_TEXT:
                return new FollowUpSendTextNotificationAssembler(event, businessDTO);
            case SEND_IMAGE:
                return new FollowUpSendImageNotificationAssembler(event, businessDTO);
            case SEND_REMIND:
                return new FollowUpSendRemindNotificationAssembler(event, businessDTO);
            case SEND_QUESTION:
                return new FollowUpSendQuestionNotificationAssembler(event, businessDTO);
        }
        return null;
    }
}
