package com.puree.followup.helper;

import cn.hutool.core.util.StrUtil;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospital;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 医院公众号配置名称helper类
 * </p>
 *
 * <AUTHOR>
 * @date 2025/6/12 11:10
 */
@Slf4j
@Component
public class HospitalWxNameHelper {

    @Resource
    private RemoteHospitalService remoteHospitalService;

    /**
     *  从公众号配置中获取公众号名称
     *   - 如未设置取医院名称
     * @param hospitalId    医院Id
     * @return  公众号配置
     */
    public BusHospitalWechatConfig getHospitalWechatConfig(Long hospitalId) {
        R<BusHospitalWechatConfig> busHospitalWechatConfigR = remoteHospitalService.queryConfigInfo(hospitalId, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
        if (!busHospitalWechatConfigR.isSuccess() || Objects.isNull(busHospitalWechatConfigR.getData()) || StrUtil.isBlank(busHospitalWechatConfigR.getData().getOfficialAccountName())) {
            log.warn("hospitalId:{} 未获取到配置的公众号名称，查询医院名称", hospitalId);
            BusHospitalWechatConfig busHospitalWechatConfig = new BusHospitalWechatConfig();
            R<BusHospital> hospitalInfoR = remoteHospitalService.getHospitalInfo(hospitalId);
            if (!hospitalInfoR.isSuccess() || Objects.isNull(hospitalInfoR.getData())) {
                log.warn("hospitalId:{} 医院信息不存在", hospitalId);
            } else {
                // 设置医院名称
                busHospitalWechatConfig.setOfficialAccountName(hospitalInfoR.getData().getHospitalName());
            }
            return busHospitalWechatConfig;
        }
        return busHospitalWechatConfigR.getData();
    }
}
