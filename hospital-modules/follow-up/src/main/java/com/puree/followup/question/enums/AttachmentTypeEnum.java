package com.puree.followup.question.enums;

import cn.hutool.core.util.StrUtil;

/**
 * @ClassName: AttachmentTypeEnum
 * @Date 2024/6/5 15:36
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum AttachmentTypeEnum {

    PICTURE(0,"picture", "图片附件") ,
    VIDEO(1,"video", "视频附件") ,
    FILE(2,"file", "文件附件") ,


    ;


    private Integer index;
    private String name;
    private String desc;

    AttachmentTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static AttachmentTypeEnum getByName(String name) {
        if (StrUtil.isBlank(name) ) {
            return null;
        }
        AttachmentTypeEnum[] metaArr = AttachmentTypeEnum.values();
        for (AttachmentTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static AttachmentTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        AttachmentTypeEnum[] metaArr = AttachmentTypeEnum.values();
        for (AttachmentTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }



}
