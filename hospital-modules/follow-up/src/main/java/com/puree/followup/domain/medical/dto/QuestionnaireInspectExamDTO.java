package com.puree.followup.domain.medical.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName QuestionnaireInspectExamDTO
 * <AUTHOR>
 * @Description 化验单（检查项）或检查单
 * @Date 2024/7/11 14:24
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuestionnaireInspectExamDTO {

    /**
     * 患者身份证
     */
    @NotBlank(message = "患者身份证 不能为空")
    private String patientIdNumber;
    /**
     * 医院ID
     */
    @NotNull(message = "医院ID 不能为空")
    private Long hospitalId;
    /**
     * item
     */
    @NotNull(message = "item 不能为空")
    private List<InspectExamItemDTO> item;
}
