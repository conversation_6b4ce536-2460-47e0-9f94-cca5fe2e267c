package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 随访表状态修改 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpSwitchDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 启用标识：false.否 true.是
     */
    private Boolean isEnable;

    /**
     * 发布标识：false.否 true.是
     */
    private Boolean isPublish;

    /**
     * 乐观锁
     */
    private Integer revision;


}