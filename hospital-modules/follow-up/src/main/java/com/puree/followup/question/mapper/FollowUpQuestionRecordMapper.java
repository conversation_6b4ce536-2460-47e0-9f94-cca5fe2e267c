package com.puree.followup.question.mapper;

import com.puree.followup.question.domain.model.FollowUpQuestionRecord;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【follow_up_question_record(随访-问卷记录表)】的数据库操作Mapper
* @createDate 2024-06-12 17:22:44
* @Entity com.puree.followup.question.domain.model.FollowUpQuestionRecord
*/
public interface FollowUpQuestionRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(FollowUpQuestionRecord record);

    int insertSelective(FollowUpQuestionRecord record);

    FollowUpQuestionRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FollowUpQuestionRecord record);

    int updateByPrimaryKey(FollowUpQuestionRecord record);

    int updateIsDelete(FollowUpQuestionRecord record) ;

    List<FollowUpQuestionRecord> selectByCondition(FollowUpQuestionRecord record) ;

    int countByCondition(FollowUpQuestionRecord record) ;

}
