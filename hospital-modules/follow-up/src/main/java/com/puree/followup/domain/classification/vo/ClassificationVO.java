package com.puree.followup.domain.classification.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 随访-分类 VO
 * <AUTHOR>
 * @date 2024-04-07 14:29:33
 */
@Data
public class ClassificationVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类的数据类型：0.问卷分类 1.随访分类
     */
    private Integer dataType;

    /**
     * 分类的来源：0.运营平台 1.医院
     */
    private Integer source;

    /**
     * 医院ID（运营平台为-1）
     */
    private Long hospitalId;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标记：-1.系统（不允许修改） 0.未删除 1.已删除
     */
    private Integer isDelete;

}