package com.puree.followup.question.mapper;

import com.puree.followup.question.domain.model.Question;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: QuestionMapper
 * @Date 2024/6/12 16:08
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Mapper
public interface QuestionMapper {

    Question selectById(Question req) ;

    List<Question> selectByCondition(Question req) ;

    void insert(Question req) ;

    void updateByCondition(Question req) ;

    void updateClassifyId(@Param("oldClassifyId") Long oldClassifyId, @Param("newClassifyId") Long newClassifyId) ;

    List<Question> findQuestionRelateFollowUp(Question req) ;

    List<Question> findQuestionNoRelateFollowUp(Question req) ;

    int updateStatusByCloseType(@Param("status") Integer status,
                                 @Param("closeType") Integer closeType, @Param("now") Date now) ;

    List<Long> doctorViewQuestionList(@Param("hospitalId") Long hospitalId, @Param("departmentIdList") List<Long> departmentIdList,
                                      @Param("doctorId") Long doctorId, @Param("questionName") String questionName) ;

}
