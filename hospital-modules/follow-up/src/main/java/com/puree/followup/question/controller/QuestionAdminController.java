package com.puree.followup.question.controller;

import cn.hutool.core.util.StrUtil;
import com.puree.followup.domain.followup.dto.PatientWithRuleDTO;
import com.puree.followup.question.domain.dto.FindPatientToSendDTO;
import com.puree.followup.question.domain.dto.PatientQuestionDTO;
import com.puree.followup.question.domain.dto.QueryPatientDTO;
import com.puree.followup.question.domain.dto.QueryQuestionDTO;
import com.puree.followup.question.domain.dto.QuestionDTO;
import com.puree.followup.question.domain.dto.QuestionStatusDTO;
import com.puree.followup.question.domain.dto.SendQuestionToPatientDTO;
import com.puree.followup.question.domain.vo.QuestionVO;
import com.puree.followup.question.service.QuestionAdminService;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import com.puree.hospital.followup.api.model.QRJoinInDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName: QuestionAdminController
 * @Date 2024/6/12 16:56
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/question/admin")
@Validated
public class QuestionAdminController {

    @Autowired
    private QuestionAdminService questionAdminService;



    @PostMapping("/add")
    @PreAuthorize(hasPermi = "question:add")
    public AjaxResult adminAddQuestion(@Valid @RequestBody QuestionDTO req) {
        questionAdminService.adminAddQuestion(req);
        return AjaxResult.success() ;
    }

    @PutMapping("/edit")
    @PreAuthorize(hasPermi = "question:edit")
    public AjaxResult adminEditQuestion(@Valid @RequestBody QuestionDTO req) {
        questionAdminService.adminEditQuestion(req);
        return AjaxResult.success() ;
    }

    @DeleteMapping("/delete/{id}")
    @PreAuthorize(hasPermi = "question:delete")
    public AjaxResult adminDeleteQuestion(@PathVariable Long id, Integer revision) {
        questionAdminService.adminDeleteQuestion(id, revision);
        return AjaxResult.success() ;
    }

    @PutMapping("/update-status")
    @PreAuthorize(hasPermi = "question:update-status")
    public AjaxResult adminUpdateStatus(QuestionStatusDTO req) {
        questionAdminService.adminUpdateStatus(req);
        return AjaxResult.success() ;
    }

    @GetMapping("/page")
    @PreAuthorize(hasPermi = "question:page")
    public Paging adminQuestionPage(QueryQuestionDTO req) {
        return PageUtil.buildPage(questionAdminService.adminQuestionPage(req)) ;
    }

    @GetMapping("/detail/{id}")
    @PreAuthorize(hasPermi = "question:detail")
    public AjaxResult adminQuestionDetail(@PathVariable Long id) {
        return AjaxResult.success(questionAdminService.adminQuestionDetail(id)) ;
    }

    @PostMapping("/send-to-all-patient")
    public AjaxResult sendQuestionToAllPatient(@RequestBody SendQuestionToPatientDTO req) {
        questionAdminService.sendQuestionToAllPatient(req);
        return AjaxResult.success() ;
    }

    @PostMapping("/find-patient-cache-by-scope")
    public AjaxResult findPatientAndCacheByScope(@RequestBody PatientWithRuleDTO patientWithRuleDTO) {
        return AjaxResult.success(questionAdminService.findPatientAndCacheByScope(patientWithRuleDTO)) ;
    }

    @PostMapping("/find-patient-cache-by-keyword")
    public AjaxResult findPatientAndCacheByKeyword(@RequestBody QueryPatientDTO req) {
        return AjaxResult.success(questionAdminService.findPatientAndCacheByKeyword(req)) ;
    }

    @GetMapping("/check-patient-prepared")
    public AjaxResult checkPatientPrepared(FindPatientToSendDTO req) {
        if ( null== req.getId() ) {
            throw new ServiceException("问卷id缺失") ;
        }
        if ( StrUtil.isBlank(req.getSnowId()) ) {
            throw new ServiceException("snowId缺失") ;
        }
        return AjaxResult.success(questionAdminService.checkPatientPrepared(req)) ;
    }

    @GetMapping("/page-patient-to-send")
    public Paging<List<BusPatientFamilyVo>> pagePatientToSend(FindPatientToSendDTO req) {
        if ( null== req.getId() ) {
            throw new ServiceException("问卷id缺失") ;
        }
        if ( StrUtil.isBlank(req.getSnowId()) ) {
            throw new ServiceException("snowId缺失") ;
        }
        if ( null==req.getPageNum() || null==req.getPageSize() || 0==req.getPageNum() || 0==req.getPageSize() ) {
            throw new ServiceException("分页参数缺失") ;
        }
        return questionAdminService.pagePatientToSend(req) ;
    }

    @PostMapping("/send-to-patient")
    public AjaxResult sendToPatient(@RequestBody SendQuestionToPatientDTO req) {
        if ( null== req.getId() ) {
            throw new ServiceException("问卷id缺失") ;
        }
        if (StrUtil.isBlank(req.getSnowId()) ) {
            throw new ServiceException("snow id缺失") ;
        }
        questionAdminService.sendQuestionToPatient(req);
        return AjaxResult.success() ;
    }

    @GetMapping("/produce-qr-code")
    public AjaxResult produceQrCode(Long id) {
        if ( null==id ) {
            throw new ServiceException("问卷id缺失") ;
        }
        return AjaxResult.success(questionAdminService.produceQrCodeV2(id)) ;
    }

    /**
     * 获取问卷小程序码
     *
     * @param id 问卷id
     * @param appletQrCodeUrl 小程序码url
     * @return 小程序码地址
     */
    @GetMapping("/applet-qrcode")
    public R<String> getAppletQrCode(Long id, String appletQrCodeUrl) {
        if ( null==id ) {
            throw new ServiceException("问卷id缺失") ;
        }
        return R.ok(questionAdminService.getAppletQrCode(id,appletQrCodeUrl));
    }

    @PostMapping("/scan-qrcode-join-question")
    public R scanQrCodeJoinQuestion(@RequestBody QRJoinInDTO req) {
        return questionAdminService.scanQrCodeJoinQuestion(req);
    }

    /**
     * 医院后台 获取问卷列表（只获取问卷基本信息列表，不做统计等复杂查询操作）
     *
     * @param req 查询条件
     * @return 问卷基本信息列表
     * <AUTHOR>
     */
    @GetMapping("/list-info")
    public AjaxResult<List<QuestionVO>> adminQuestionInfoList(QueryQuestionDTO req) {
        return AjaxResult.success(questionAdminService.adminQuestionInfoList(req)) ;
    }

    @GetMapping("/page-patient-question")
    public Paging adminPagePatientQuestion(PatientQuestionDTO req) {
        if (null==req.getQuestionId()) {
            throw new ServiceException("没有问卷id") ;
        }
        return PageUtil.buildPage( questionAdminService.adminPagePatientQuestion(req.getQuestionId(), req.getPatientId(), req.getPatientInfo()
        , req.getDoctorId(), req.getFollowUpId(), req.getBeginDate(), req.getEndDate() )) ;
    }

    @PostMapping("/scan-qrcode-question")
    public AjaxResult scanQrCodeQuestion(@RequestBody QRJoinInDTO req) {
        try{
            return AjaxResult.success(questionAdminService.scanQrCodeJoinQuestionV2(req)) ;
        }catch(ServiceException ex) {
            return AjaxResult.error(ex.getMessage()) ;
        }

    }



}
