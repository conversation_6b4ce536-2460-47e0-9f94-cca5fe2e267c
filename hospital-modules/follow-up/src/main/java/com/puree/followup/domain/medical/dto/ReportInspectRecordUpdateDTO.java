package com.puree.followup.domain.medical.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportInspect
 * <AUTHOR>
 * @Description 体检报告检验项记录表
 * @Date 2024/5/13 17:43
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportInspectRecordUpdateDTO {

    private Long id;
    /**
     * 检验项目名称;如：血细胞分析
     */
    private String subject;

    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 文件路径
     */
    private String fileUrl;
    /**
     * 患者身份证号
     */
    private String patientIdNumber;
    /**
     * 来源类型;DATA_PUSH 数据推送，PATIENT_ADDED 患者添加，BACKGROUND_ADD 后台添加或医生添加，DOCTOR_ADDED 医生添加
     */
    private RecordSourceEnum source;
    /**
     * 检测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
    /**
     * 机构ID
     */
    private String orgId;
    /**
     * 机构名称
     */
    private String orgName;
}
