package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.TaskEventTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访分项任务配置表
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务排序
     */
    private Integer sortNum;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 提醒时间，格式为：HH:mm
     */
    private String executeTime;

    /**
     * 分项任务周期类型：0.自定义 1.周期循环 2.固定周循环
     */
    private Integer intervalType;

    /**
     * 第几天/每隔几天循环/每隔几周循环
     */
    private Integer intervals;

    /**
     * 周循环时记录一周内循环的哪些天(周1-周7)，逗号隔开
     */
    private String weekDays;

    /**
     * 执行事件
     */
    @TableField(typeHandler = TaskEventTypeHandler.class)
    private List<TaskEvent> events;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

    private Date now ;

    private String dayOfWeek ;

}