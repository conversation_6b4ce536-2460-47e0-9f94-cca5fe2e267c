package com.puree.followup.admin.classification.service.impl;

import cn.hutool.core.util.StrUtil;
import com.puree.followup.admin.classification.mapper.ClassificationMapper;
import com.puree.followup.admin.classification.service.IClassificationService;
import com.puree.followup.constants.FollowUpConstants;
import com.puree.followup.domain.classification.dto.ClassificationDTO;
import com.puree.followup.domain.classification.model.Classification;
import com.puree.followup.domain.classification.vo.ClassificationVO;
import com.puree.followup.enums.ClassificationDataTypeEnum;
import com.puree.followup.enums.ClientSourceEnum;
import com.puree.followup.question.domain.dto.DuplicateClassificationDTO;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.bean.BeanUtils;
import lombok.AllArgsConstructor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/7 14:54
 * @description 随访-分类 服务实现
 */
@Service
@AllArgsConstructor
public class ClassificationServiceImpl implements IClassificationService {

    private final ClassificationMapper classificationMapper;
    private final QuestionMapper questionMapper ;

    /**
     * 医院后台 分类列表
     *
     * @param dto 查询条件
     * @return 分类列表
     */
    @Override
    public List<ClassificationVO> hospitalPageList(ClassificationDTO dto) {

        Classification query = new Classification();
        query.setDataType(ClassificationDataTypeEnum.FOLLOW_UP.getIndex());
        query.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        query.setHospitalId(SecurityUtils.getHospitalId());

        PageUtil.startPage();
        List<Classification> classifications = classificationMapper.getList(query);
        if((dto.getPageNum() == null || dto.getPageNum() == 1) && CollectionUtils.isEmpty(classifications)){
            Classification classification = new Classification();
            classification.setName("通用");
            classification.setDataType(ClassificationDataTypeEnum.FOLLOW_UP.getIndex());
            classification.setSource(ClientSourceEnum.HOSPITAL.getIndex());
            classification.setHospitalId(SecurityUtils.getHospitalId());
            classification.setCreateBy(SecurityUtils.getUsername());
            classification.setCreateTime(new Date());
            classification.setUpdateBy(SecurityUtils.getUsername());
            classification.setUpdateTime(new Date());
            classification.setIsDelete(YesNoEnum.SYSTEM.getCode());
            classificationMapper.insert(classification);
            PageUtil.startPage();
            classifications = classificationMapper.getList(query);
        }

        List<ClassificationVO> vos = new ArrayList<>();
        classifications.forEach(item -> {
            ClassificationVO vo = new ClassificationVO();
            BeanUtils.copyProperties(item, vo);
            vos.add(vo);
        });
        if(dto.getPageNum() == null){
            return vos;
        }
        return PageUtil.buildPage(classifications, vos);
    }

    /**
     * 医院后台 根据分类id查询分类详情
     *
     * @param id  分类id
     * @return 分类详情
     */
    @Override
    public ClassificationVO getInfo(Long id) {
        Classification classification = classificationMapper.getById(id);
        if(classification == null){
            throw new ServiceException("分类不存在");
        }
        ClassificationVO vo = new ClassificationVO();
        BeanUtils.copyProperties(classification, vo);
        return vo;
    }

    /**
     * 医院后台 添加分类
     *
     * @param dto 分类详情
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean hospitalAdd(ClassificationDTO dto) {

        Classification query = new Classification();
        query.setDataType(ClassificationDataTypeEnum.FOLLOW_UP.getIndex());
        query.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        query.setHospitalId(SecurityUtils.getHospitalId());
        query.setName(dto.getName());
        List<Classification> classifications = classificationMapper.getList(query);
        if(!CollectionUtils.isEmpty(classifications)){
            throw new ServiceException("分类名称已存在");
        }

        Classification classification = new Classification();
        classification.setName(dto.getName());
        classification.setDataType(ClassificationDataTypeEnum.FOLLOW_UP.getIndex());
        classification.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        classification.setHospitalId(SecurityUtils.getHospitalId());
        classification.setCreateBy(SecurityUtils.getUsername());
        classification.setCreateTime(new Date());
        classification.setUpdateBy(SecurityUtils.getUsername());
        classification.setUpdateTime(new Date());
        classification.setIsDelete(YesNoEnum.NO.getCode());
        int count = classificationMapper.insert(classification);
        return count > 0 ? true : false;
    }

    /**
     * 医院后台 修改分类
     *
     * @param dto 分类详情
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean hospitalEdit(ClassificationDTO dto) {

        if(StringUtils.isEmpty(dto.getName())){
            throw new ServiceException("请填写分类名称");
        }

        Classification classification = classificationMapper.getById(dto.getId());
        if(classification == null){
            throw new ServiceException("分类不存在");
        }

        Classification query = new Classification();
        query.setDataType(ClassificationDataTypeEnum.FOLLOW_UP.getIndex());
        query.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        query.setHospitalId(SecurityUtils.getHospitalId());
        query.setName(dto.getName());
        List<Classification> classifications = classificationMapper.getList(query);
        if(!CollectionUtils.isEmpty(classifications)){
            classifications.forEach(item -> {
                if(!item.getId().equals(dto.getId())){
                    throw new ServiceException("分类名称已存在");
                }
            });
        }

        classification.setName(dto.getName());
        classification.setRevision(dto.getRevision());
        classification.setUpdateBy(SecurityUtils.getUsername());
        classification.setUpdateTime(new Date());
        int count = 0;
        try{
            count = classificationMapper.updateById(classification);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            throw new ServiceException("分类名称已存在");
        }catch (Exception e){
            throw new ServiceException("修改失败");
        }

        return count > 0 ? true : false;
    }

    /**
     * 医院后台 根据分类id删除分类
     *
     * @param id        分类id
     * @param revision  乐观锁
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean hospitalRemove(Long id, Integer revision) {
        Classification classification = classificationMapper.getById(id);
        if(classification == null || classification.getIsDelete().equals(YesNoEnum.YES.getCode()) || !classification.getHospitalId().equals(SecurityUtils.getHospitalId())){
            throw new ServiceException("分类不存在");
        }
        if(classification.getIsDelete().equals(YesNoEnum.SYSTEM.getCode())){
            throw new ServiceException("通用分类不允许被删除");
        }
        classification.setRevision(revision);
        classification.setUpdateBy(SecurityUtils.getUsername());
        classification.setUpdateTime(new Date());
        classification.setIsDelete(YesNoEnum.YES.getCode());
        int count = 0;
        try{
            count = classificationMapper.updateById(classification);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
        }

        return count > 0 ? true : false;
    }


    /**
     * 医院后台 - 问卷分类列表
     *
     * @param dto 查询条件
     * @return 分类列表
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ClassificationVO> listQuestionClassification(ClassificationDTO dto) {

        Classification query = new Classification();
        query.setDataType(ClassificationDataTypeEnum.QUESTIONNAIRE.getIndex());
        query.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        query.setHospitalId( null==SecurityUtils.getHospitalId() ? FollowUpConstants.OPERATE_HOSPITAL_ID : SecurityUtils.getHospitalId() );

        PageUtil.startPage();
        List<Classification> classifications = classificationMapper.getQuestionClassificationList(query);
        if((dto.getPageNum() == null || dto.getPageNum() == 1) && CollectionUtils.isEmpty(classifications)){
            Classification classification = new Classification();
            classification.setName(FollowUpConstants.GENERAL_CLASSIFICATION_NAME);
            classification.setDataType(ClassificationDataTypeEnum.QUESTIONNAIRE.getIndex());
            classification.setSource(ClientSourceEnum.HOSPITAL.getIndex());
            classification.setHospitalId(null==SecurityUtils.getHospitalId() ? FollowUpConstants.OPERATE_HOSPITAL_ID : SecurityUtils.getHospitalId());
            classification.setCreateBy(SecurityUtils.getUsername());
            classification.setCreateTime(new Date());
            classification.setUpdateBy(SecurityUtils.getUsername());
            classification.setUpdateTime(new Date());
            classification.setIsDelete(YesNoEnum.SYSTEM.getCode());
            classificationMapper.insert(classification);
            PageUtil.startPage();
            classifications = classificationMapper.getQuestionClassificationList(query);
        }

        List<ClassificationVO> vos = new ArrayList<>();
        classifications.forEach(item -> {
            ClassificationVO vo = new ClassificationVO();
            BeanUtils.copyProperties(item, vo);
            vos.add(vo);
        });
        if(dto.getPageNum() == null){
            return vos;
        }
        return PageUtil.buildPage(classifications, vos);
    }

    /**
     * 医院后台 添加分类
     *
     * @param dto 分类详情
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addQuestionClassification(ClassificationDTO dto) {

        if( StrUtil.isBlank(dto.getName()) ){
            throw new ServiceException("请填写问卷分类名称");
        }
        if( dto.getName().length() > 30 ){
            throw new ServiceException("问卷分类名称过长");
        }

        Classification query = new Classification();
        query.setDataType(ClassificationDataTypeEnum.QUESTIONNAIRE.getIndex());
        query.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        query.setHospitalId( null==SecurityUtils.getHospitalId() ? 0L : SecurityUtils.getHospitalId() );
        query.setName(dto.getName());
        List<Classification> classifications = classificationMapper.getList(query);
        if(!CollectionUtils.isEmpty(classifications)){
            throw new ServiceException("问卷分类名称已存在");
        }

        Classification classification = new Classification();
        classification.setName(dto.getName());
        classification.setDataType(ClassificationDataTypeEnum.QUESTIONNAIRE.getIndex());
        classification.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        classification.setHospitalId( null==SecurityUtils.getHospitalId() ? FollowUpConstants.OPERATE_HOSPITAL_ID : SecurityUtils.getHospitalId() );
        classification.setCreateBy(SecurityUtils.getUsername());
        classification.setCreateTime(new Date());
        classification.setUpdateBy(SecurityUtils.getUsername());
        classification.setUpdateTime(new Date());
        classification.setIsDelete(YesNoEnum.NO.getCode());
        int count = classificationMapper.insert(classification);
        return count > 0 ? true : false;
    }

    /**
     * 医院后台 修改问卷分类
     *
     * @param dto 分类详情
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean editQuestionClassification(ClassificationDTO dto) {

        if( null==dto.getId() ) {
            throw new ServiceException("no id");
        }

        if( StrUtil.isBlank(dto.getName()) ){
            throw new ServiceException("请填写问卷分类名称");
        }
        if(dto.getName().length() > 30){
            throw new ServiceException("问卷分类名称过长");
        }

        Classification classification = classificationMapper.getById(dto.getId());
        if(classification == null){
            throw new ServiceException("问卷分类不存在");
        }

        Classification query = new Classification();
        query.setDataType(ClassificationDataTypeEnum.QUESTIONNAIRE.getIndex());
        query.setSource(ClientSourceEnum.HOSPITAL.getIndex());
        query.setHospitalId(null==SecurityUtils.getHospitalId() ? FollowUpConstants.OPERATE_HOSPITAL_ID : SecurityUtils.getHospitalId());
        query.setName(dto.getName());
        List<Classification> classifications = classificationMapper.getList(query);
        if(!CollectionUtils.isEmpty(classifications)){
            classifications.forEach(item -> {
                if(!item.getId().equals(dto.getId())){
                    throw new ServiceException("问卷分类名称已存在");
                }
            });
        }

        classification.setName(dto.getName());
        classification.setRevision(dto.getRevision());
        classification.setUpdateBy(SecurityUtils.getUsername());
        classification.setUpdateTime(new Date());
        int count = 0;
        try{
            count = classificationMapper.updateById(classification);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            throw new ServiceException("问卷分类名称已存在");
        }catch (Exception e){
            throw new ServiceException("修改失败");
        }

        return count > 0 ? true : false;
    }

    /**
     * 医院后台 根据分类id删除分类
     *
     * @param id        分类id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteQuestionClassification(Long id, Integer revision) {
        Classification classification = classificationMapper.getById(id);
        if(classification == null || classification.getIsDelete().equals(YesNoEnum.YES.getCode()) || !classification.getHospitalId().equals(SecurityUtils.getHospitalId())){
            throw new ServiceException("分类不存在");
        }
        if(classification.getIsDelete().equals(YesNoEnum.SYSTEM.getCode())){
            throw new ServiceException("通用分类不允许被删除");
        }

        classification.setUpdateBy(SecurityUtils.getUsername());
        classification.setUpdateTime(new Date());
        classification.setIsDelete(YesNoEnum.YES.getCode());
        int count = 0;
        try{
            count = classificationMapper.updateById(classification);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
        }
        Classification query = new Classification();
        query.setDataType(ClassificationDataTypeEnum.QUESTIONNAIRE.getIndex()) ;
        query.setSource(ClientSourceEnum.HOSPITAL.getIndex()) ;
        query.setName(FollowUpConstants.GENERAL_CLASSIFICATION_NAME) ;
        query.setIsDelete(YesNoEnum.SYSTEM.getCode()) ;
        Classification generalClassification = classificationMapper.getOne(query);

        Question updateQuestionDTO = new Question();
        updateQuestionDTO.setClassifyId(generalClassification.getId()) ;
        questionMapper.updateClassifyId(classification.getId(), generalClassification.getId());

        return count > 0 ? true : false;
    }

    public Boolean checkDuplicateClassification(DuplicateClassificationDTO req){

        if ( StrUtil.isBlank(req.getName()) ) {
            throw new ServiceException("name参数缺失") ;
        }
        req.setDataType(ClassificationDataTypeEnum.QUESTIONNAIRE.getIndex());
        Long hospitalId = SecurityUtils.getHospitalId();
        req.setSource( null==hospitalId ? ClientSourceEnum.PLATFORM.getIndex() : ClientSourceEnum.HOSPITAL.getIndex() );
        req.setHospitalId( null==hospitalId ? FollowUpConstants.OPERATE_HOSPITAL_ID : hospitalId);
        int t = classificationMapper.findDuplicateName(req);
        return 0==t ;

    }


}