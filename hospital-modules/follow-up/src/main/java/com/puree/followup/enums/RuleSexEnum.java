package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/5/24 17:55
 * @Description  入组规则中的性别枚举
 */
public enum RuleSexEnum {

    FEMALE(0, "女"),
    MALE(1, "男");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    RuleSexEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static RuleSexEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        RuleSexEnum[] metaArr = RuleSexEnum.values();
        for (RuleSexEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static RuleSexEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        RuleSexEnum[] metaArr = RuleSexEnum.values();
        for (RuleSexEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
