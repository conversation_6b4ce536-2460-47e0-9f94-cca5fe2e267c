package com.puree.followup.question.service.impl;

import com.puree.followup.admin.followup.mapper.FollowUpItemExecutorMapper;
import com.puree.followup.admin.followup.mapper.FollowUpItemTaskMapper;
import com.puree.followup.domain.followup.model.FollowUpItemExecutor;
import com.puree.followup.domain.followup.model.FollowUpItemTask;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.enums.TaskEventTypeEnum;
import com.puree.followup.question.domain.model.FollowUpQuestionRecord;
import com.puree.followup.question.mapper.FollowUpQuestionRecordMapper;
import com.puree.followup.question.service.FollowUpQuestionService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: FollowUpQuestionServiceImpl
 * @Date 2024/7/23 10:05
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Service
@Slf4j
public class FollowUpQuestionServiceImpl implements FollowUpQuestionService {

    @Autowired
    private FollowUpItemTaskMapper followUpItemTaskMapper ;
    @Autowired
    private FollowUpItemExecutorMapper followUpItemExecutorMapper;
    @Autowired
    private FollowUpQuestionRecordMapper followUpQuestionRecordMapper ;



    @Async
    public void bindFollowUpQuestion(Long followUpId){

        Date now = new Date();
        String username = SecurityUtils.getUsername();
        List<FollowUpQuestionRecord> followUpQuestionRecordList = new ArrayList<>() ;

        this.handleFollowUpQuestionFromTask(followUpId, followUpQuestionRecordList);
        this.handleFollowUpQuestionFromExecutor(followUpId, followUpQuestionRecordList);

        FollowUpQuestionRecord delDTO = new FollowUpQuestionRecord() ;
        delDTO.setFollowUpId(followUpId) ;
        delDTO.setUpdateTime(now);
        delDTO.setUpdateBy(username) ;
        delDTO.setIsDelete(YesNoEnum.YES.getCode()) ;
        followUpQuestionRecordMapper.updateIsDelete(delDTO) ;

        followUpQuestionRecordList.forEach(followUpQuestionRecord -> {
            followUpQuestionRecord.setCreateBy(username);
            followUpQuestionRecord.setUpdateBy(username);
            followUpQuestionRecord.setCreateTime(now);
            followUpQuestionRecord.setUpdateTime(now);
            followUpQuestionRecord.setIsDelete(YesNoEnum.NO.getCode()) ;
            followUpQuestionRecordMapper.insert(followUpQuestionRecord) ;
        });

    }

    private void handleFollowUpQuestionFromTask(Long followUpId, List<FollowUpQuestionRecord> followUpQuestionRecordList){

        FollowUpItemTask itemTaskQuery = new FollowUpItemTask() ;
        itemTaskQuery.setFollowUpId(followUpId);
        itemTaskQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        List<FollowUpItemTask> followUpItemTaskList = followUpItemTaskMapper.getList(itemTaskQuery);
        if ( null==followUpItemTaskList || followUpItemTaskList.isEmpty() ) {
            return ;
        }

        for (FollowUpItemTask itemTask : followUpItemTaskList) {

            List<TaskEvent> taskEventList = itemTask.getEvents();
            if ( null==taskEventList || taskEventList.isEmpty() ) {
                continue;
            }

            for (TaskEvent taskEvent : taskEventList) {
                if ( TaskEventTypeEnum.QUESTIONNAIRE.getIndex().equals(taskEvent.getEventType()) ) {
                    FollowUpQuestionRecord followUpQuestionRecord = new FollowUpQuestionRecord();
                    followUpQuestionRecord.setQuestionId(taskEvent.getSendId()) ;
                    followUpQuestionRecord.setFollowUpId(itemTask.getFollowUpId()) ;
                    followUpQuestionRecord.setItemId(itemTask.getItemId()) ;
                    followUpQuestionRecord.setEventId(taskEvent.getId()) ;
                    followUpQuestionRecordList.add(followUpQuestionRecord) ;
                }
            }

        }

    }

    private void handleFollowUpQuestionFromExecutor(Long followUpId,  List<FollowUpQuestionRecord> followUpQuestionRecordList){

        FollowUpItemExecutor itemExecutorQuery = new FollowUpItemExecutor();
        itemExecutorQuery.setFollowUpId(followUpId);
        itemExecutorQuery.setIsDelete(YesNoEnum.NO.getCode());
        List<FollowUpItemExecutor> itemExecutorList = followUpItemExecutorMapper.getList(itemExecutorQuery);
        if ( null==itemExecutorList || itemExecutorList.isEmpty() ) {
            return ;
        }

        for (FollowUpItemExecutor itemExecutor : itemExecutorList) {

            if ( null!=itemExecutor.getQuestionnaireId() ) {
                FollowUpQuestionRecord followUpQuestionRecord = new FollowUpQuestionRecord();
                followUpQuestionRecord.setQuestionId(itemExecutor.getQuestionnaireId()) ;
                followUpQuestionRecord.setFollowUpId(itemExecutor.getFollowUpId()) ;
                followUpQuestionRecord.setItemId(itemExecutor.getItemId()) ;
                followUpQuestionRecordList.add(followUpQuestionRecord) ;
            }

            List<TaskEvent> taskEventList = itemExecutor.getEvents();
            if ( null==taskEventList || taskEventList.isEmpty() ) {
                continue;
            }

            for (TaskEvent taskEvent : taskEventList) {
                if ( TaskEventTypeEnum.QUESTIONNAIRE.getIndex().equals(taskEvent.getEventType()) ) {
                    FollowUpQuestionRecord followUpQuestionRecord = new FollowUpQuestionRecord();
                    followUpQuestionRecord.setQuestionId(taskEvent.getSendId()) ;
                    followUpQuestionRecord.setFollowUpId(itemExecutor.getFollowUpId()) ;
                    followUpQuestionRecord.setItemId(itemExecutor.getItemId()) ;
                    followUpQuestionRecord.setEventId(taskEvent.getId()) ;
                    followUpQuestionRecordList.add(followUpQuestionRecord) ;
                }
            }

        }

    }







}
