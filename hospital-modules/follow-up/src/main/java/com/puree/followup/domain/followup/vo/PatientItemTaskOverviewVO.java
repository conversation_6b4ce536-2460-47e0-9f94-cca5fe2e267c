package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 患者分项任务概览 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientItemTaskOverviewVO {

    /**
     * 任务id
     */
    private Long id;

    /**
     * 任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginDay;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDay;

    /**
     * 任务完成状态： iscoming:即将开始、ongoing:待完成、finished:已完成、hasexpired:已过期、terminated:已终止
     */
    private String taskStatus;


    /**
     * 事件总数(不包含已终止的事件)
     */
    private Integer totalEvents;

    /**
     * 即将开始事件数
     */
    private Integer iscomingEvents;

    /**
     * 待完成事件数
     */
    private Integer ongoingEvents;

    /**
     * 已完成事件
     */
    private Integer finishedEvents;

    /**
     * 已过期事件数
     */
    private Integer hasExpiredEvents;

    /**
     * 事件名称
     */
    private String name;



}