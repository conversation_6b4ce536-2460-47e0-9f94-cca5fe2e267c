package com.puree.followup.question.service;

import com.puree.followup.domain.followup.dto.PatientWithRuleDTO;
import com.puree.followup.question.domain.dto.FindPatientToSendDTO;
import com.puree.followup.question.domain.dto.QueryPatientDTO;
import com.puree.followup.question.domain.dto.QueryQuestionDTO;
import com.puree.followup.question.domain.dto.QuestionDTO;
import com.puree.followup.question.domain.dto.QuestionStatusDTO;
import com.puree.followup.question.domain.dto.SendQuestionToPatientDTO;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.vo.QuestionVO;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.followup.api.model.QRJoinInDTO;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: QuestionAdminService
 * @Date 2024/6/12 18:52
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
public interface QuestionAdminService {

    void adminAddQuestion(QuestionDTO req) ;

    void adminEditQuestion(QuestionDTO req) ;

    void adminDeleteQuestion(Long id, Integer revision) ;

    void adminUpdateStatus(QuestionStatusDTO req) ;

    List<QuestionVO> adminQuestionPage(QueryQuestionDTO req) ;

    QuestionVO adminQuestionDetail(Long id) ;

    void adminCopyQuestion(Long id);

    void sendQuestionToAllPatient(SendQuestionToPatientDTO req) ;

    String findPatientAndCacheByScope(PatientWithRuleDTO patientWithRuleDTO) ;

    String findPatientAndCacheByKeyword(QueryPatientDTO req) ;

    Boolean checkPatientPrepared(FindPatientToSendDTO req) ;

    Paging<List<BusPatientFamilyVo>> pagePatientToSend(FindPatientToSendDTO req) ;

    void sendQuestionToPatient(SendQuestionToPatientDTO req) ;

    String produceQrCode(Long id) ;

    R scanQrCodeJoinQuestion(QRJoinInDTO req) ;

    List<QuestionVO> adminQuestionInfoList(QueryQuestionDTO req);

    List<PatientQuestionRecord> adminPagePatientQuestion(Long questionId, Long patientId , String patientInfo, Long doctorId, Long followUpId, Date beginDate, Date endDate) ;

    String produceQrCodeV2(Long id) ;

    Long scanQrCodeJoinQuestionV2(QRJoinInDTO req) ;

    /**
     * 获取问卷小程序码
     *
     * @param id 问卷id
     * @param appletQrCodeUrl 小程序码url
     * @return 小程序码地址
     */
    String getAppletQrCode(Long id,String appletQrCodeUrl);
}
