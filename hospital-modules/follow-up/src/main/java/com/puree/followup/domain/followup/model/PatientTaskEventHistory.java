package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 患者随访事件执行历史表
 *
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientTaskEventHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 患者随访记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemRecordId;

    /**
     * 任务执行ID
     */
    private Long taskHistoryId;

    /**
     * 任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;
    /**
     * 事件ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long eventId;

    /**
     * 事件类型：1发送消息   2发送患教  3发送问卷
     */
    private Integer eventType;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 执行事件
     */
    //@TableField(typeHandler = JsonTypeHandler.class)
    private TaskEvent events;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 事件执行时间
     */
    private Date eventExecuteTime;

    /**
     * 事件提醒时间
     */
    private Date remindTime;

    /**
     * 事件提醒状态：0.未提醒 1.已提醒
     */
    private Integer eventRemindStatus;

    /**
     * 事件完成状态：0.未开始 1.消息未读/问卷未填写 2.消息已读/问卷已填写 3.已过期 4.已终止
     */
    private Integer eventFinishStatus;

    /**
     * 是否终止未来事件：0.否 1.是
     */
    private Integer isTerminateFuture;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0 ;

    /**
     * 事件完成状态列表
     */
    private List<Integer> eventFinishStatusList;

    /**
     * 任务执行ID列表
     */
    private List<Long> taskHistoryIds;

    /**
     * 事件执行ID列表
     */
    private List<Long> eventHistoryIds;

    /**
     * 任务完成状态：0.未开始(即将开始，未来的) 1.进行中(未完成、待完成，今天的) 2.已完成 3.已过期 4.已终止
     */
    private Integer taskStatus;

    /**
     * ID列表
     */
    private List<Long> ids;

}