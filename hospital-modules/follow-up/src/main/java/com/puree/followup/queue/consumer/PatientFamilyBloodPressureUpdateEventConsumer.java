package com.puree.followup.queue.consumer;

import cn.hutool.core.collection.CollectionUtil;
import com.puree.followup.admin.medical.service.ReportRegularRecordService;
import com.puree.followup.domain.medical.constant.RegularRecordEnum;
import com.puree.followup.domain.medical.model.ReportRecord;
import com.puree.followup.domain.medical.model.ReportRegularRecord;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import com.puree.hospital.common.redis.mq.annotation.RedisConsumer;
import com.puree.hospital.ehr.api.model.event.PatientFamilyBloodPressureUpdateEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 就诊人血压数据跟新事件
 * </p>
 *
 * <AUTHOR>
 * @date 2025/2/19 20:01
 */
@Slf4j
@SuppressWarnings("unused")
@RedisConsumer(topic = PatientFamilyBloodPressureUpdateEvent.TOPIC, group = "follow-up")
public class PatientFamilyBloodPressureUpdateEventConsumer extends RedisStreamConsumer<PatientFamilyBloodPressureUpdateEvent> {

    @Resource
    private ReportRegularRecordService reportRegularRecordService;

    @Override
    public void onMessage(RedisMessage<PatientFamilyBloodPressureUpdateEvent> message) {
        log.info("收到就诊人血压数据更新事件：{}", message);
        List<ReportRegularRecord> reportRegularRecords = buildRecordList(message.getBody());
        if (CollectionUtil.isEmpty(reportRegularRecords)) {
            log.error("构建监看报告血压数据数据为空");
            return;
        }
        reportRegularRecordService.saveBatchAndSummery(reportRegularRecords);
        // todo  触发随访事件
    }


    /**
     * 构建健康报告数据
     *
     * @param event 血压数据更新事件
     * @return 构建后的数据
     */
    private List<ReportRegularRecord> buildRecordList(PatientFamilyBloodPressureUpdateEvent event) {
        if (Objects.isNull(event)) {
            return Lists.newArrayList();
        }
        List<ReportRegularRecord> reportRegularRecords = Lists.newArrayList();
        //操作时间
        Date date = Objects.nonNull(event.getOpTime()) ? new Date(event.getOpTime() * 1000) : new Date();
        ReportRecord reportRecord = new ReportRecord();
        reportRecord.setHospitalId(event.getHospitalId());
        reportRecord.setPatientId(event.getFamilyId());
        reportRecord.setPatientIdNumber(event.getFamilyIdNumber());
        if (Objects.nonNull(event.getSys())) {
            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getSys() + "", reportRecord, RegularRecordEnum.LEFT_SYSTOLIC, date));
        }
        if (Objects.nonNull(event.getDia())) {
            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getDia() + "", reportRecord, RegularRecordEnum.LEFT_DIASTOLIC, date));
        }
        if (Objects.nonNull(event.getSys2())) {
            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getSys2() + "", reportRecord, RegularRecordEnum.SYSTOLIC, date));
        }
        if (Objects.nonNull(event.getDia2())) {
            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getDia2() + "", reportRecord, RegularRecordEnum.DIASTOLIC, date));
        }
        if (Objects.nonNull(event.getPlu())) {
            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getPlu() + "", reportRecord, RegularRecordEnum.HEART_RATE, date));
        }
        if (Objects.nonNull(event.getIad())) {
            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getIad() + "", reportRecord, RegularRecordEnum.IAD, date));
        }
        return reportRegularRecords;
    }
}
