package com.puree.followup.domain.drools.builder;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName GlobalBuilder
 * <AUTHOR>
 * @Description 全局变量
 * @Date 2024/4/3 17:49
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class GlobalBuilder {
    /**
     * global 开始
     */
    private final static String GLOBAL_START = "global";
    /**
     * 全量 class name（如 com.xxx.xxx.entity.calculat）
     */
    private String className;
    /**
     * 简单 class name（如 calculat）
     */
    private String simpleClassName;
    /**
     * 变量名
     */
    private String variableName;

    /**
     * @Param
     * @Return java.lang.String
     * @Description 生成 String 串
     * <AUTHOR>
     * @Date 2024/4/9 16:26
     **/
    public String builderGlobalString(){
        StrBuilder builder = new StrBuilder();
        if (StrUtil.isNotEmpty(className) && StrUtil.isNotEmpty(variableName)  ) {
            builder.append(GLOBAL_START)
                    .append(StrPool.C_SPACE).append(className)
                    .append(StrPool.C_SPACE).append(variableName).append(StrPool.C_LF);
        }
        return builder.toString();
    }

}
