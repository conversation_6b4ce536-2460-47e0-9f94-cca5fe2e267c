package com.puree.followup.domain.drools.constant;

/**
 * @ClassName JoinRuleVariableManipulationConstant
 * <AUTHOR>
 * @Description 入组常量
 * @Date 2024/4/23 19:20
 * @Version 1.0
 */
public abstract class JoinRuleConstant {
    /**
     * 总检建议
     */
    public final static String CHECK_RECOMMENDATIONS = "总检建议";
    /**
     * 小结
     */
    public final static String BRIEF_SUMMARY = "小结";
    /**
     * 患者资料
     */
    public final static String PATIENT_INFORMATION = "患者资料";
    /**
     * 体检报告
     */
    public final static String MEDICAL_REPORT = "体检报告";

    /**
     * 总检建议（字符类）
     */
    public final static String CONCLUSIONS_SUGGEST = "/conclusions/examSuggestDTOS[suggest {} ]";
    /**
     * 检验项指标（数值类）
     */
    public final static String INSPECTSUMMARIES_ITEMS_RESULT = "/inspectSummaries/items[name == \"{}\" && result {}]";
    /**
     * 区间
     */
    public final static String INSPECTSUMMARIES_ITEMS_RESULT_INTERVAL = "/inspectSummaries/items[name == \"{}\" && {}]";
    /**
     *
     */
    public final static String INSPECTSUMMARIES_ITEMS_RESULT_VALUE = "result";

    /**
     * 检验项小结（字符类）
     */
    public final static String INSPECTSUMMARIES_SUMMARY_SUBJECT = "/inspectSummaries[ subject == \"{}\" && summary {}]";
    /**
     * 检验项小结（字符类）体检报告用
     */
    public final static String INSPECTSUMMARIES_SUMMARY = "/inspectSummaries[ summary {}]";


    /**
     * 历史数据-患者资料
     */
    public final static String HISTORY_PATIENT_FAMILY = "/patientFamilyVo[{}]";
    /**
     * 历史数据-总检建议（字符类）
     */
    public final static String HISTORY_CONCLUSIONS_SUGGEST = "/infoDTO/conclusions/examSuggestDTOS[suggest {} ]";
    /**
     * 历史数据-检验项小结（字符类）体检报告用
     */
    public final static String HISTORY_INSPECTSUMMARIES_SUMMARY = "/infoDTO/inspectSummaries[ summary {}]";
    /**
     * 检查项小结（字符类） 体检报告用
     */
    public final static String HISTORY_EXAMSUMMARIES_SUMMARY = "/infoDTO/examSummaries[summary {}]";
    /**
     * 历史数据-检查项
     */
    public final static String HISTORY_EXAM_ITEM = "examItem.get(\"{}\").desc {}";
    /**
     * 历史数据-检验项
     */
    public final static String HISTORY_INSPECT_ITEM = "inspectItem.get(\"{}\").result {}";
    /**
     * 历史数据-检查项小结
     */
    public final static String HISTORY_EXAM_SUMMARY = "/examSummary[name == \"{}\" && summary {}]";
    /**
     * 历史数据-检验项小结
     */
    public final static String HISTORY_INSPECT_SUMMARY = "/inspectSummary[name == \"{}\" && summary {}]";


    /**
     * 检查项小结（字符类）
     */
    public final static String EXAMSUMMARIES_SUMMARY_SUBJECT = "/examSummaries[subject == \"{}\" && summary {}]";
    /**
     * 检查项小结（字符类） 体检报告用
     */
    public final static String EXAMSUMMARIES_SUMMARY = "/examSummaries[summary {}]";
    /**
     * 小项检查结果描述(字符类)
     */
    public final static String EXAMSUMMARIES_ITEMS_DESC = "/examSummaries/items[name == \"{}\" && desc {}]";
//    /**
//     * 常规检测项目(字符类)
//     */
//    private final static String EXAMSUMMARIES_ITEMS_DESC = "/regularExam/items/desc[]";
    /**
     * 返回变量名
     */
    public final static String RESULT_VARIABLE_NAME = "result";

}
