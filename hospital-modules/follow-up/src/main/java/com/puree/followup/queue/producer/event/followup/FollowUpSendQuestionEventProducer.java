package com.puree.followup.queue.producer.event.followup;

import com.puree.hospital.common.redis.mq.RedisStreamProducer;
import com.puree.hospital.common.redis.mq.annotation.RedisProducer;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendQuestionEvent;

/**
 *  随访问卷事件生产者
 * <AUTHOR>
 * @date 2025/2/28 14:34
 */
@RedisProducer(topic = FollowUpSendQuestionEvent.TOPIC)
public class FollowUpSendQuestionEventProducer extends RedisStreamProducer<FollowUpSendQuestionEvent> {
}
