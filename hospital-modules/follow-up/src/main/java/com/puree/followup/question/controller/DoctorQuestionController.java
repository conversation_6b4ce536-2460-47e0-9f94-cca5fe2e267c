package com.puree.followup.question.controller;

import cn.hutool.core.util.StrUtil;
import com.puree.followup.question.domain.dto.DoctorQuestionDTO;
import com.puree.followup.question.service.DoctorQuestionService;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName: DoctorQuestionController
 * @Date 2024/7/23 18:24
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/question/doctor")
@Validated
public class DoctorQuestionController {

    @Autowired
    private DoctorQuestionService doctorQuestionService ;



    @GetMapping("/page")
    public Paging doctorPageQuestion(DoctorQuestionDTO req) {
        if ( null==req.getPageNum() || null==req.getPageSize() || 0==req.getPageNum() || 0==req.getPageSize() ) {
            throw new ServiceException("分页参数缺失") ;
        }
        if ( null==req.getHospitalId() || null==req.getDoctorId()) {
            throw new ServiceException("医院或者医生参数缺失") ;
        }
        return PageUtil.buildPage(doctorQuestionService.doctorPageQuestion(req)) ;
    }

    @GetMapping("/page-patient-question")
    public Paging doctorPagePatientQuestion(DoctorQuestionDTO req) {
        if ( null==req.getPageNum() || null==req.getPageSize() || 0==req.getPageNum() || 0==req.getPageSize() ) {
            throw new ServiceException("分页参数缺失") ;
        }
        if ( null==req.getHospitalId() || null==req.getDoctorId()) {
            throw new ServiceException("医院或者医生参数缺失") ;
        }
        if ( null==req.getQuestionId() ) {
            throw new ServiceException("问卷id缺失") ;
        }
        return PageUtil.buildPage(doctorQuestionService.doctorPagePatientQuestion(req)) ;
    }

    @GetMapping("/patient-question-detail/{id}")
    public AjaxResult doctorDetailPatientQuestion(@PathVariable Long id) {
        return AjaxResult.success(doctorQuestionService.doctorDetailPatientQuestion(id)) ;
    }

    @PostMapping("/send-question")
    public AjaxResult doctorSendQuestion(@RequestBody DoctorQuestionDTO req) {
        if ( null==req.getHospitalId() ) {
            throw new ServiceException("医院id缺失") ;
        }
        if ( null==req.getQuestionId() ) {
            throw new ServiceException("问卷id缺失") ;
        }
        if ( null==req.getUserId() || null==req.getPatientId() ) {
            throw new ServiceException("患者信息缺失") ;
        }
        if ( null==req.getDoctorId() || StrUtil.isBlank(req.getDoctorName())) {
            throw new ServiceException("医生信息缺失") ;
        }
        if ( null==req.getGroupId() ) {
            throw new ServiceException("群组id缺失") ;
        }
        doctorQuestionService.doctorSendQuestion(req);
        return AjaxResult.success() ;
    }

    @GetMapping("/detail/{id}")
    public AjaxResult doctorDetailQuestion(@PathVariable Long id) {
        return AjaxResult.success(doctorQuestionService.doctorDetailQuestion(id)) ;
    }


}
