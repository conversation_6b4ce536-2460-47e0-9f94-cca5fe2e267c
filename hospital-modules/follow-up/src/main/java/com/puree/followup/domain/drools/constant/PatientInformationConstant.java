package com.puree.followup.domain.drools.constant;

import cn.hutool.core.util.NumberUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName PatientInformationConstant
 * <AUTHOR>
 * @Description 患者资料常量
 * @Date 2024/4/30 15:59
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum PatientInformationConstant {
    SEX(1,"性别",  "sex","大于号"),
    AGE(2, "年龄", " age","年龄"),
    NATION(3, "民族", " nation","民族"),
    MARITAL_STATUS(4, "婚姻状况", "maritalStatus","婚姻状况"),
    location(5, "住址所在地区", "location","所在区"),
    WORK_UNIT(6, "工作单位", "workUnit","工作单位"),
    DETAIL_ADDRESS(7, "详细地址", "detailAddress","详细地址"),
    PROFESSION(8, "职业", "profession","职业"),
    EDUCATION_LEVEL(9, "文化程度", "educationLevel","文化程度"),
;


    private static Map<String, PatientInformationConstant> resultExceptionStateEnumHashMap = new HashMap<>(32);

    /**
     * 启动时缓存方便取 枚举
     */
    static {
        for (PatientInformationConstant value : values()) {
            resultExceptionStateEnumHashMap.put(value.getCode(), value);
        }
    }

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;

    /**
     * @Param codeStr
     * @Return com.puree.followup.domain.drools.constant.PatientInformationConstant
     * @Description 通过 code 取常量
     * <AUTHOR>
     * @Date 2024/4/30 16:24
     **/
    public static PatientInformationConstant getEnumByCode(String codeStr) {
        return resultExceptionStateEnumHashMap.get(codeStr);
    }
}
