package com.puree.followup.domain.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.medical.constant.IsHandleEnum;
import com.puree.followup.domain.medical.constant.RecordTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportRecord
 * <AUTHOR>
 * @Description 体检报告记录表
 * @Date 2024/5/13 16:46
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportRecord {

    /**
     * 主键
     */
    private Long id;
    /**
     * 原始报告的json
     */
    private String reportJson;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 记录类型;SINGLE_INSPECTION 单项检查、MEDICAL_REPORT 体检报告
     */
    private RecordTypeEnum recordType;
    /**
     * 设备编号
     */
    private String deviceId;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 检测ID
     */
    private String examId;
    /**
     * 检测日期
     */
    private Date examDate;
    /**
     * 是否已经处理;PROCESSED 为未处理，NOT_PROCESSED 为已处理
     */
    private IsHandleEnum isHandle;
    /**
     * 体检报告ID
     */
    private String reportId;
    /**
     * 体检报告版本号
     */
    private Integer reportVer;
    /**
     * 体检报告日期
     */
    private Date reportDate;
    /**
     * 患者身份证号
     */
    private String patientIdNumber;
    /**
     * 患者 ID
     */
    private Long patientId;
    /**
     * 就诊人 ID
     */
    private Long familyId;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
}
