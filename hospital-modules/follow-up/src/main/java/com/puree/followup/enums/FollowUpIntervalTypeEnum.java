package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  分项任务执行周期的类型：0.自定义 1.周期循环 2.固定周循环
 */
public enum FollowUpIntervalTypeEnum {

    CUSTOM(0, "custom", "自定义"),
    PERIOD_CYCLE(1, "periodCycle", "周期循环"),
    WEEK_CYCLE(2, "weekCycle", "固定周循环");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpIntervalTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpIntervalTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpIntervalTypeEnum[] metaArr = FollowUpIntervalTypeEnum.values();
        for (FollowUpIntervalTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpIntervalTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpIntervalTypeEnum[] metaArr = FollowUpIntervalTypeEnum.values();
        for (FollowUpIntervalTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
