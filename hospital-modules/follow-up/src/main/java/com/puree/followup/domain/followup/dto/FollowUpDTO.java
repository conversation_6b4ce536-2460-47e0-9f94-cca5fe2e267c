package com.puree.followup.domain.followup.dto;

import com.puree.followup.enums.DepartmentLimitEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 随访表 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访名称
     */
    private String name;

    /**
     * 随访描述
     */
    private String desc;

    /**
     * 备注说明(仅内部可见)
     */
    private String remark;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 分类名称
     */
    private String classifyName;

    /**
     * 启用标识：0.否 1.是
     */
    private Integer isEnable;

    /**
     * 发布标识：0.否 1.是
     */
    private Integer isPublish;

    /**
     * 随访周期类型(到期时间)：longTerm:长期、joinIn:加入随访X天后结束
     */
    private String expireType;

    /**
     * 加入随访X天后结束
     */
    private Integer expireDay;

    /**
     * 可用科室：unlimited:不限、custom:自定义
     */
    private String departmentLimit;

    /**
     * 可用医生：unlimited:不限、custom:自定义
     */
    private String doctorLimit;

    /**
     * 随访分项列表
     */
    private List<FollowUpItemDTO> subitemList;

    /**
     * 随访任务过期的启用标识：false.禁用 true.启用
     */
    private Boolean taskExpireSwitch;

    /**
     * 随访任务过期X天后不能再完成
     */
    private Integer taskExpireDay;

    /**
     * 随访超期预警的启用标识：false.禁用 true.启用
     */
    private Boolean taskExpireWarnSwitch;

    /**
     * 随访任务连续X次过期进行超期预警
     */
    private Integer taskExpireTimes;

    /**
     * 入组类型：auto:自动入组 、patientAgree:需患者同意
     */
    private String joinType;

    /**
     * 入组规则
     */
    private List<FollowUpJoinRuleDTO> joinRuleList;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * redis锁的key
     */
    private String redisKey;

}