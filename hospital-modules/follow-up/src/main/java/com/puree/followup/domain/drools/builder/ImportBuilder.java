package com.puree.followup.domain.drools.builder;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName ImportBuilder
 * <AUTHOR>
 * @Description 导入生成器
 * @Date 2024/4/3 17:30
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class ImportBuilder {

    /**
     * import 开始
     */
    private final static String IMPORT_START = "import";
    /**
     * 全量 class name（如 com.xxx.xxx.entity.calculat）
     */
    private String className;
    /**
     * 简单 class name（如 calculat）
     */
    private String simpleClassName;

    /**
     * @Param
     * @Return java.lang.String
     * @Description 生成 String 串
     * <AUTHOR>
     * @Date 2024/4/9 16:26
     **/
    public String builderImportString(){
        StrBuilder builder = new StrBuilder();
        if (StrUtil.isNotEmpty(className)) {
            builder.append(IMPORT_START).append(StrPool.C_SPACE).append(className).append(StrPool.C_LF);
        }
        return builder.toString();
    }
}
