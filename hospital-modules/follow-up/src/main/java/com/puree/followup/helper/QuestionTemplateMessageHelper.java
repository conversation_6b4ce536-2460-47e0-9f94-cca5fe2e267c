package com.puree.followup.helper;

import com.puree.followup.question.domain.dto.QuestionEventBusinessDTO;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.followup.question.notification.assembler.QuestionInviteNotificationAssembler;
import com.puree.followup.question.util.SendQuestionToPatientUtil;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.notification.assembler.INotificationAssembler;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *  问卷相关的模板通知事件辅助工具
 * <AUTHOR>
 * @date 2025/3/4 10:00
 */
@Component
@AllArgsConstructor(onConstructor = @__(@Autowired))
public class QuestionTemplateMessageHelper {

    private final PatientQuestionRecordMapper patientQuestionRecordMapper;

    private final QuestionMapper questionMapper;

    private final SendQuestionToPatientUtil sendQuestionToPatientUtil;

    /**
     *  获取默认的订单业务数据
     * @param event 事件对象
     * @return  问卷数据
     */
    public <E extends QuestionInviteEvent> QuestionEventBusinessDTO getBusinessData(E event) {
        QuestionEventBusinessDTO questionEventBusinessDTO = null;
        if (event.getPatientQuestionRecordId() != null) {
            // 组装业务参数
            PatientQuestionRecord patientQuestionRecord = patientQuestionRecordMapper.selectByPrimaryKey(event.getPatientQuestionRecordId());
            if (patientQuestionRecord != null) {
                // 以前的familyId相当于是就诊人Id, patientId指患者Id；现在就诊人Id为patientId, 患者Id为userId
                questionEventBusinessDTO = new QuestionEventBusinessDTO();
                questionEventBusinessDTO.setQuestionId(patientQuestionRecord.getQuestionId());
                questionEventBusinessDTO.setPatientId(patientQuestionRecord.getPatientId());
                questionEventBusinessDTO.setUserId(patientQuestionRecord.getUserId());
                questionEventBusinessDTO.setDoctorName(patientQuestionRecord.getDoctorName());
                questionEventBusinessDTO.setPatientQuestionRecordId(patientQuestionRecord.getId());
                questionEventBusinessDTO.setHospitalId(patientQuestionRecord.getHospitalId());
                questionEventBusinessDTO.setContentIsTooLong(isContentTooLong(patientQuestionRecord.getQuestionId()));
            }
        }
        return questionEventBusinessDTO;
    }

    /**
     *  判断问卷内容是否过长
     * @param questionId
     * @return
     */
    public boolean isContentTooLong(Long questionId) {
        Question questionQuery = new Question();
        questionQuery.setId(questionId);
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());
        Question questionDO = questionMapper.selectById(questionQuery);
        if (questionDO != null) {
           return sendQuestionToPatientUtil.isContentTooLong(questionDO.getQuestionContent());
        }
        return false;
    }

    /**
     *  获取模板消息组装器
     * @param event     事件
     * @param businessDTO  问卷业务数据
     * @return  消息通知组装对象
     */
    public <E extends QuestionInviteEvent> INotificationAssembler getAssembler(E event, QuestionEventBusinessDTO businessDTO) {
        return new QuestionInviteNotificationAssembler(event, businessDTO);
    }

}
