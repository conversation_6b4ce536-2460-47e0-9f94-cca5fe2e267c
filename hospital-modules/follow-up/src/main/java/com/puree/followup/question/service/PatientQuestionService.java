package com.puree.followup.question.service;

import com.puree.followup.question.domain.dto.PatientQuestionDTO;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.vo.PatientQuestionVO;

import java.util.List;

/**
 * @ClassName: PatientQuestionService
 * @Date 2024/6/17 18:00
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public interface PatientQuestionService {

    List<PatientQuestionRecord> patientPageQuestion(PatientQuestionDTO req) ;

    PatientQuestionVO patientDetailQuestion(Long id) ;

    void fillQuestion(PatientQuestionDTO req) ;

    void submitQuestion(PatientQuestionDTO req) ;

    void reviseQuestion(PatientQuestionDTO req) ;

    Long followUpFillQuestion(PatientQuestionDTO req);

    void followUpSubmitQuestion(PatientQuestionDTO req);

    PatientQuestionVO followUpQuestionInfo(Long eventHistoryId, Long questionId);

    Boolean tempSave(PatientQuestionDTO req);

    void tempSaveQuestion(PatientQuestionDTO req);

}
