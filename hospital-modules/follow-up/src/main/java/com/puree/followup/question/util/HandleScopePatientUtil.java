package com.puree.followup.question.util;

import com.puree.followup.admin.followup.service.IFollowUpAsyncService;
import com.puree.followup.domain.followup.dto.PatientWithRuleDTO;
import com.puree.followup.domain.followup.vo.InvitePatientListVO;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.hospital.business.api.RemoteBusPatientService;
import com.puree.hospital.business.api.model.BusPatientDto;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.YesNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName: HandleScopePatientUtil
 * @Date 2024/6/21 17:57
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Component
@Slf4j
@RefreshScope
public class HandleScopePatientUtil {

    @Autowired
    private IFollowUpAsyncService followUpAsyncService ;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RemoteBusPatientService remoteBusPatientService;
    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper ;

    @Async("taskExecutor")
    public void asyncHandlePatientsByRules(PatientWithRuleDTO patientWithRuleDTO, Question question , String snowId, Integer cachePatientDuration) {

        List<Long> excludePatientIdList = patientQuestionRecordMapper.findPatientByFillStatus(question.getHospitalId(), question.getId(),
                YesNoEnum.NO.getCode(), Arrays.asList(FillQuestionStatusEnum.FILLING.getIndex(), FillQuestionStatusEnum.FINISHED.getIndex()),
                question.getFillTimes() );

        BusPatientDto patientDTO = new BusPatientDto();
        patientDTO.setHospitalId(question.getHospitalId());
        //排除搜索的患者id列表
        patientDTO.setExcludeUserIds(excludePatientIdList);

        InvitePatientListVO patientListVO = new InvitePatientListVO();

        followUpAsyncService.syncHandlePatientsByRules(patientWithRuleDTO, patientDTO, patientListVO);

        handleInterResult(patientListVO,  question , snowId, cachePatientDuration, excludePatientIdList) ;

    }

    /**
     * 结果集的处理：取交集
     *
     */
    private void handleInterResult(InvitePatientListVO patientListVO, Question question, String snowId, Integer cachePatientDuration, List<Long> excludePatientIdList){

        //合并结果集
        List<List<BusPatientFamilyVo>> listsOfPatients = Arrays.asList(patientListVO.getDiagnosisResult(),
                patientListVO.getPushDataResult(),
                patientListVO.getGoodsResult(),
                patientListVO.getDrugResult(),
                patientListVO.getServicePackageResult(),
                patientListVO.getHealthRecordResult(),
                patientListVO.getDoctorResult());

        // 排除null的列表（列表为null表示对应的条件未触发，列表为空则表示条件触发了，但筛选的结果为0个患者）
        List<List<BusPatientFamilyVo>> lists = listsOfPatients.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        //合并后的最终筛选结果（取交集）
        List<BusPatientFamilyVo> intersectionResult = getIntersection(lists);

        BusPatientDto patientDto = new BusPatientDto() ;
        patientDto.setHospitalId(question.getHospitalId());
        patientDto.setIsVisible(Boolean.TRUE);
        R<List<BusPatientFamilyVo>> r = remoteBusPatientService.listFamilyInfo(patientDto);
        List<BusPatientFamilyVo> dataList = r.getData();
        List<Long> allPatientIdList = dataList.stream().map(BusPatientFamilyVo::getId).distinct().collect(Collectors.toList());

        List<BusPatientFamilyVo> dataListV2 = new ArrayList<>() ;

        if ( null==excludePatientIdList || excludePatientIdList.isEmpty() ) {
            dataListV2.addAll(dataList) ;
        }else {

            for ( Long patientId : allPatientIdList ) {
                if (!excludePatientIdList.contains(patientId)){
                    BusPatientFamilyVo patientFamilyVo = dataList.stream().filter(e -> patientId.equals(e.getId())).findFirst().get();
                    dataListV2.add(patientFamilyVo) ;
                }
            }

        }

        if( null == intersectionResult ) {
            log.debug("question用户未设置任何入组规则，筛选该医院下所有患者");

            String key1 = Constants.QUESTION_FIND_PATIENT + question.getId() + ":" + snowId ;
            redisTemplate.opsForList().rightPushAll(key1, dataListV2);
            redisTemplate.expire(key1, cachePatientDuration, TimeUnit.SECONDS);

            String key2 = Constants.QUESTION_FIND_PATIENT_END + question.getId() + ":" + snowId ;
            redisTemplate.opsForValue().set(key2, YesNoEnum.YES.getCode(), cachePatientDuration, TimeUnit.SECONDS);

            return;
        }

        log.debug("question指定范围查询患者最终结果：{}", intersectionResult);

        List<BusPatientFamilyVo> finalResult = new ArrayList<>() ;

        for (BusPatientFamilyVo patientFamilyVo : intersectionResult) {

            for (BusPatientFamilyVo data :dataListV2) {

                if ( data.getId().equals(patientFamilyVo.getId())
                        && data.getPatientId().equals(patientFamilyVo.getPatientId())
                        && data.getHospitalId().equals(patientFamilyVo.getHospitalId()) ) {

                    finalResult.add(data) ;
                    break;
                }
            }

        }

        if ( !finalResult.isEmpty() ) {
            String key1 = Constants.QUESTION_FIND_PATIENT + question.getId() + ":" + snowId ;
            redisTemplate.opsForList().rightPushAll(key1, finalResult);
            redisTemplate.expire(key1, cachePatientDuration, TimeUnit.SECONDS);
        }

        String key2 = Constants.QUESTION_FIND_PATIENT_END + question.getId() + ":" + snowId ;
        redisTemplate.opsForValue().set(key2, YesNoEnum.YES.getCode(), cachePatientDuration, TimeUnit.SECONDS);

    }

    /**
     * 获取交集
     *
     * @param lists 数据列表
     * @return 取交集后的结果
     */
    private List<BusPatientFamilyVo> getIntersection(List<List<BusPatientFamilyVo>> lists) {
        if(CollectionUtils.isEmpty(lists))
            return null;
        //创建一个Set来存储第一个List中所有就诊人的id
        Set<Long> ids = lists.get(0).stream()
                .map(BusPatientFamilyVo::getId)
                .collect(Collectors.toSet());

        List<BusPatientFamilyVo> result = lists.get(0);
        for(int i = 1 ; i < lists.size() ; i++){
            List<BusPatientFamilyVo> list = lists.get(i);
            //找出其他列表中那些id也存在于ids中的元素
            List<BusPatientFamilyVo> intersection = new ArrayList<>();
            for (BusPatientFamilyVo patientFamilyVo : list)
                if(ids.contains(patientFamilyVo.getId())){
                    intersection.add(patientFamilyVo);
                }
            result= intersection;
            ids = result.stream()
                    .map(BusPatientFamilyVo::getId)
                    .collect(Collectors.toSet());
        }
        return result;
    }

    /**
     * 获取医院下的所有患者
     *
     * @param query 查询条件
     * @return 患者列表
     */
    private List<BusPatientFamilyVo> getAllPatients(BusPatientDto query) {
        List<BusPatientFamilyVo> result = remoteBusPatientService.userPage(query, null, null).getData();
        if(result == null) {
            return new ArrayList<>();
        }
        return result;
    }


}
