package com.puree.followup.domain.medical.constant;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName RecordJsonTypeEnum
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/13 18:04
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum RecordJsonTypeEnum {

    INSPECT_SUMMARY(1, "INSPECT_SUMMARY", "INSPECT_SUMMARY", "检验小结"),
    INSPECT_INDICATORS(2, "INSPECT_INDICATORS", "INSPECT_INDICATORS", "检验指标"),
    EXAM_SUMMARY(3, "EXAM_SUMMARY", "EXAM_SUMMARY", "检查小结"),
    EXAM_INDICATORS(4, "EXAM_INDICATORS", "EXAM_INDICATORS", "检查指标"),
    ;

    /**
     * @Param code
     * @Return com.puree.followup.domain.medical.constant.RegularRecordEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/5/13 15:39
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RecordJsonTypeEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        RecordJsonTypeEnum recordJsonTypeEnum = Arrays.stream(RecordJsonTypeEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
        return recordJsonTypeEnum;
    }

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;
}
