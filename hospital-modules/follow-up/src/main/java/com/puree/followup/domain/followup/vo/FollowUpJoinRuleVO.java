package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.followup.model.joinrule.JoinRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 随访-入组方式 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpJoinRuleVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 入组方式标识：one:处方诊断 two:数据推送 three:商品 four:服务包 five:健康档案 drugs:药品
     */
    private String joinType;

    /**
     * 入组开关：close:关闭 enable:开启(任一) all:满足全部
     */
    private String joinSwitch;

    /**
     * 入组规则
     */
    private JoinRule rule;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}