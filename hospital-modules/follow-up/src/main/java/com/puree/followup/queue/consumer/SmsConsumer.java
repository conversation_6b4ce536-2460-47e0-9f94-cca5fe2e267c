package com.puree.followup.queue.consumer;

import com.alibaba.fastjson.JSONObject;
import com.puree.followup.queue.producer.SmsProducer;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.TemplateMsgConstants;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.common.sms.SMSProperties;
import com.puree.hospital.im.api.model.FollowUpImWxDTO;
import com.puree.hospital.tool.api.RemoteSmsNotificationService;
import com.puree.hospital.tool.api.model.dto.SmsNotificationDTO;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: SmsConsumer
 * @Date 2024/5/28 11:45
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public class SmsConsumer extends RedisStreamConsumer<FollowUpImWxDTO> {

    @Autowired
    private RedisService redisService ;
    @Autowired
    private SMSProperties smsProperties ;
    @Autowired
    private SmsProducer smsProducer ;
    @Resource
    private RemoteSmsNotificationService remoteSmsNotificationService;

    @Override
    public void onMessage(RedisMessage<FollowUpImWxDTO> message) {
        FollowUpImWxDTO followUpImWxDTO = message.getBody();

        String redisKey = "" ;
        switch (followUpImWxDTO.getType()) {

            case TemplateMsgConstants.FOLLOW_UP_TEXT_TYPE :

            case TemplateMsgConstants.FOLLOW_UP_IMAGE_TYPE :
                redisKey = Constants.SMS_FOLLOWUP_TEXT_IMAGE + smsProperties.getFollowUpEventMsgCode() + ":" + followUpImWxDTO.getPhoneNum() ;
                break;

            case TemplateMsgConstants.FOLLOW_UP_FORWARD_TUTORIAL_TYPE :
                redisKey = Constants.SMS_FOLLOWUP_TUTORIAL + smsProperties.getFollowUpEventTutorialCode() + ":" + followUpImWxDTO.getPhoneNum() ;
                break;

            case TemplateMsgConstants.FOLLOW_UP_SUMMARY_REMIND_TYPE :
                redisKey = Constants.SMS_FOLLOWUP_UNIFY + smsProperties.getFollowUpSummary() + ":" + followUpImWxDTO.getPhoneNum() ;
                break;

            case TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE :
                redisKey = Constants.SMS_QUESTION + smsProperties.getQuestionCode() + ":" + followUpImWxDTO.getPhoneNum() ;
                break;


            default: return ;

        }

        this.sendShortMsg(redisKey, followUpImWxDTO) ;

    }

    private void sendShortMsg(String redisKey, FollowUpImWxDTO followUpImWxDTO) {

        boolean isLocked = redisService.setIfAbsent(redisKey, smsProperties.getDelay(), TimeUnit.SECONDS);
        if (isLocked) {
            sendFollowUpEventSms(followUpImWxDTO.getPhoneNum(), followUpImWxDTO.getType(), followUpImWxDTO.getWxName(), followUpImWxDTO.getHospitalId());
            return;
        }

        smsProducer.delaySend(followUpImWxDTO, (smsProperties.getDelay() * 1000) + 1);

    }

    public void sendFollowUpEventSms(String phoneNum, String type, String wxName, Long hospitalId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("hospital", "");
        jsonObject.put("name", "微信公众号" + wxName);

        // 组装参数
        SmsNotificationDTO baseSmsNotificationDTO = new SmsNotificationDTO();
        baseSmsNotificationDTO.setPhoneNumber(phoneNum);
        baseSmsNotificationDTO.setHospitalId(hospitalId);
        baseSmsNotificationDTO.setChannel(SmsNotificationDTO.ChannelType.ALIYUN);
        baseSmsNotificationDTO.setMessageContent(jsonObject.toJSONString());

        switch (type) {
            case TemplateMsgConstants.FOLLOW_UP_TEXT_TYPE:
            case TemplateMsgConstants.FOLLOW_UP_IMAGE_TYPE:
                baseSmsNotificationDTO.setTemplateType("FOLLOW_UP_EVENT");
                break;
            case TemplateMsgConstants.FOLLOW_UP_FORWARD_TUTORIAL_TYPE:
                baseSmsNotificationDTO.setTemplateType("FOLLOW_UP_EVENT_TUTORIAL");
                break;
            case TemplateMsgConstants.FOLLOW_UP_SUMMARY_REMIND_TYPE:
                baseSmsNotificationDTO.setTemplateType("FOLLOW_UP_SUMMARY");
                break;
            case TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE:
                baseSmsNotificationDTO.setTemplateType("QUESTION");
                break;
        }
        remoteSmsNotificationService.templateMessage(baseSmsNotificationDTO);
    }

}
