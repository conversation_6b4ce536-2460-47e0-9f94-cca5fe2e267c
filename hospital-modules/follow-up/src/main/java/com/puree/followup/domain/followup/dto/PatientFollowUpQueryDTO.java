package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 患者端随访表查询 DTO
 * <AUTHOR>
 * @date 2024-05-15 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientFollowUpQueryDTO {

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 患者id
     */
    private Long userId;

}