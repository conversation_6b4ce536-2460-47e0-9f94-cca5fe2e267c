package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/6/17 17:55
 * @Description  处方诊断类型：MM.西医  TCM.中医
 */
public enum JoinRuleDiagnosisTypeEnum {

    MM(1, "MM", "西医"),
    TCM(2, "TCM", "中医");


    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    JoinRuleDiagnosisTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static JoinRuleDiagnosisTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        JoinRuleDiagnosisTypeEnum[] metaArr = JoinRuleDiagnosisTypeEnum.values();
        for (JoinRuleDiagnosisTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static JoinRuleDiagnosisTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        JoinRuleDiagnosisTypeEnum[] metaArr = JoinRuleDiagnosisTypeEnum.values();
        for (JoinRuleDiagnosisTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
