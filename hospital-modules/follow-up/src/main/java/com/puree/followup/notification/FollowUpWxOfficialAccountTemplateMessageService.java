package com.puree.followup.notification;

import com.puree.followup.domain.followup.dto.FollowUpEventBusinessDTO;
import com.puree.followup.helper.FollowUpTemplateMessageHelper;
import com.puree.hospital.common.notification.assembler.INotificationAssembler;
import com.puree.hospital.common.notification.service.impl.AbstractWxOfficialAccountTemplateMessageService;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/21 10:10
 */
@Service("followUpWxOfficialAccountTemplateMessageService")
public class FollowUpWxOfficialAccountTemplateMessageService<E extends BaseFollowUpEvent> extends AbstractWxOfficialAccountTemplateMessageService<E, FollowUpEventBusinessDTO> {

    @Resource
    private FollowUpTemplateMessageHelper followUpTemplateMessageHelper;

    @Override
    protected FollowUpEventBusinessDTO getBusinessData(E event) {
        return followUpTemplateMessageHelper.getBusinessData(event);
    }

    @Override
    protected Long getHospitalId(E event, FollowUpEventBusinessDTO businessData) {
        return businessData.getHospitalId();
    }

    @Override
    protected Long getPatientId(E event, FollowUpEventBusinessDTO businessData) {
        return businessData.getUserId();
    }

    @Override
    protected String getPartnerCode(E event, FollowUpEventBusinessDTO businessData) {
        // 随访问卷这块目前没有关联机构Code，只推送医院的模板
        return null;
    }

    @Override
    protected INotificationAssembler getAssembler(E event, FollowUpEventBusinessDTO businessData) {
        return followUpTemplateMessageHelper.getAssembler(event, businessData);
    }
}
