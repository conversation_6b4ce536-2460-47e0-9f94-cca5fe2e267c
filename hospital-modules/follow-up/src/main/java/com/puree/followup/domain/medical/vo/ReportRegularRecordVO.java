package com.puree.followup.domain.medical.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ReportRegularRecordVo
 * <AUTHOR>
 * @Description vo
 * @Date 2024/5/23 19:00
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ReportRegularRecordVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<ReportRegularRecordChildrenVO> children;
}
