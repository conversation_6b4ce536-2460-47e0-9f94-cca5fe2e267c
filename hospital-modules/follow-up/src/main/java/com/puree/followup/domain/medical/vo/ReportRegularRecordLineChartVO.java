package com.puree.followup.domain.medical.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.followup.domain.medical.constant.RegularRecordEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName ReportRegularRecordLineChartVO
 * <AUTHOR>
 * @Description 折线图 VO
 * @Date 2024/5/30 16:02
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ReportRegularRecordLineChartVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 项目类型;TALL为 身高,WEIGHT为 体重,BMI为 体重指数,SYSTOLIC为 收缩压,DIASTOLIC为 舒张压,PULSE_RATE为 脉搏,BLOOD_SUGAR为 血糖,BODY_TEMPERATURE为 体温,TOTAL_CHOLESTEROL为 总胆固醇,TRIGLYCERIDES为 甘油三脂,HDL为 高密度脂蛋白,LDL为 低密度脂蛋白,URIC_ACID为 尿酸,BLOOD_OXYGEN为 血氧，HEART_RATE 心率
     */
    private RegularRecordEnum itemType;
    /**
     * 项目值
     */
    private String itemValue;
    /**
     * 项目值单位
     */
    private String itemUnit;
    /**
     * 类型;DATA_PUSH为数据推送，PATIENT_ADDED为患者添加
     */
    private RecordSourceEnum source;
    /**
     * 体检数据记录ID
     */
    private Long recordId;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 患者身份证号
     */
    private String patientIdNumber;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;
}
