package com.puree.followup.question.mapper;

import com.puree.followup.question.domain.dto.PatientQuestionDTO;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import org.apache.ibatis.annotations.Param;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【patient_question_record(患者填写问卷记录表)】的数据库操作Mapper
* @createDate 2024-06-12 17:52:46
* @Entity com.puree.followup.question.domain.PatientQuestionRecord
*/
public interface PatientQuestionRecordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(PatientQuestionRecord record) throws SQLIntegrityConstraintViolationException;

    int insertSelective(PatientQuestionRecord record);

    PatientQuestionRecord selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PatientQuestionRecord record);

    int updateByPrimaryKey(PatientQuestionRecord record);

    void updateFillStatusByFillStatus(PatientQuestionDTO req) ;

    int countQuestionByFillStatus(PatientQuestionDTO req) ;

    int countPatientByFillStatus(PatientQuestionDTO req) ;

    List<Long> findPatientByFillStatus(@Param("hospitalId") Long hospitalId , @Param("questionId") Long questionId,
                                       @Param("isDelete") Integer isDelete, @Param("fillStatusList") List<Integer> fillStatusList ,
                                       @Param("fillTimes") Integer fillTimes) ;

    List<PatientQuestionRecord> patientFindQuestionList(@Param("userId") Long userId , @Param("questionStatusList") List<Integer> questionStatusList) ;

    int countOnePatientFillOneQuestionTimes(@Param("hospitalId") Long hospitalId , @Param("questionId") Long questionId, @Param("patientId") Long patientId ,
                                            @Param("isDelete") Integer isDelete, @Param("fillStatusList") List<Integer> fillStatusList) ;

    List<PatientQuestionRecord> selectByCondition(PatientQuestionRecord req) ;

    List<PatientQuestionRecord> doctorFindPatientQuestion(@Param("hospitalId") Long hospitalId , @Param("questionId") Long questionId, @Param("doctorId") Long doctorId ,
                                                          @Param("fillStatus") Integer fillStatus, @Param("isDelete") Integer isDelete,
                                                          @Param("submitTimeStart") Date submitTimeStart, @Param("submitTimeEnd") Date submitTimeEnd,
                                                          @Param("patientIdList") List<Long> patientIdList ) ;

    int batchUpdateFillStatus() ;

    PatientQuestionRecord selectOne(PatientQuestionRecord query);

    int updateFillStatusByPatientEventId(PatientQuestionRecord update);
}
