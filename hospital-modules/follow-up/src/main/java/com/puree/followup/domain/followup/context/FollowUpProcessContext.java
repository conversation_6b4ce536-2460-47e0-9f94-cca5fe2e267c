package com.puree.followup.domain.followup.context;

import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.FollowUpItem;
import com.puree.followup.domain.followup.vo.FollowUpItemVO;
import com.puree.followup.question.domain.model.Question;
import com.puree.hospital.business.api.model.BusBizDepartment;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import com.puree.hospital.shop.api.model.BusShopGoods;
import com.puree.hospital.tool.api.model.Article;
import lombok.Data;
import java.util.HashMap;
import java.util.Map;

/**
 * 随访数据处理上下文
 *
 * <AUTHOR>
 * @date 2025/1/3 11:29
 */
@Data
public class FollowUpProcessContext {
    public static final String ARTICLE = "article";
    public static final String FOLLOWUP = "followup";
    public static final String FOLLOWUP_ITEM = "followup_item";
    public static final String QUESTION = "question";
    public static final String DEPARTMENT = "department";
    public static final String GOODS = "goods";
    public static final String SERVICE_PACK = "service_pack";

    /**
     * 患教信息
     */
    private Map<String, Article> articleMap = new HashMap<>();

    /**
     * 随访信息
     */
    private Map<String, FollowUp> followUpMap = new HashMap<>();

    /**
     * 随访分项信息
     */
    private Map<String, FollowUpItem> followUpItemMap = new HashMap<>();

    /**
     * 问卷信息
     */
    private Map<String, Question> questionMap = new HashMap<>();

    /**
     * 科室信息
     */
    private Map<String, BusBizDepartment> departmentMap = new HashMap<>();

    /**
     * 商品信息
     */
    private Map<String, BusShopGoods> goodsMap = new HashMap<>();

    /**
     * 服务包信息
     */
    private Map<String, BusFiveServicePackVO> servicePackMap = new HashMap<>();


    /**
     * 随访详情
     */
    private FollowUp followUp;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 随访分项信息
     */
    private FollowUpItemVO followUpItemVO;

}
