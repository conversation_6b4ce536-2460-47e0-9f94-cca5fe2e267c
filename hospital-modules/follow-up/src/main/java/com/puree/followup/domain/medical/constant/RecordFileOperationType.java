package com.puree.followup.domain.medical.constant;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName RecordFileOperationType
 * <AUTHOR>
 * @Description 体检报告文件上传操作类型
 * @Date 2024/6/3 12:22
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum RecordFileOperationType {

    DOWNLOAD_DOCUMENT(1, "DOWNLOAD_DOCUMENT", "DOWNLOAD_DOCUMENT", "文件下载"),
    DATA_DISPLAY(2, "DATA_DISPLAY", "DATA_DISPLAY", "数据展示"),
    ;


    /**
     * @Param null
     * @Return
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/6/3 12:22
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RecordFileOperationType getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        RecordFileOperationType recordJsonTypeEnum = Arrays.stream(RecordFileOperationType.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
        return recordJsonTypeEnum;
    }

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;
}
