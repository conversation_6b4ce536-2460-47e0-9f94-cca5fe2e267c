package com.puree.followup.domain.followup.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.SmartExecutorConditionTypeHandler;
import com.puree.followup.config.TaskEventTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * 随访-分项-智能执行配置
 * <AUTHOR>
 * @date 2024-07-15 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItemExecutor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 智能执行ID
     */
    private Long id;

    /**
     * 智能执行排序
     */
    private Integer sortNum;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 医院ID（运营平台为0）
     */
    private Long hospitalId;

    /**
     * 智能执行开关：0.关闭 1.满足任一 2.满足全部
     */
    private Integer executorSwitch;

    /**
     * 数据源的类型：1.问卷数据 2.健康档案
     */
    private Integer dataSourceType;

    /**
     * 问卷id
     */
    private Long questionnaireId;

    /**
     * 问卷名称
     */
    private String questionnaireName;

    /**
     * 健康档案的时效性：x天内的患者最新指标
     */
    private Integer validDays;

    /**
     * 预警启用标识：0.禁用 1.启用
     */
    private Integer warnSwitch;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

    /**
     * 执行事件列表
     */
    @TableField(typeHandler = TaskEventTypeHandler.class)
    private List<TaskEvent> events;

    /**
     * 条件列表
     */
    @TableField(typeHandler = SmartExecutorConditionTypeHandler.class)
    private List<SmartExecutorCondition> conditions;

    /**
     * 患者的随访记录id
     */
    private Long followUpRecordId;

    /**
     * 就诊人id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;
}