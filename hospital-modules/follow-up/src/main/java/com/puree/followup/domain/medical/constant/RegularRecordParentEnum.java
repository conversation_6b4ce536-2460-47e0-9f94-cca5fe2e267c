package com.puree.followup.domain.medical.constant;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName RegularRecordParentEnum
 * <AUTHOR>
 * @Description 常规记录父类枚举
 * @Date 2024/5/23 17:37
 * @Version 1.0
 */
@SuppressWarnings("")
@Getter
@AllArgsConstructor
public enum RegularRecordParentEnum {

    BM(1001, "BM", "BM", "身体质量指数"),
    BP(1002, "BP", "BP", "血压"),
    BS(1003, "BS", "BS","血糖"),
    BT(1004, "BT", "BT", "体温"),
    BL(1005, "BL", "BL", "血脂"),
    UA(1006, "UA", "UA","尿酸"),
    BO(1007, "BO", "BO","血氧"),
    BTYPE(1008, "BTYPE", "BTYPE","血型"),
    ;


    /**
     * @Param code
     * @Return com.puree.followup.domain.medical.constant.RegularRecordEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/5/13 15:39
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RegularRecordParentEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(RegularRecordParentEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
    }

    /**
     * 编号
     */
    private final Integer number;

    /**
     * 代码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;
    /**
     * 描述
     */
    private final String desc;
}
