package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 智能执行：患者信息VO
 */
@Data
public class SmartExecutorPatientVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /** 姓名 */
    private String name;
    /** 性别（0女 1男） */
    private String sex;
    /** 身份证号码 */
    private String idNumber;
    /** 患者关系（字典表获取） */
    private Long patientRelationship;
    /** 是否默认 */
    private Integer status;
    /** 民族 */
    private Long nation;
    /** 婚姻状况（0 未婚 1 已婚） */
    private Integer maritalStatus;
    /** 工作单位 */
    private String workUnit;
    /** 住址所在地区 */
    private String location;
    /** 详细地址 */
    private String detailAddress;
    /** 患者ID */
    private Long patientId;
    /** 诊疗档案 */
    private String diagnosisArchives;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastConsultationTime;
    /**
     * 添加来源 0普通添加 1扫码添加
     */
    private String source;
    /**
     * 医生ID
     */
    private Long doctorId;
    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 渠道名称
     */
    private String channelName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastTime;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 血型
     */
    private String bloodType;
    /**
     * 年龄
     */
    private String familyAge;
    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registrationTime;
    /**
     * 渠道名称
     */
    private String fullName;

    /** 登录账号的手机号 */
    private String phoneNumber ;

    /** 智能执行-患者报告：首项命中的值*/
    private String firstValue;

    /** 智能执行-患者报告：次项命中的值 */
    private String secondValue;
}
