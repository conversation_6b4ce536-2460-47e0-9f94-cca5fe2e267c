package com.puree.followup.domain.followup.model;

import com.puree.hospital.followup.api.model.AppletQrCodeVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientJoinInResult extends AppletQrCodeVO {

    public PatientJoinInResult() {

    }

    public PatientJoinInResult(Boolean isSuccess, String message) {
        this.isSuccess = isSuccess;
        this.message = message;
    }

    /**
     * 加入随访是否成功
     */
    private Boolean isSuccess = true;

    /**
     * 提示信息
     */
    private String message = "";

    /**
     * 成功加入的就诊人id列表
     */
    private List<Long> ids = new ArrayList<>();

}