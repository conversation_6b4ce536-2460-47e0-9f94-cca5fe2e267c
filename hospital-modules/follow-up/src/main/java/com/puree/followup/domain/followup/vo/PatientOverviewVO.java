package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 患者概览 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientOverviewVO {

    /**
     * 患者随访记录ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 随访ID
     */
    private String followUpName;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者手机号
     */
    private String patientPhone;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者性别
     */
    private String patientSex;

    /**
     * 关联科室名称
     */
    private String departmentName;

    /**
     * 关联医生名称
     */
    private String doctorName;

    /**
     * 入组时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    /**
     * 终止/结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 入组类型：autoJoinIn:自动添加 autoInvite:自动邀请 manualAdd:手动添加 manualInvite:手动邀请
     */
    private String joinType;

    /**
     * 随访状态：inviting:邀请中 followuping:随访中 finished:已完成 terminated:提前终止 refused:已拒绝
     */
    private String joinStatus;

    /**
     * 入组方式
     */
    private String joinReason;


    /**
     * 任务总数
     */
    private Integer totalTasks;

    /**
     * 即将开始任务数
     */
    private Integer iscomingTasks;

    /**
     * 待完成任务数
     */
    private Integer ongoingTasks;

    /**
     * 已完成任务数
     */
    private Integer finishedTasks;

    /**
     * 已过期任务数
     */
    private Integer hasExpiredTasks;

    /**
     * 已终止任务数
     */
    private Integer terminatedTasks;

    /**
     * 随访进度(百分比)
     */
    private BigDecimal finishedFollowUpRate;

    /**
     * 任务完成率(百分比)
     */
    private BigDecimal finishedTaskRate;

    /**
     * 分项列表
     */
    private List<PatientItemOverviewVO> items;

    /**
     * 随访周期类型(到期时间)：longTerm:长期、joinIn:加入随访X天后结束
     */
    private String expireType;




}