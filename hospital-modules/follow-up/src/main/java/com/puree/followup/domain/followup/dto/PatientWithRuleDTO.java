package com.puree.followup.domain.followup.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.followup.model.joinrule.JoinRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 手动添加患者-指定范围场景 DTO
 * <AUTHOR>
 * @date 2024-05-20 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientWithRuleDTO {

    /**
     * 随访ID 或者 问卷Id
     */
    private Long id;

    /**
     * 添加方式  auto:手动添加  patientAgree:手动邀请
     */
    private String joinType;

    /**
     * 规则列表
     */
    private List<FollowUpJoinRuleDTO> joinRuleList;

    /**
     * 医生id列表
     */
    private List<Long> doctorIds;


}