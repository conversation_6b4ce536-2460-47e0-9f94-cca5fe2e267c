package com.puree.followup.domain.drools.constant;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName ComparisonOperatorEnum
 * <AUTHOR>
 * @Description 比较运算符
 * @Date 2024/4/10 18:40
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum ComparisonOperatorEnum {
    /**
     * MORE、LESS、EQUAL、MORE_OR_EQUAL、LESS_OR_EQUAL、NOT_EQUAL、INTERREGIONAL 为数值类型，检验类
     * IN、NOT_IN、IS_NULL、NOT_NULL 为字符类型，检查类
     * gt:大于号  lt：小于号  eq：等于号  ge：大于等于号  le：小于等于号  ne：不等于号   contains：包含   not_in：不包含    is_null：为空   not_null：不为空  interregional：区间内
     */
    GT(1,"gt",  " {} > {}","大于号", " > ", "大于"),
    LT(2, "lt", " {} < {}","小于号", " < ", "小于"),
    EQ(3, "eq", " {} == {}","等于号", " = ", "等于"),
    GE(4, "ge", " {} >= {}","大于等于号", " >= ", "大于等于"),
    LE(5, "le", " {} <= {}","小于等于号", " <= ", "小于等于"),
    NE(6, "ne", " {} != {}","不等于号", " != ", "不等于"),
    CONTAINS(7, "contains", "{} contains (\"{}\")","包含", " like '%%%s%%' ", "包含"),
    NOT_IN(8, "not_in", "{} not in (\"{}\")","不包含", " not like '%%%s%%' ", "不包含"),
    IS_NULL(9, "is_null", "{} == null","为空", " is null ", "为空"),
    NOT_NULL(10, "not_null", "{} != null","不为空", " is not null ", "不为空"),
    INTERREGIONAL(11, "interregional", "{} < {} && {} < {}","区间内", " between %s and %s ", "区间"),

    ;


    /**
     * @Param code
     * @Return com.puree.followup.enums.GenderEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/3/31 16:15
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static ComparisonOperatorEnum getEnumByCode(String code) {
        if (code == null){
            return null;
        }
        ComparisonOperatorEnum comparisonOperatorEnum = Arrays.stream(ComparisonOperatorEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code) )
                .findAny()
                .orElse(null);
        return comparisonOperatorEnum;
    }

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;

    /**
     * sql运算符
     */
    private String sqlOperator;

    /**
     * 智能执行触发预警时的：判断范围(设置的条件)
     */
    private String conditionDesc;
}
