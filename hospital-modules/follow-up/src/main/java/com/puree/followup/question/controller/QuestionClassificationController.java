package com.puree.followup.question.controller;

import com.puree.followup.admin.classification.service.IClassificationService;
import com.puree.followup.domain.classification.dto.ClassificationDTO;
import com.puree.followup.domain.classification.vo.ClassificationVO;
import com.puree.followup.question.domain.dto.DuplicateClassificationDTO;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import com.puree.hospital.common.security.annotation.PreAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName: QuestionClassificationController
 * @Date 2024/6/7 10:34
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Slf4j
@RestController
@RequestMapping("/question/classification")
public class QuestionClassificationController extends BaseController {

    @Autowired
    private IClassificationService classificationService;

    /**
     * 医院后台 - 问卷分类列表
     *
     * @param dto 查询条件
     * @return 分类列表
     */
    @PreAuthorize(hasPermi = "question:classification:list")
    @GetMapping("/admin/list")
    @Log(title = "医院后台-分页获取问卷分类列表", businessType = BusinessType.QUERY)
    public Paging<List<ClassificationVO>> listQuestionClassification(ClassificationDTO dto) {
        return PageUtil.buildPage(classificationService.listQuestionClassification(dto));
    }

    /**
     * 医院后台 根据分类id查询分类详情
     *
     * @param id  分类id
     * @return 分类详情
     */
    @GetMapping("/admin/{id}")
    @Log(title = "医院后台-问卷分类详情", businessType = BusinessType.QUERY)
    public AjaxResult<ClassificationVO> getDetailInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(classificationService.getInfo(id));
    }

    /**
     * 医院后台 添加分类
     *
     * @param dto 分类详情
     * @return
     */
    @PreAuthorize(hasPermi = "question:classification:add")
    @Log(title = "医院后台-添加问卷分类", businessType = BusinessType.INSERT)
    @PostMapping("/admin/add")
    public AjaxResult<Boolean> addQuestionClassification(@Valid @RequestBody ClassificationDTO dto) {
        return AjaxResult.success(classificationService.addQuestionClassification(dto));
    }

    /**
     * 医院后台 修改问卷分类
     *
     * @param dto 分类详情
     * @return
     */
    @PreAuthorize(hasPermi = "question:classification:edit")
    @Log(title = "医院后台-修改问卷分类", businessType = BusinessType.UPDATE)
    @PutMapping("/admin/edit")
    public AjaxResult<Boolean> editQuestionClassification(@Valid @RequestBody ClassificationDTO dto) {
        return AjaxResult.success(classificationService.editQuestionClassification(dto));
    }

    /**
     * 医院后台 根据分类id删除分类
     *
     * @param id 分类id
     * @return
     */
    @PreAuthorize(hasPermi = "question:classification:delete")
    @Log(title = "医院后台-删除问卷分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/admin/delete/{id}")
    public AjaxResult<Boolean> deleteQuestionClassification(@PathVariable("id") Long id, Integer revision) {
        return AjaxResult.success(classificationService.deleteQuestionClassification(id, revision));
    }

    /**
     * 医院后台 校验问卷分类重复
     *
     */
    @Log(title = "医院后台-校验问卷分类重复", businessType = BusinessType.QUERY)
    @GetMapping("/admin/check-duplicate")
    public AjaxResult<Boolean> checkDuplicateClassification(DuplicateClassificationDTO req) {
        return AjaxResult.success(classificationService.checkDuplicateClassification(req));
    }

}
