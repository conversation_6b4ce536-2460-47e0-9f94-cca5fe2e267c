package com.puree.followup.admin.followup.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.puree.followup.admin.followup.handler.FollowUpProcessContestHandler;
import com.puree.followup.admin.followup.mapper.FollowUpItemExecutorMapper;
import com.puree.followup.admin.followup.mapper.FollowUpItemTaskMapper;
import com.puree.followup.domain.followup.context.FollowUpProcessContext;
import com.puree.followup.domain.followup.model.FollowUpItemExecutor;
import com.puree.followup.domain.followup.model.FollowUpItemTask;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.domain.followup.vo.FollowUpItemTaskVO;
import com.puree.followup.domain.followup.vo.FollowUpItemVO;
import com.puree.followup.domain.followup.vo.FollowUpSmartExecutorVO;
import com.puree.followup.enums.DayOfWeekEnum;
import com.puree.followup.enums.FollowUpJoinSwitchEnum;
import com.puree.followup.enums.RecommendSourceEnum;
import com.puree.followup.enums.SmartExecutorDataSourceTypeEnum;
import com.puree.followup.enums.TaskEventTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 随访分项任务信息组装
 * <AUTHOR>
 * @date 2025/1/4 14:54
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FollowUpItemTaskAssembler implements FollowUpDataAssembler<FollowUpItemVO> {

    private final FollowUpItemTaskMapper followUpItemTaskMapper;
    private final FollowUpItemExecutorMapper followUpItemExecutorMapper;
    private final FollowUpProcessContestHandler followUpProcessContestHandler;

    @Override
    public FollowUpItemVO assemble(FollowUpProcessContext context) {
        //分项任务VO处理
        FollowUpItemVO itemVO = context.getFollowUpItemVO();
        FollowUpItemTask itemTaskQuery = new FollowUpItemTask();
        itemTaskQuery.setItemId(itemVO.getId());
        itemTaskQuery.setIsDelete(YesNoEnum.NO.getCode());
        List<FollowUpItemTask> itemTaskList = followUpItemTaskMapper.getList(itemTaskQuery);
        List<FollowUpItemTaskVO> itemTasks = new ArrayList<>();
        itemTaskList.forEach(task -> itemTasks.add(handleItemTaskVO(task, context)));
        itemVO.setTasks(itemTasks);
        //智能执行VO处理
        FollowUpItemExecutor itemExecutorQuery = new FollowUpItemExecutor();
        itemExecutorQuery.setItemId(itemVO.getId());
        itemExecutorQuery.setIsDelete(YesNoEnum.NO.getCode());
        List<FollowUpItemExecutor> itemExecutorList = followUpItemExecutorMapper.getList(itemExecutorQuery);
        List<FollowUpSmartExecutorVO> itemExecutors = new ArrayList<>();
        itemExecutorList.forEach(executor -> itemExecutors.add(handleItemExecutorVO(executor, context)));
        itemVO.setSmartExecutors(itemExecutors);
        return itemVO;
    }

    /**
     * FollowUpItemTask的VO处理
     * @param task  随访分项任务
     * @return VO
     */
    private FollowUpItemTaskVO handleItemTaskVO(FollowUpItemTask task, FollowUpProcessContext context) {
        FollowUpItemTaskVO vo = BeanUtil.copyProperties(task, FollowUpItemTaskVO.class);
        vo.setWeekDays(DayOfWeekEnum.transferWeekInfoByIndex(vo.getWeekDays())) ;
        if(task.getExecuteTime() != null && task.getExecuteTime().length() > 5){
            vo.setExecuteTime(task.getExecuteTime().substring(0, 5));
        }
        handleEvents(vo.getEvents(), context);
        return vo;
    }

    /**
     * FollowUpSmartExecutor的VO处理
     * @param executor  随访分项智能执行
     * @return FollowUpSmartExecutorVO
     */
    private FollowUpSmartExecutorVO handleItemExecutorVO(FollowUpItemExecutor executor, FollowUpProcessContext context) {
        FollowUpSmartExecutorVO vo = BeanUtil.copyProperties(executor, FollowUpSmartExecutorVO.class);
        vo.setExecutorSwitch(FollowUpJoinSwitchEnum.getByIndex(executor.getExecutorSwitch()).getName());
        vo.setDataSourceType(SmartExecutorDataSourceTypeEnum.getByIndex(executor.getDataSourceType()).getName());
        vo.setWarnSwitch(executor.getWarnSwitch().equals(YesNoEnum.YES.getCode()));
        handleEvents(vo.getEvents(), context);
        return vo;
    }
    
    private void handleEvents(List<TaskEvent> events, FollowUpProcessContext context){
        for (TaskEvent event : events) {
            if(TaskEventTypeEnum.RECOMMEND.getIndex().equals(event.getEventType()) && StringUtils.isNotEmpty(event.getRecommendId())){
                RecommendSourceEnum sourceEnum = RecommendSourceEnum.getRecommendSourceEnumByIndex(event.getRecommendSourceId());
                if(sourceEnum == null){
                    log.error("推荐类型错误：{}", event);
                    continue;
                }
                switch(sourceEnum) {
                    case DEPARTMENT:
                        event.setRecommendName(followUpProcessContestHandler.getDepartmentsStr(event.getRecommendId(), context));
                        break;
                    case GOODS:
                        event.setRecommendName(followUpProcessContestHandler.getGooods(Long.valueOf(event.getRecommendId()), context).getName());
                        break;
                    case SERVICE_PACK:
                        event.setRecommendName(followUpProcessContestHandler.getServicePack(Long.valueOf(event.getRecommendId()), context).getServicePackName());
                        break;
                    default:
                        break;
                }
            }
            //发送患教
            if(TaskEventTypeEnum.TUTORIAL.getIndex().equals(event.getEventType())){
                event.setSendName(followUpProcessContestHandler.getArticle(event.getSendId(), context).getTitle());
            }
        }
    }

}
