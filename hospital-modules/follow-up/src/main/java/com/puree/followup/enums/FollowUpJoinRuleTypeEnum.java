package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  入组方式标识：diagnosis.处方诊断 pushData.数据推送 goods.商品 drug.药品 servicePackage.服务包 healthRecord.健康档案 doctor.所属医生
 */
public enum FollowUpJoinRuleTypeEnum {

    DIAGNOSIS(1, "diagnosis", "处方诊断"),
    PUSH_DATA(2, "pushData", "数据推送"),
    GOODS(3, "goods", "商品"),
    DRUGS(4, "drug", "药品"),
    SERVICE_PACKAGE(5, "servicePackage", "服务包"),
    HEALTH_RECORD(6, "healthRecord", "健康档案"),
    DOCTOR(7, "doctor", "所属医生");


    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpJoinRuleTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpJoinRuleTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpJoinRuleTypeEnum[] metaArr = FollowUpJoinRuleTypeEnum.values();
        for (FollowUpJoinRuleTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpJoinRuleTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpJoinRuleTypeEnum[] metaArr = FollowUpJoinRuleTypeEnum.values();
        for (FollowUpJoinRuleTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
