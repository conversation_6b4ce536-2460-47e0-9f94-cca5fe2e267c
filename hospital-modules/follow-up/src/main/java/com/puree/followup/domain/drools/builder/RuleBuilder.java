package com.puree.followup.domain.drools.builder;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName RuleBuild
 * <AUTHOR>
 * @Description 规则构建
 * @Date 2024/4/2 15:48
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class RuleBuilder {
    /**
     * 规则开始
     */
    private final static String RULE_START = "rule";
    /**
     * 规则结束
     */
    private final static String RULE_END = "end";
    /**
     * 规则 when 开始
     */
    private final static String RULE_WHEN = "when";
    /**
     * 规则 then 开始
     */
    private final static String RULE_THEN= "then";
    /**
     * 规则名称
     */
    private String ruleName;
    /**
     * 规则属性，可选
     */
    private RuleAttributesBuilder ruleAttributes;
    /**
     * 条件部分，可选，为空则为 true
     */
    private LHSBuilder lhs;
    /**
     * 行动部分，可选，为空则为 true
     */
    private RHSBuilder rhs;

    /**
     * @Param
     * @Return java.lang.String
     * @Description 构建 规则 string
     * <AUTHOR>
     * @Date 2024/4/9 17:20
     **/
    public String builderRuleString() {
        StrBuilder builder = new StrBuilder();

        builder.append(RULE_START).append(StrPool.C_SPACE).append(ruleName).append(StrPool.C_LF);
        if (ruleAttributes != null) {
            builder.append(ruleAttributes.builderAttributesString());
        }

        if (lhs != null) {
            builder.append(StrPool.C_TAB).append(RULE_WHEN).append(StrPool.C_LF);
            builder.append(StrPool.C_TAB).append(StrPool.C_TAB).append(lhs.builderLHSString()).append(StrPool.C_LF);
        }

        if (rhs != null) {
            builder.append(StrPool.C_TAB).append(RULE_THEN).append(StrPool.C_LF);
            builder.append(StrPool.C_TAB).append(StrPool.C_TAB).append(rhs.builderRHSString()).append(StrPool.C_LF);
        }
        builder.append(RULE_END).append(StrPool.C_LF);

        return builder.toString();
    }
}
