package com.puree.followup.domain.followup.model;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.puree.followup.domain.drools.constant.ComparisonOperatorEnum;
import com.puree.followup.enums.SmartExecutorComparisonOperatorEnum;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import lombok.Data;

/**
 * @ClassName SmartExecutorCondition
 * <AUTHOR>
 * @Description 随访分项设置-智能执行条件
 * @Date 2024/7/11 15:57
 * @Version 1.0
 */
@Data
public class SmartExecutorCondition {

    /**
     *  condition的id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     *  序号
     */
    private Integer conditionNum;

    /**
     * 第一级选项
     */
    private String firstLevelOption;

    /**
     * 第一级选项的id（问卷-题目id）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private String firstLevelId;

    /**
     * 题目的类型：0.单选题  1.多选题  2.单行文本  3.多行文本  4.多行填空 5.量表  6.附件 7.说明文字题  8.患者指标 9.患者报告
     */
    private Integer questionType;

    /**
     * 第二级选项
     */
    private String secondLevelOptions;

    /**
     * 第二级选项id（问卷-选项id）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private String secondLevelId;

    /**
     * 比较运算符
     */
    private SmartExecutorComparisonOperatorEnum comparisonOperatorEnum;

    /**
     * 预期值
     */
    private String expectedValue;

    /**
     * 额外的预期值，用于区间内的比较
     */
    private String additionalExpectedValue;

    /**
     * 前端设置条件的标识字段
     */
    private String settingType;

    /**
     * condition编辑时的行为：add、update、del
     */
    private String action;
}
