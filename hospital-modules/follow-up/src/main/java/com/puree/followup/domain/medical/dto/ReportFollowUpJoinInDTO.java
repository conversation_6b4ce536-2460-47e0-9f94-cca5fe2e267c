package com.puree.followup.domain.medical.dto;

import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportFollowUpJoinInDTO
 * <AUTHOR>
 * @Description 患者体检报告入组DTO
 * @Date 2024/7/8 17:43
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportFollowUpJoinInDTO {

    /**
     * 患者基本信息
     */
    private BusPatientFamilyVo userVO;
    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 体检报告信息结构
     */
    private ExamReportInfoDTO examReportInfoDTO;

}
