package com.puree.followup.question.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.puree.followup.admin.classification.mapper.ClassificationMapper;
import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.util.JudgeExecuteDateUtil;
import com.puree.followup.constants.FollowUpConstants;
import com.puree.followup.domain.classification.model.Classification;
import com.puree.followup.domain.followup.dto.PatientWithRuleDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.helper.AppletQrCodeHelper;
import com.puree.followup.question.domain.dto.FindPatientToSendDTO;
import com.puree.followup.question.domain.dto.PatientQuestionDTO;
import com.puree.followup.question.domain.dto.QueryPatientDTO;
import com.puree.followup.question.domain.dto.QueryQuestionDTO;
import com.puree.followup.question.domain.dto.QuestionDTO;
import com.puree.followup.question.domain.dto.QuestionStatusDTO;
import com.puree.followup.question.domain.dto.SendQuestionToPatientDTO;
import com.puree.followup.question.domain.model.FollowUpQuestionRecord;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.domain.model.QuestionDeptDoctorRecord;
import com.puree.followup.question.domain.vo.QuestionVO;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.enums.QuestionStatusEnum;
import com.puree.followup.question.mapper.FollowUpQuestionRecordMapper;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.question.mapper.QuestionDeptDoctorRecordMapper;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.followup.question.service.QuestionAdminService;
import com.puree.followup.question.service.SycQuestionStatusService;
import com.puree.followup.question.util.HandleScopePatientUtil;
import com.puree.followup.question.util.SendQuestionToPatientUtil;
import com.puree.hospital.app.api.RemoteWeChatSerivce;
import com.puree.hospital.business.api.RemoteBusPatientService;
import com.puree.hospital.business.api.RemoteHospitalService;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.business.api.model.BusPatientDto;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.core.constant.TemplateMsgConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.QRCodeUtil;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.utils.http.HttpUtil;
import com.puree.hospital.common.oss.OSSSaveDirectory;
import com.puree.hospital.common.oss.OSSUtil;
import com.puree.hospital.common.redis.service.RedisService;
import com.puree.hospital.followup.api.model.QRJoinInDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName: QuestionAdminServiceImpl
 * @Date 2024/6/12 18:04
 * <AUTHOR> jain
 * @Description:
 * @Version 1.0
 */
@Service
@Slf4j
@RefreshScope
public class QuestionAdminServiceImpl implements QuestionAdminService {

    @Autowired
    private QuestionMapper questionMapper;
    @Autowired
    private QuestionDeptDoctorRecordMapper questionDeptDoctorRecordMapper;
    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper;
    @Autowired
    private FollowUpQuestionRecordMapper followUpQuestionRecordMapper ;
    @Autowired
    private ClassificationMapper classificationMapper ;
    @Autowired
    private RemoteBusPatientService remoteBusPatientService ;
    @Autowired
    private HandleScopePatientUtil handleScopePatientUtil ;
    @Autowired
    private RedisTemplate redisTemplate ;
    @Value("${cache.patient.duration:3600}")
    private Integer cachePatientDuration ;
    @Value("${send.patient.question.lock:120}")
    private Integer sendPatientQuestionLock;
    @Autowired
    private RedisService redisService ;
    @Autowired
    private SendQuestionToPatientUtil sendQuestionToPatientUtil ;
    @Autowired
    private RemoteWeChatSerivce remoteWeChatSerivce;
    @Autowired
    private FollowUpMapper followUpMapper ;
    @Autowired
    private RemoteHospitalService remoteHospitalService ;
    @Autowired
    private OSSUtil ossUtil ;
    @Autowired
    private SycQuestionStatusService sycQuestionStatusService ;
    @Autowired
    private AppletQrCodeHelper appletQrCodeHelper;
    private static final String JPEG = ".jpeg";

    @Transactional(rollbackFor = Exception.class)
    public void adminAddQuestion(QuestionDTO req) {

        this.checkQuestionData(req);

        Date now = new Date();

        if (YesNoEnum.YES.getCode().equals(req.getCloseType())) {
            if (null == req.getCloseBeginDay()) {
                throw new ServiceException("没有选择问卷关闭时间!");
            }

            if ( now.compareTo(req.getCloseBeginDay()) >= 0 ) {
                req.setStatus(QuestionStatusEnum.ENABLE.getIndex()) ;
            } else {
                req.setStatus(QuestionStatusEnum.PUBLISH.getIndex()) ;
            }

        }else {
            req.setStatus(QuestionStatusEnum.PUBLISH.getIndex()) ;
        }

        Question questionDO = new Question();
        BeanUtils.copyProperties(req, questionDO);
        questionDO.setId(null) ;
        questionDO.setHospitalId(null == SecurityUtils.getHospitalId() ? FollowUpConstants.OPERATE_HOSPITAL_ID : SecurityUtils.getHospitalId());
        questionDO.setRevision(FollowUpConstants.DEFAULT_REVISION);
        questionDO.setStatus(req.getStatus()) ;
        questionDO.setCreateTime(now);
        questionDO.setUpdateTime(now);
        questionDO.setCreateBy(SecurityUtils.getUsername());
        questionDO.setUpdateBy(SecurityUtils.getUsername());
        questionDO.setIsDelete(YesNoEnum.NO.getCode());

        questionMapper.insert(questionDO);

        if (YesNoEnum.YES.getCode().equals(req.getDepartmentLimit())) {
            List<List<Long>> departmentCascadeIds = req.getDepartmentCascadeIds();
            for (List<Long> departmentCascade : departmentCascadeIds) {
                QuestionDeptDoctorRecord questionDeptDoctorRecord = new QuestionDeptDoctorRecord();
                questionDeptDoctorRecord.setQuestionId(questionDO.getId());
                questionDeptDoctorRecord.setDepartmentIds(departmentCascade);
                questionDeptDoctorRecord.setDepartmentId(departmentCascade.get(departmentCascade.size() - 1));
                questionDeptDoctorRecord.setCreateTime(now);
                questionDeptDoctorRecord.setUpdateTime(now);
                questionDeptDoctorRecord.setCreateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setUpdateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setIsDelete(YesNoEnum.NO.getCode());
                questionDeptDoctorRecordMapper.insert(questionDeptDoctorRecord);
            }
        }

        if (YesNoEnum.YES.getCode().equals(req.getDoctorLimit())) {
            List<Long> doctorIds = req.getDoctorIds();
            for (Long doctorId : doctorIds) {
                QuestionDeptDoctorRecord questionDeptDoctorRecord = new QuestionDeptDoctorRecord();
                questionDeptDoctorRecord.setQuestionId(questionDO.getId());
                questionDeptDoctorRecord.setDoctorId(doctorId);
                questionDeptDoctorRecord.setCreateTime(now);
                questionDeptDoctorRecord.setUpdateTime(now);
                questionDeptDoctorRecord.setCreateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setUpdateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setIsDelete(YesNoEnum.NO.getCode());
                questionDeptDoctorRecordMapper.insert(questionDeptDoctorRecord);
            }
        }


    }

    private void checkQuestionData(QuestionDTO req) {

        if (YesNoEnum.YES.getCode().equals(req.getDepartmentLimit())) {
            if (null == req.getDepartmentCascadeIds() || req.getDepartmentCascadeIds().isEmpty()) {
                throw new ServiceException("没有选择自定义科室!");
            }
        }

        if (YesNoEnum.YES.getCode().equals(req.getDoctorLimit())) {
            if (null == req.getDoctorIds() || req.getDoctorIds().isEmpty()) {
                throw new ServiceException("没有选择自定义医生!");
            }
        }

        if (YesNoEnum.YES.getCode().equals(req.getIsScore())) {
            if (null == req.getQuestionScoreValues() || req.getQuestionScoreValues().isEmpty()) {
                throw new ServiceException("计分问卷没有设置总分评定!");
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void adminEditQuestion(QuestionDTO req) {
        if (null == req.getId()) {
            throw new ServiceException("no id");
        }
        this.checkQuestionData(req);

        Question questionQuery = new Question();
        questionQuery.setId(req.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());

        Question questionDO = questionMapper.selectById(questionQuery);
        if (null == questionDO || null == questionDO.getId()) {
            throw new ServiceException("不存在此问卷");
        }

        Date now = new Date();

        if (YesNoEnum.YES.getCode().equals(req.getCloseType())) {
            if (null == req.getCloseBeginDay()) {
                throw new ServiceException("没有选择问卷关闭时间!");
            }

            if (QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus())) {
                if ( now.compareTo(req.getCloseBeginDay()) >= 0 ) {
                    req.setStatus(QuestionStatusEnum.ENABLE.getIndex()) ;
                } else {
                    req.setStatus(questionDO.getStatus()) ;
                }
            }

        }else {
            req.setStatus(questionDO.getStatus()) ;
        }

        if (QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus())) {
            throw new ServiceException("该问卷已发布，无法编辑");
        }

        PatientQuestionDTO patientQuestionDTO = new PatientQuestionDTO();
        patientQuestionDTO.setQuestionId(questionDO.getId());
        patientQuestionDTO.setIsDelete(YesNoEnum.NO.getCode());
        patientQuestionDTO.setFillStatusList(Arrays.asList(FillQuestionStatusEnum.FILLING.getIndex(), FillQuestionStatusEnum.FINISHED.getIndex()));
        int i = patientQuestionRecordMapper.countQuestionByFillStatus(patientQuestionDTO);
        if (i > 0) {
            throw new ServiceException("该问卷已有数据，无法编辑");
        }

        BeanUtils.copyProperties(req, questionDO);
        questionDO.setStatus(req.getStatus()) ;
        questionDO.setUpdateTime(now);
        questionDO.setUpdateBy(SecurityUtils.getUsername());
        questionDO.setIsDelete(YesNoEnum.NO.getCode());
        questionMapper.updateByCondition(questionDO);

        QuestionDeptDoctorRecord questionDeptDoctorRecordDO = new QuestionDeptDoctorRecord();
        questionDeptDoctorRecordDO.setQuestionId(req.getId());
        questionDeptDoctorRecordDO.setIsDelete(YesNoEnum.YES.getCode());
        questionDeptDoctorRecordDO.setUpdateTime(now);
        questionDeptDoctorRecordDO.setUpdateBy(SecurityUtils.getUsername());
        questionDeptDoctorRecordMapper.updateByQuestion(questionDeptDoctorRecordDO);

        if (YesNoEnum.YES.getCode().equals(req.getDepartmentLimit())) {
            List<List<Long>> departmentCascadeIds = req.getDepartmentCascadeIds();
            for (List<Long> departmentCascade : departmentCascadeIds) {
                QuestionDeptDoctorRecord questionDeptDoctorRecord = new QuestionDeptDoctorRecord();
                questionDeptDoctorRecord.setQuestionId(questionDO.getId());
                questionDeptDoctorRecord.setDepartmentIds(departmentCascade);
                questionDeptDoctorRecord.setDepartmentId(departmentCascade.get(departmentCascade.size() - 1));
                questionDeptDoctorRecord.setCreateTime(now);
                questionDeptDoctorRecord.setUpdateTime(now);
                questionDeptDoctorRecord.setCreateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setUpdateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setIsDelete(YesNoEnum.NO.getCode());
                questionDeptDoctorRecordMapper.insert(questionDeptDoctorRecord);
            }
        }

        if (YesNoEnum.YES.getCode().equals(req.getDoctorLimit())) {
            List<Long> doctorIds = req.getDoctorIds();
            for (Long doctorId : doctorIds) {
                QuestionDeptDoctorRecord questionDeptDoctorRecord = new QuestionDeptDoctorRecord();
                questionDeptDoctorRecord.setQuestionId(questionDO.getId());
                questionDeptDoctorRecord.setDoctorId(doctorId);
                questionDeptDoctorRecord.setCreateTime(now);
                questionDeptDoctorRecord.setUpdateTime(now);
                questionDeptDoctorRecord.setCreateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setUpdateBy(SecurityUtils.getUsername());
                questionDeptDoctorRecord.setIsDelete(YesNoEnum.NO.getCode());
                questionDeptDoctorRecordMapper.insert(questionDeptDoctorRecord);
            }
        }

        if ( QuestionStatusEnum.ENABLE.getIndex().equals(questionDO.getStatus()) ){
            PatientQuestionDTO updateDTO = new PatientQuestionDTO();
            updateDTO.setQuestionId(questionDO.getId());
            updateDTO.setFillStatus(FillQuestionStatusEnum.CLOSED.getIndex());
            updateDTO.setFillStatusList(Arrays.asList(FillQuestionStatusEnum.INVITING.getIndex(), FillQuestionStatusEnum.FILLING.getIndex()));
            patientQuestionRecordMapper.updateFillStatusByFillStatus(updateDTO);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void adminDeleteQuestion(Long id, Integer revision){
        Question questionQuery = new Question();
        questionQuery.setId(id);
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());

        Question questionDO = questionMapper.selectById(questionQuery);
        if (null == questionDO || null == questionDO.getId()) {
            throw new ServiceException("不存在此问卷");
        }

        if (QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus())) {
            throw new ServiceException("该问卷已发布，无法删除");
        }

        PatientQuestionDTO patientQuestionDTO = new PatientQuestionDTO();
        patientQuestionDTO.setQuestionId(questionDO.getId());
        patientQuestionDTO.setIsDelete(YesNoEnum.NO.getCode());
        patientQuestionDTO.setFillStatusList(Arrays.asList(FillQuestionStatusEnum.FILLING.getIndex(), FillQuestionStatusEnum.FINISHED.getIndex()));
        int i = patientQuestionRecordMapper.countQuestionByFillStatus(patientQuestionDTO);
        if (i > 0) {
            throw new ServiceException("该问卷已有数据，无法删除");
        }

        FollowUpQuestionRecord followUpQuestionRecordQuery = new FollowUpQuestionRecord();
        followUpQuestionRecordQuery.setQuestionId(questionDO.getId()) ;
        followUpQuestionRecordQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        int t = followUpQuestionRecordMapper.countByCondition(followUpQuestionRecordQuery);
        if (t > 0) {
            throw new ServiceException("该问卷已关联随访，无法删除");
        }

        Date now = new Date();
        questionDO.setUpdateTime(now);
        questionDO.setUpdateBy(SecurityUtils.getUsername());
        questionDO.setIsDelete(YesNoEnum.YES.getCode());
        questionMapper.updateByCondition(questionDO);

    }

    @Transactional(rollbackFor = Exception.class)
    public void adminUpdateStatus(QuestionStatusDTO req){

        if ( !QuestionStatusEnum.CLOSE.getIndex().equals(req.getStatus())
                && !QuestionStatusEnum.ENABLE.getIndex().equals(req.getStatus())
                && !QuestionStatusEnum.PUBLISH.getIndex().equals(req.getStatus()) ) {

            throw new ServiceException("status错误") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(req.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());
        Question questionDO = questionMapper.selectById(questionQuery);
        if (null == questionDO || null == questionDO.getId()) {
            throw new ServiceException("不存在此问卷");
        }

        Date now = new Date();

        if ( QuestionStatusEnum.PUBLISH.getIndex().equals(req.getStatus()) && YesNoEnum.YES.getCode().equals(questionDO.getCloseType()) ) {
            if ( now.compareTo(questionDO.getCloseBeginDay()) >= 0 ) {
                throw new ServiceException("当前时间大于问卷关闭时间，无法发布此问卷");
            }
        }

        Question updateReq = new Question() ;
        updateReq.setId(req.getId()) ;
        updateReq.setStatus(req.getStatus()) ;
        updateReq.setUpdateTime(now);
        updateReq.setUpdateBy(SecurityUtils.getUsername());
        questionMapper.updateByCondition(updateReq);

        if ( QuestionStatusEnum.ENABLE.getIndex().equals(req.getStatus()) ){
            PatientQuestionDTO updateDTO = new PatientQuestionDTO();
            updateDTO.setQuestionId(questionDO.getId());
            updateDTO.setFillStatus(FillQuestionStatusEnum.CLOSED.getIndex());
            updateDTO.setFillStatusList(Arrays.asList(FillQuestionStatusEnum.INVITING.getIndex(), FillQuestionStatusEnum.FILLING.getIndex()));
            patientQuestionRecordMapper.updateFillStatusByFillStatus(updateDTO);
        }

    }

    public List<QuestionVO> adminQuestionPage(QueryQuestionDTO req){

        sycQuestionStatusService.sycQuestionStatus();

        Question questionQuery = new Question();
        questionQuery.setHospitalId( null==SecurityUtils.getHospitalId() ? FollowUpConstants.OPERATE_HOSPITAL_ID : SecurityUtils.getHospitalId() ) ;
        questionQuery.setName(req.getQuestionName()) ;
        questionQuery.setClassifyId(req.getClassifyId()) ;
        questionQuery.setStatus(req.getStatus()) ;
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;

        List<Question> questionList = new ArrayList<>() ;

        PageUtil.startPage();
        // 关联随访
        if ( YesNoEnum.YES.getCode().equals(req.getRelateFollowUp()) ) {
            questionList = questionMapper.findQuestionRelateFollowUp(questionQuery) ;
        }

        // 不关联随访
        if ( YesNoEnum.NO.getCode().equals(req.getRelateFollowUp()) ) {
            questionList = questionMapper.findQuestionNoRelateFollowUp(questionQuery) ;
        }

        // 全部
        if ( null==req.getRelateFollowUp() ) {
            questionList = questionMapper.selectByCondition(questionQuery) ;
        }

        List<QuestionVO> resultList = new ArrayList<>() ;

        if ( null== questionList || questionList.isEmpty() ) {
            return resultList ;
        }

        for ( Question question : questionList) {

            QuestionVO questionVO = new QuestionVO();
            BeanUtils.copyProperties(question, questionVO);

            Classification classification = classificationMapper.getById(question.getClassifyId());
            if (null!=classification) {
                questionVO.setClassifyName(classification.getName());
            }

            questionVO.setRelateFollowUp(req.getRelateFollowUp());

            // 全部
            if ( null==questionVO.getRelateFollowUp() ) {
                FollowUpQuestionRecord recordQuery = new FollowUpQuestionRecord() ;
                recordQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
                recordQuery.setQuestionId(questionVO.getId()) ;
                int i = followUpQuestionRecordMapper.countByCondition(recordQuery);
                if (i>0) {
                    questionVO.setRelateFollowUp(YesNoEnum.YES.getCode());
                }else {
                    questionVO.setRelateFollowUp(YesNoEnum.NO.getCode());
                }
            }

            PatientQuestionDTO patientQuestionDTO = new PatientQuestionDTO();
            patientQuestionDTO.setHospitalId(question.getHospitalId());
            patientQuestionDTO.setQuestionId(question.getId());
            patientQuestionDTO.setIsDelete(YesNoEnum.NO.getCode());
            patientQuestionDTO.setFillStatusList(Arrays.asList(FillQuestionStatusEnum.FINISHED.getIndex()));
            int finishCopy = patientQuestionRecordMapper.countQuestionByFillStatus(patientQuestionDTO);
            questionVO.setFinishCopy(finishCopy);

            int finishPerson = patientQuestionRecordMapper.countPatientByFillStatus(patientQuestionDTO);
            questionVO.setFinishPerson(finishPerson);

            resultList.add(questionVO) ;

        }

        return PageUtil.buildPage(questionList, resultList);

    }

    public QuestionVO adminQuestionDetail(Long id) {

        sycQuestionStatusService.sycQuestionStatus();

        Question questionQuery = new Question();
        questionQuery.setId(id) ;
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;

        Question questionDO = questionMapper.selectById(questionQuery);
        if ( null==questionDO || null==questionDO.getId() ) {
            throw new ServiceException("没有此问卷") ;
        }

        QuestionVO questionVO = new QuestionVO();
        BeanUtils.copyProperties(questionDO, questionVO);

        QuestionDeptDoctorRecord questionDeptDoctorRecordQuery = new QuestionDeptDoctorRecord();
        questionDeptDoctorRecordQuery.setQuestionId(questionDO.getId()) ;
        questionDeptDoctorRecordQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        List<QuestionDeptDoctorRecord> questionDeptDoctorRecords = questionDeptDoctorRecordMapper.selectByCondition(questionDeptDoctorRecordQuery);

        if ( null==questionDeptDoctorRecords || questionDeptDoctorRecords.isEmpty() ) {
            return questionVO ;
        }

        List<List<Long>> departmentCascadeIds = new ArrayList<>() ;
        List<Long> doctorIds = new ArrayList<>() ;
        for (QuestionDeptDoctorRecord e : questionDeptDoctorRecords) {

            if ( null!=e.getDepartmentIds() && !e.getDepartmentIds().isEmpty() ) {
                departmentCascadeIds.add(e.getDepartmentIds()) ;
            }

            if ( null!=e.getDoctorId() ) {
                doctorIds.add(e.getDoctorId()) ;
            }

        }

        questionVO.setDepartmentCascadeIds(departmentCascadeIds);
        questionVO.setDoctorIds(doctorIds) ;

        return questionVO ;

    }

    @Transactional(rollbackFor = Exception.class)
    public void adminCopyQuestion(Long id) {

        Question questionQuery = new Question();
        questionQuery.setId(id) ;
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;

        Question oldQuestion = questionMapper.selectById(questionQuery);
        if ( null== oldQuestion || null== oldQuestion.getId() ) {
            throw new ServiceException("没有此问卷") ;
        }

        QuestionDeptDoctorRecord questionDeptDoctorRecordQuery = new QuestionDeptDoctorRecord();
        questionDeptDoctorRecordQuery.setQuestionId(id) ;
        questionDeptDoctorRecordQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        List<QuestionDeptDoctorRecord> oldRecordList = questionDeptDoctorRecordMapper.selectByCondition(questionDeptDoctorRecordQuery);

        Date now = new Date();
        oldQuestion.setId(null) ;
        oldQuestion.setStatus(QuestionStatusEnum.PUBLISH.getIndex()) ;
        oldQuestion.setCreateTime(now);
        oldQuestion.setUpdateTime(now);
        oldQuestion.setCreateBy(SecurityUtils.getUsername());
        oldQuestion.setUpdateBy(SecurityUtils.getUsername());
        questionMapper.insert(oldQuestion);

        if ( null==oldRecordList || oldRecordList.isEmpty() ) {
            return ;
        }

        for ( QuestionDeptDoctorRecord e : oldRecordList) {
            e.setId(null) ;
            e.setQuestionId(oldQuestion.getId()) ;
            e.setCreateTime(now);
            e.setUpdateTime(now);
            e.setCreateBy(SecurityUtils.getUsername());
            e.setUpdateBy(SecurityUtils.getUsername());
            questionDeptDoctorRecordMapper.insert(e) ;
        }

    }

    /**
     *  发送问卷给全部患者
     */
    public void sendQuestionToAllPatient(SendQuestionToPatientDTO req) {

        sycQuestionStatusService.sycQuestionStatus();

        if ( null== req.getId() ) {
            throw new ServiceException("问卷id缺失") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(req.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if ( QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus()) ) {
            throw new ServiceException("问卷关闭");
        }

        String lockKey = Constants.SEND_QUESTION_TO_PATIENT_LOCK + question.getId() ;
        boolean flag = redisService.setIfAbsent(lockKey, Long.valueOf(sendPatientQuestionLock), TimeUnit.SECONDS);
        if (!flag) {
            throw new ServiceException("正在批量发送问卷给患者，请稍后再试") ;
        }

        BusPatientDto patientDto = new BusPatientDto() ;
        patientDto.setHospitalId(SecurityUtils.getHospitalId());

        sendQuestionToPatientUtil.sendQuestionToAllPatient(patientDto, question, lockKey) ;

    }



    /**
     * 指定范围，多条件差查找患者
     **/
    public String findPatientAndCacheByScope(PatientWithRuleDTO patientWithRuleDTO){

        sycQuestionStatusService.sycQuestionStatus();

        if ( null==patientWithRuleDTO.getId() ) {
            throw new ServiceException("问卷id缺失") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(patientWithRuleDTO.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if (QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus())) {
            throw new ServiceException("问卷关闭");
        }

        String snowId = IdUtil.getSnowflake(1, 1).nextIdStr() ;

        if(patientWithRuleDTO.getJoinRuleList() == null) {
            patientWithRuleDTO.setJoinRuleList(new ArrayList<>());
        }

        handleScopePatientUtil.asyncHandlePatientsByRules(patientWithRuleDTO, question , snowId, cachePatientDuration);

        return snowId ;

    }



    /**
     * 根据患者名字，手机号码，查找患者
     **/
    public String findPatientAndCacheByKeyword(QueryPatientDTO req){

        sycQuestionStatusService.sycQuestionStatus();

        String snowId = IdUtil.getSnowflake(1, 1).nextIdStr() ;

        if ( null== req.getId() ) {
            throw new ServiceException("问卷id缺失") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(req.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if (QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus())) {
            throw new ServiceException("问卷关闭");
        }

        BusPatientDto patientDto = new BusPatientDto() ;
        patientDto.setKeyword(req.getKeyword());
        patientDto.setHospitalId(SecurityUtils.getHospitalId());
        patientDto.setIsVisible(Boolean.TRUE);
        R<List<BusPatientFamilyVo>> r = remoteBusPatientService.listFamilyInfo(patientDto);
        if ( null==r.getData() || r.getData().isEmpty() ) {
            String key = Constants.QUESTION_FIND_PATIENT_END + question.getId() + ":" + snowId ;
            redisTemplate.opsForValue().set(key, YesNoEnum.YES.getCode(), cachePatientDuration, TimeUnit.SECONDS);
            return snowId ;
        }

        List<BusPatientFamilyVo> allPatientFamilyVoList = r.getData();
        List<Long> allPatientIdList = allPatientFamilyVoList.stream().map(BusPatientFamilyVo::getId).distinct().collect(Collectors.toList());

        List<Long> finishPatientIdList = patientQuestionRecordMapper.findPatientByFillStatus(question.getHospitalId(), question.getId(),
                YesNoEnum.NO.getCode(), Arrays.asList(FillQuestionStatusEnum.FILLING.getIndex(), FillQuestionStatusEnum.FINISHED.getIndex()),
                question.getFillTimes() );

        List<BusPatientFamilyVo> unFinishPatientFamilyVoList  = new ArrayList<>() ;

        if ( null==finishPatientIdList || finishPatientIdList.isEmpty() ) {

            unFinishPatientFamilyVoList.addAll(allPatientFamilyVoList) ;

        }else {

            for ( Long patientId : allPatientIdList ) {
                if (!finishPatientIdList.contains(patientId)){
                    BusPatientFamilyVo patientFamilyVo = allPatientFamilyVoList.stream().filter(e -> patientId.equals(e.getId())).findFirst().get();
                    unFinishPatientFamilyVoList.add(patientFamilyVo) ;
                }
            }

        }

        if (!unFinishPatientFamilyVoList.isEmpty()) {
            String key1 = Constants.QUESTION_FIND_PATIENT + question.getId() + ":" + snowId ;
            redisTemplate.opsForList().rightPushAll(key1, unFinishPatientFamilyVoList);
            redisTemplate.expire(key1 , cachePatientDuration, TimeUnit.SECONDS);
        }

        String key2 = Constants.QUESTION_FIND_PATIENT_END + question.getId() + ":" + snowId ;
        redisTemplate.opsForValue().set(key2, YesNoEnum.YES.getCode(), cachePatientDuration, TimeUnit.SECONDS);

        return snowId ;

    }

    public Boolean checkPatientPrepared(FindPatientToSendDTO req) {
        String key = Constants.QUESTION_FIND_PATIENT_END + req.getId() + ":" + req.getSnowId() ;
        Boolean flag = redisTemplate.hasKey(key);
        return flag ;
    }



    public Paging<List<BusPatientFamilyVo>> pagePatientToSend(FindPatientToSendDTO req) {

        sycQuestionStatusService.sycQuestionStatus();

        Long questionId = req.getId() ;
        String snowId = req.getSnowId();

        Boolean flag = this.checkPatientPrepared(req) ;
        if (!flag) {
            throw new ServiceException("查询的患者信息失效，请重新查询") ;
        }

        String key = Constants.QUESTION_FIND_PATIENT + questionId + ":" + snowId ;
        Boolean hasKey = redisTemplate.hasKey(key);

        List<BusPatientFamilyVo> resultList = new ArrayList<>() ;
        if (!hasKey) {
            return Paging.success(resultList, 0L, 0, 0);
        }

        resultList = redisTemplate.opsForList().range( key, 0, -1);

        if ( null!=req.getExcludeIdList() && !req.getExcludeIdList().isEmpty() ) {
            resultList.removeIf(e -> req.getExcludeIdList().contains(e.getId())) ;
        }

        if (resultList.isEmpty()) {
            return Paging.success(resultList, 0L, 0, 0);
        }

        Long totalCount = (long) resultList.size();

        resultList = PageUtil.buildPage(resultList, req.getPageSize(), req.getPageNum()) ;

        Paging<List<BusPatientFamilyVo>> page = Paging.success(resultList, totalCount, req.getPageNum(), req.getPageSize());
        return page ;

    }


    public void sendQuestionToPatient(SendQuestionToPatientDTO req){

        sycQuestionStatusService.sycQuestionStatus();

        String lockKey = Constants.SEND_QUESTION_TO_PATIENT_LOCK + req.getId() ;
        boolean flag = redisService.setIfAbsent(lockKey, Long.valueOf(sendPatientQuestionLock), TimeUnit.SECONDS);
        if (!flag) {
            throw new ServiceException("正在批量发送问卷给患者，请稍后再试") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(req.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if ( QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus()) ) {
            throw new ServiceException("问卷关闭");
        }

        String snowId = req.getSnowId() ;

        String key = Constants.QUESTION_FIND_PATIENT_END + question.getId() + ":" + snowId ;
        if (!redisTemplate.hasKey(key)) {
            log.error("缓存待发送问卷的患者信息失效, question id is " + question.getId() +  ", snowId is " + snowId);
            redisTemplate.delete(key) ;
            throw new ServiceException("查询的患者信息失效，请重新查询再发送问卷!");
        }

        sendQuestionToPatientUtil.sendQuestionToPatient( question,
                req.getExcludeIdList(),
                lockKey,
                Constants.QUESTION_FIND_PATIENT + question.getId() + ":" + snowId );

    }



    public String produceQrCode(Long id){

        Question questionQuery = new Question();
        questionQuery.setId(id);
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if (QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus())) {
            throw new ServiceException("该问卷已禁用，无法查看");
        }

        Long hospitalId = question.getHospitalId() ;
        String accessToken = remoteWeChatSerivce.getToken(question.getHospitalId()).getData();

        //组装生成二维码所需参数
        JSONObject param = new JSONObject();
        param.put("action_name", "QR_LIMIT_STR_SCENE");
        JSONObject scene = new JSONObject();
        scene.put("scene_str", FollowUpConstants.QUESTION_QRCODE_IDENTIFY + id + "," + hospitalId);
        JSONObject actionInfo = new JSONObject();
        actionInfo.put("scene", scene);
        param.put("action_info", actionInfo);
        JSONObject jsonObject = HttpUtil.doPost(FollowUpConstants.QRCODE_TICKET + accessToken, param.toString());

        //判断token 是否失效：如果失效，刷新token并重新请求ticket
        if (jsonObject.containsKey("errcode")) {
            accessToken = remoteWeChatSerivce.refreshToken(hospitalId).getData();
            jsonObject =  HttpUtil.doPost(FollowUpConstants.QRCODE_TICKET + accessToken, param.toString());
        }

        if(StrUtil.isBlank(accessToken)) {
            throw new ServiceException("公众号未配置");
        }

        String ticket = jsonObject.getString("ticket");
        String url = FollowUpConstants.QRCODE_MP + URLEncoder.encode(ticket);
        return url;

    }

    public R scanQrCodeJoinQuestion(QRJoinInDTO req){

        Question questionQuery = new Question();
        questionQuery.setId(req.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            return R.fail(FollowUpConstants.QUESTION_NOT_FOUND) ;
        }
        if ( QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus()) ) {
            return R.fail(FollowUpConstants.QUESTION_NOT_FOUND) ;
        }

        BusPatientDto patientDto = new BusPatientDto() ;
        patientDto.setHospitalId(req.getHospitalId());
        patientDto.setPatientId(req.getUserId());
        patientDto.setIsVisible(Boolean.TRUE);
        R<List<BusPatientFamilyVo>> r = remoteBusPatientService.listFamilyInfo(patientDto);
        if ( null==r || null==r.getData() || r.getData().isEmpty() ) {
            return R.fail(FollowUpConstants.USER_NOT_FOUND) ;
        }

        List<BusPatientFamilyVo> patientFamilyVoList = r.getData();
        patientFamilyVoList.forEach( patientFamilyVo->{
            sendQuestionToPatientUtil.sendQuestionToPatientByQrCode(question, patientFamilyVo) ;
        });

        return R.ok(null , TemplateMsgConstants.FOLLOW_UP_EVENT_QUESTION) ;

    }

    /**
     * 医院后台 获取问卷列表（只获取问卷基本信息列表，不做统计等复杂查询操作）
     *
     * @param req 查询条件
     * @return 问卷基本信息列表
     * <AUTHOR>
     */
    @Override
    public List<QuestionVO> adminQuestionInfoList(QueryQuestionDTO req) {

        sycQuestionStatusService.sycQuestionStatus();

        Question questionQuery = new Question();
        questionQuery.setHospitalId( null==SecurityUtils.getHospitalId() ? FollowUpConstants.OPERATE_HOSPITAL_ID : SecurityUtils.getHospitalId() ) ;
        questionQuery.setClassifyId(req.getClassifyId()) ;
        questionQuery.setStatus(req.getStatus()) ;
        questionQuery.setStatusOrder(true); //随访分项：添加问卷时，需要把所有状态的问卷都返回，并且按照状态值排序
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        List<Question> questionList = questionMapper.selectByCondition(questionQuery) ;
        List<QuestionVO> resultList = new ArrayList<>() ;

        for (Question question : questionList) {
            QuestionVO questionVO = new QuestionVO();
            BeanUtils.copyProperties(question, questionVO);
            resultList.add(questionVO) ;
        }
        return resultList;
    }

    public List<PatientQuestionRecord> adminPagePatientQuestion(Long questionId, Long patientId , String patientInfo, Long doctorId, Long followUpId, Date beginDate, Date endDate){

        sycQuestionStatusService.sycQuestionStatus();

        BusPatientDto patientDto = new BusPatientDto();
        patientDto.setHospitalId(SecurityUtils.getHospitalId());
        patientDto.setId(patientId);
        if ( null==patientId && StrUtil.isNotBlank(patientInfo) ) {

            if (StrUtil.isNumeric(patientInfo)) {
                patientDto.setCellPhoneNumber(patientInfo);
            }else {
                patientDto.setName(patientInfo);
            }

        }

        List<BusPatientFamilyVo> patientFamilyVoList = new ArrayList<>() ;

        R<List<BusPatientFamilyVo>> familyInfoR = remoteBusPatientService.listFamilyInfo(patientDto);
        if ( null!=familyInfoR && null!=familyInfoR.getData() && !familyInfoR.getData().isEmpty() ) {
            patientFamilyVoList = familyInfoR.getData();
        }

        List<PatientQuestionRecord> patientQuestionRecordList = new ArrayList<>() ;
        if (patientFamilyVoList.isEmpty()) {
            return patientQuestionRecordList ;
        }

        List<Long> patientIdList = patientFamilyVoList.stream().map(BusPatientFamilyVo::getId).distinct().collect(Collectors.toList())  ;

        PatientQuestionRecord req = new PatientQuestionRecord();
        req.setHospitalId(SecurityUtils.getHospitalId()) ;
        req.setQuestionId(questionId) ;
        req.setFollowUpId(followUpId) ;
        req.setDoctorId(doctorId) ;
        req.setFillStatus(FillQuestionStatusEnum.FINISHED.getIndex()) ;

        if (null!=beginDate) {
            req.setBeginDate(DateUtil.beginOfDay(beginDate)) ;
        }
        if (null!=endDate) {
            req.setEndDate(DateUtil.endOfDay(endDate)) ;
        }

        if (1==patientIdList.size()) {
            req.setPatientId(patientIdList.get(0)) ;
        }else {
            req.setPatientIdList(patientIdList) ;
        }

        PageUtil.startPage() ;
        patientQuestionRecordList = patientQuestionRecordMapper.selectByCondition(req);
        if ( null==patientQuestionRecordList || patientQuestionRecordList.isEmpty() ) {
            return patientQuestionRecordList ;
        }

        for (PatientQuestionRecord patientQuestionRecord : patientQuestionRecordList) {

            Optional<BusPatientFamilyVo> optional = patientFamilyVoList.stream().filter(e -> patientQuestionRecord.getPatientId().equals(e.getId())).findFirst();
            if (optional.isPresent()) {
                BusPatientFamilyVo patientFamilyVo = optional.get();
                patientQuestionRecord.setPatientName(patientFamilyVo.getName()) ;
                patientQuestionRecord.setPatientSex(patientFamilyVo.getSex()) ;
                patientQuestionRecord.setPatientAge(patientFamilyVo.getFamilyAge()) ;
            }

            if ( null!=patientQuestionRecord.getSubmitTime() && null!=patientQuestionRecord.getFillTime() ){
                String s = JudgeExecuteDateUtil.calcDateGapHms(patientQuestionRecord.getSubmitTime(), patientQuestionRecord.getFillTime());
                patientQuestionRecord.setFillDuration(s) ;
            }

            if (null!=patientQuestionRecord.getFollowUpId()) {
                FollowUp followUp = followUpMapper.getById(patientQuestionRecord.getFollowUpId());
                if (null!=followUp) {
                    patientQuestionRecord.setFollowUpName(followUp.getName()) ;
                }

            }

        }

        return PageUtil.buildPage(patientQuestionRecordList, patientQuestionRecordList) ;

    }

    public String produceQrCodeV2(Long id){

        Question questionQuery = new Question();
        questionQuery.setId(id);
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if (QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus())) {
            throw new ServiceException("该问卷已禁用，无法查看");
        }

        Long hospitalId = question.getHospitalId() ;

        R<BusHospitalWechatConfig> hospitalWechatConfigR = remoteHospitalService.queryConfigInfo(hospitalId, ClientTypeEnum.WX_OFFICIAL_ACCOUNT.getType());
        if ( null==hospitalWechatConfigR || null==hospitalWechatConfigR.getData()) {
            throw new ServiceException("该医院没有公众号配置");
        }
        BusHospitalWechatConfig hospitalWechatConfig = hospitalWechatConfigR.getData();
        String codeContent = hospitalWechatConfig.getTokenUrl() + TemplateMsgConstants.QUESTION_QR_CODE_URL  ;
        codeContent = String.format(codeContent, id, hospitalId);

        String url = "" ;
        try {
            BufferedImage image = QRCodeUtil.getBufferedImage(codeContent);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ImageIO.write(image, "jpeg", bos);
            MultipartFile multipartFile = new MockMultipartFile(codeContent + ".jpeg", codeContent + ".jpeg", "",
                    bos.toByteArray());

            url = updateHead(multipartFile);

        } catch (Exception ex) {
            log.error("生成问卷二维码失败", ex);
            throw new ServiceException("生成问卷二维码失败");
        }

        return url ;

    }

    private String updateHead(MultipartFile file) throws Exception {
        if (file == null || file.getSize() <= 0) {
            throw new Exception("file不能为空");
        }
        return ossUtil.simpleUpload(file, OSSSaveDirectory.QRCODE.getValue());
    }

    public Long scanQrCodeJoinQuestionV2(QRJoinInDTO req){

        Question questionQuery = new Question();
        questionQuery.setId(req.getId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException(FollowUpConstants.QUESTION_NOT_EXIT);
        }
        if (!QuestionStatusEnum.PUBLISH.getIndex().equals(question.getStatus())) {
            throw new ServiceException("该问卷已禁用，无法查看");
        }

        BusPatientDto patientDto = new BusPatientDto() ;
        patientDto.setHospitalId(req.getHospitalId());
        patientDto.setPatientId(req.getUserId());
        patientDto.setId(req.getPatientId());
        patientDto.setIsVisible(Boolean.TRUE);
        R<List<BusPatientFamilyVo>> r = remoteBusPatientService.listFamilyInfo(patientDto);
        if ( null==r || null==r.getData() || r.getData().isEmpty() ) {
            throw new ServiceException("没有查到患者");
        }

        BusPatientFamilyVo patientFamilyVo = r.getData().get(0) ;

        int i = patientQuestionRecordMapper.countOnePatientFillOneQuestionTimes(question.getHospitalId(), question.getId(), patientFamilyVo.getId(),
                YesNoEnum.NO.getCode(), Arrays.asList(FillQuestionStatusEnum.FINISHED.getIndex()));

        if ( i>=question.getFillTimes() ) {
            throw new ServiceException(FollowUpConstants.FILL_QUESTION_EXCESS) ;
        }

        return sendQuestionToPatientUtil.sendQuestionToPatientByQrCode(question, patientFamilyVo);

    }

    /**
     * 获取问卷小程序码
     *
     * @param id 问卷id
     * @param appletQrCodeUrl 小程序码url
     * @return 小程序码地址
     */
    @Override
    public String getAppletQrCode(Long id,String appletQrCodeUrl) {
        Question questionQuery = new Question();
        questionQuery.setId(id);
        questionQuery.setIsDelete(YesNoEnum.NO.getCode()) ;
        Question question = questionMapper.selectById(questionQuery);
        if ( null==question ) {
            throw new ServiceException("问卷不存在");
        }
        if (QuestionStatusEnum.CLOSE.getIndex().equals(question.getStatus())) {
            throw new ServiceException("该问卷已禁用，无法查看");
        }
        if (StringUtils.isNotEmpty(question.getAppletQrCode())) {
            return question.getAppletQrCode();
        }
        Long hospitalId = question.getHospitalId() ;
        // 获取access token
        String accessToken = appletQrCodeHelper.getAccessToken(hospitalId);
        String path = appletQrCodeUrl + String.format("?type=3&questionId=%s&hospitalId=%s",
                id,
                hospitalId);
        if (log.isDebugEnabled()) {
            log.debug("问卷小程序码，path：{}", path);
        }
        try {
            InputStream qrCode = appletQrCodeHelper.generateAppletQrCode(accessToken, path);
            String appletQrCodeAddress = appletQrCodeHelper.updateHead(qrCode, OSSSaveDirectory.QRCODE.getValue(), JPEG);
            question.setAppletQrCode(appletQrCodeAddress);
            questionMapper.updateByCondition(question);
            return appletQrCodeAddress;
        } catch (Exception e) {
            log.error("获取问卷小程序码失败", e);
            return null;
        }
    }


}