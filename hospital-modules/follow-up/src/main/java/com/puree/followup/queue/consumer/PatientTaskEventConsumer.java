package com.puree.followup.queue.consumer;

import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskEventHistoryMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskHistoryMapper;
import com.puree.followup.admin.util.JudgeExecuteDateUtil;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.FollowUpItemTask;
import com.puree.followup.domain.followup.model.PatientFollowUpItemRecord;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.PatientTaskEventHistory;
import com.puree.followup.domain.followup.model.PatientTaskHistory;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.enums.FollowUpJoinStatusEnum;
import com.puree.followup.enums.PatientTaskStatusEnum;
import com.puree.followup.enums.TaskEventTypeEnum;
import com.puree.followup.enums.TaskRemindStatusEnum;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.service.ImWxMessageService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: PatientTaskEventConsumer
 * @Date 2024/5/21 9:14
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
@Slf4j
public class PatientTaskEventConsumer extends RedisStreamConsumer<FollowUpItemTask> {

    private Logger logger = LoggerFactory.getLogger(PatientTaskEventConsumer.class);

    public static final String TASK = "task" ;

    @Autowired
    private PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper ;
    @Autowired
    private PatientTaskHistoryMapper patientTaskHistoryMapper ;
    @Autowired
    private FollowUpMapper followUpMapper;
    @Autowired
    private PatientFollowUpRecordMapper patientFollowUpRecordMapper ;
    @Autowired
    private PatientTaskEventHistoryMapper patientTaskEventHistoryMapper ;
    @Autowired
    private ImWxMessageService imWxMessageService ;
    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper ;


    @Override
    public void onMessage(RedisMessage<FollowUpItemTask> message) {

        try{

            FollowUpItemTask itemTask = message.getBody();
            this.patientTaskEvent(itemTask);

        }catch (Exception ex) {
            logger.error("patientTaskEventConsumer failed : " ,  ex);
        }

    }

    private void patientTaskEvent(FollowUpItemTask itemTask){

        List<PatientFollowUpItemRecord> patientItemList = patientFollowUpItemRecordMapper.selectPatientItem(itemTask);
        if (null==patientItemList || patientItemList.isEmpty()) {
            return  ;
        }

        for (PatientFollowUpItemRecord patientItem : patientItemList) {

            boolean flag = JudgeExecuteDateUtil.isExecuteDate(itemTask.getIntervalType(), itemTask.getIntervals(), itemTask.getWeekDays(), itemTask.getDayOfWeek(),
                    itemTask.getNow() , patientItem.getBeginDay(), patientItem.getFirstDayOfJoinWeek());

            if (flag) {
                patientTaskEvent(itemTask, patientItem, itemTask.getNow()) ;
            }

        }

    }

    private void patientTaskEvent( FollowUpItemTask itemTask, PatientFollowUpItemRecord patientItem, Date now) {

        PatientTaskHistory req = new PatientTaskHistory();
        req.setHospitalId(patientItem.getHospitalId()) ;
        req.setFollowUpId(patientItem.getFollowUpId()) ;
        req.setItemId(patientItem.getItemId()) ;
        req.setTaskId(itemTask.getId()) ;
        req.setFollowUpRecordId(patientItem.getFollowUpRecordId()) ;
        req.setItemRecordId(patientItem.getId()) ;
        req.setUserId(patientItem.getUserId()) ;
        req.setPatientId(patientItem.getPatientId()) ;
        req.setBeginDay(now) ;

        Long taskHistoryId;

        PatientTaskHistory patientTaskHistory = patientTaskHistoryMapper.selectTodayPatientTaskHistory(req);
        if ( null==patientTaskHistory || null==patientTaskHistory.getId() ) {

            int count = patientTaskHistoryMapper.countFutureTerminatedPatientTaskHistory(req);
            if (count>0) {
                return ;
            }else {
                req.setTaskName(itemTask.getTaskName()) ;
                req.setTaskRemindStatus(TaskRemindStatusEnum.REMINDED.getIndex()) ;
                req.setRemindTime(new Date());
                req.setCreateTime(new Date()) ;
                req.setUpdateTime(new Date()) ;
                req.setCreateBy(TASK) ;
                req.setUpdateBy(TASK) ;
                req.setIsTerminateFuture(YesNoEnum.NO.getCode());
                req.setTaskStatus(PatientTaskStatusEnum.ONGOING.getIndex()) ;
                try{
                    patientTaskHistoryMapper.insert(req) ;
                }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
                    log.error("患者随访任务重复插入异常", e);
                    return;
                }

                taskHistoryId = req.getId() ;

            }

        }else {

            if ( PatientTaskStatusEnum.FINISHED.getIndex().equals(patientTaskHistory.getTaskStatus())
            || PatientTaskStatusEnum.HASEXPIRED.getIndex().equals(patientTaskHistory.getTaskStatus())
            || PatientTaskStatusEnum.TERMINATED.getIndex().equals(patientTaskHistory.getTaskStatus()) ) {

                return ;
            }

            taskHistoryId = patientTaskHistory.getId() ;

        }

        List<TaskEvent> eventList = itemTask.getEvents();
        if ( null==eventList || eventList.isEmpty() ) {
            return ;
        }

        FollowUp followUp = followUpMapper.getById(patientItem.getFollowUpId());
        if ( null==followUp || YesNoEnum.NO.getCode().equals(followUp.getIsEnable()) || YesNoEnum.YES.getCode().equals(followUp.getIsDelete()) ) {
            return ;
        }

        PatientFollowUpRecord patientFollowUpRecordReq = new PatientFollowUpRecord() ;
        patientFollowUpRecordReq.setHospitalId(patientItem.getHospitalId()) ;
        patientFollowUpRecordReq.setUserId(patientItem.getUserId()) ;
        patientFollowUpRecordReq.setPatientId(patientItem.getPatientId()) ;
        patientFollowUpRecordReq.setFollowUpId(followUp.getId()) ;
        patientFollowUpRecordReq.setJoinStatusList(Arrays.asList(FollowUpJoinStatusEnum.INVITING.getIndex(), FollowUpJoinStatusEnum.FOLLOWUPING.getIndex())) ;
        PatientFollowUpRecord patientFollowUpRecord = patientFollowUpRecordMapper.selectPatientFollowUpByFollowUpUser(patientFollowUpRecordReq);
        if ( null==patientFollowUpRecord || null==patientFollowUpRecord.getId() ) {
            return ;
        }

        for (TaskEvent event : eventList) {
            PatientTaskEventHistory patientTaskEventHistory = new PatientTaskEventHistory();
            patientTaskEventHistory.setFollowUpId(patientItem.getFollowUpId()) ;
            patientTaskEventHistory.setFollowUpRecordId(patientFollowUpRecord.getId()) ;
            patientTaskEventHistory.setItemRecordId(patientItem.getId()) ;
            patientTaskEventHistory.setTaskHistoryId(taskHistoryId);
            patientTaskEventHistory.setTaskId(itemTask.getId());
            patientTaskEventHistory.setEventId(event.getId());
            patientTaskEventHistory.setEventType(event.getEventType()) ;
            patientTaskEventHistory.setItemId(patientItem.getItemId());
            patientTaskEventHistory.setHospitalId(patientItem.getHospitalId());
            patientTaskEventHistory.setUserId(patientItem.getUserId());
            patientTaskEventHistory.setPatientId(patientItem.getPatientId());

            int count = patientTaskEventHistoryMapper.countPatientTaskEventHistory(patientTaskEventHistory);
            if (count>0) {
                continue ;
            }

            patientTaskEventHistory.setEvents(event);
            patientTaskEventHistory.setRemindTime(now);
            patientTaskEventHistory.setEventRemindStatus(PatientTaskStatusEnum.ONGOING.getIndex());
            patientTaskEventHistory.setEventFinishStatus(PatientTaskStatusEnum.ONGOING.getIndex());
            patientTaskEventHistory.setIsTerminateFuture(YesNoEnum.NO.getCode());
            patientTaskEventHistory.setCreateTime(now);
            patientTaskEventHistory.setUpdateTime(now);
            patientTaskEventHistory.setCreateBy(TASK) ;
            patientTaskEventHistory.setUpdateBy(TASK) ;
            try{
                patientTaskEventHistoryMapper.insert(patientTaskEventHistory) ;
            }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
                log.error("患者随访事件记录重复插入异常", e);
                continue;
            }

            if ( TaskEventTypeEnum.QUESTIONNAIRE.getIndex().equals(event.getEventType()) &&  null!=event.getSendId() ) {
                this.eventSendQuestion(patientTaskEventHistory, event.getSendId(), now) ;
            }

            imWxMessageService.sendImWxMessage(followUp, patientItem, event, patientFollowUpRecord , patientTaskEventHistory.getId(), Boolean.FALSE) ;

        }

    }

    private void eventSendQuestion(PatientTaskEventHistory patientTaskEventHistory, Long questionId, Date now){
        PatientQuestionRecord patientQuestionRecord = new PatientQuestionRecord();
        patientQuestionRecord.setHospitalId(patientTaskEventHistory.getHospitalId()) ;
        patientQuestionRecord.setUserId(patientTaskEventHistory.getUserId()) ;
        patientQuestionRecord.setPatientId(patientTaskEventHistory.getPatientId());
        patientQuestionRecord.setQuestionId(questionId) ;
        patientQuestionRecord.setFollowUpId(patientTaskEventHistory.getFollowUpId()) ;
        patientQuestionRecord.setPatientEventId(patientTaskEventHistory.getId()) ;
        patientQuestionRecord.setFillStatus(FillQuestionStatusEnum.INVITING.getIndex()) ;
        patientQuestionRecord.setCreateBy(TASK) ;
        patientQuestionRecord.setUpdateBy(TASK);
        patientQuestionRecord.setCreateTime(now) ;
        patientQuestionRecord.setUpdateTime(now) ;
        patientQuestionRecord.setIsDelete(YesNoEnum.NO.getCode()) ;

        try{
            patientQuestionRecordMapper.insert(patientQuestionRecord) ;
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            log.error("患者随访问卷记录重复插入异常", e);
        }
    }



}
