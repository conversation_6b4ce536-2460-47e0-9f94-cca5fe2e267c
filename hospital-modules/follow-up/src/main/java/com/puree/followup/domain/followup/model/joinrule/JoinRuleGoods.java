package com.puree.followup.domain.followup.model.joinrule;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.shop.api.model.BusShopLabel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 入组方式-商品
 */

@Data
public class JoinRuleGoods {

    /**
     * 商品id
     */
    private Long id;

    /**
     * 商品名称
     */
    private String name;
    /**
     * 商品标题
     */
    private String title;
    /**
     * 商品副标题
     */
    private String subtitle;
    /**
     * 商品图
     */
    private String img;
    /**
     * 商品视频
     */
    private String video;
    /**
     * 商品详情
     */
    private String detail;
    /**
     * 商品分类
     */
    private Long typeId;
    /**
     * 商品品牌
     */
    private Long brandId;
    /**
     * 商品规格
     */
    private String specification;
    /**
     * 商品销售价
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal sellingPrice;

    /**
     * 商品原价
     */
    private BigDecimal originalPrice;

    /**
     * 商品成本价
     */
    private BigDecimal costPrice;

    /**
     * 建议零售价
     */
    private BigDecimal suggestSellingPrice;

    /**
     * 商品库存
     */
    private Integer stock;
    /**
     * 商品状态 0-下架 1-上架
     */
    private Integer status;
    /**
     * 商品来源（院内为空）
     */
    private Long sourceId;
    /**
     * 父商品id（医院关联配送企业商品id）
     */
    private Long parentId;

    /**
     * 是否热门 0否 1是否
     */
    private String hotFlag;
    /**
     * 排序
     */
    private Integer orderNum;
    /**
     * 是否是c位标签 0否 1是
     */
    private Integer isCBit;
    /**
     * 分类名称
     */
    @TableField(exist = false)
    private String typeName;
    /**
     * 品牌名称
     */
    @TableField(exist = false)
    private String brandName;
    /**
     * 发货渠道
     */
    @TableField(exist = false)
    private String sourceName;

    /**
     * 标签id
     */
    @TableField(exist = false)
    private Long labelId;

    /**
     * 标签列表
     */
    @TableField(exist = false)
    private List<BusShopLabel> labelList;

    /**
     * 标签列表
     */
    @TableField(exist = false)
    private List<BusShopLabel> busShopLabels;

    /**
     * 全部标签
     */
    @TableField(exist = false)
    private String allLabels;


}
