package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访-分项
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项名称
     */
    private String name;

    /**
     * 分项排序
     */
    private Integer sortNum;

    /**
     * 分项执行时间的类型：0.全周期 1.加入随访后第X至Y天 2.通过事件加入后至Y天
     */
    private Integer executeType;

    /**
     * 分项执行时间：加入随访第X天开始
     */
    private Integer beginDay;

    /**
     * 分项执行时间：加入随访至Y天结束，或者表示通过事件加入至Y天结束
     */
    private Integer endDay;

    /**
     * 提醒时间的类型：0.统一提醒 1.单独提醒
     */
    private Integer remindTimeType;

    /**
     * 提醒时间，格式为：HH:mm
     */
    private String remindTime;

    /**
     * 分项任务执行周期的类型：0.自定义 1.周期循环 2.固定周循环
     */
    private Integer intervalType;

//    /**
//     * 分项任务(包括执行事件)
//     */
//    @TableField(typeHandler = JsonTypeHandler.class)
//    private List<ItemTask> tasks;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

    /**
     * 分项开始时间
     */
    private Date beginDate;

    /**
     * 分项结束
     */
    private Date endDate;

    /**
     * 随访记录id
     */
    private Long followUpRecordId;

    /**
     * 分项执行时间的类型列表
     */
    private List<Integer> executeTypes;

}