package com.puree.followup;

import com.puree.hospital.common.security.annotation.EnableCustomConfig;
import com.puree.hospital.common.security.annotation.EnableHospitalFeignClients;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableCustomConfig
@EnableHospitalFeignClients
@EnableAsync
@SpringBootApplication(scanBasePackages = {"com.puree.followup", "com.puree.hospital.common.notification"})
public class FollowUpApplication {
    public static void main(String[] args) {
        System.setProperty("druid.mysql.usePingMethod", "false");
        SpringApplication.run(FollowUpApplication.class, args);
        System.out.println("智能随访模块启动成功");
    }
}
