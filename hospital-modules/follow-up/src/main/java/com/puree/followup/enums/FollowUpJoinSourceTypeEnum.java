package com.puree.followup.enums;

import com.puree.hospital.common.core.enums.ImTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/6/21 17:55
 * @Description  患者入组的来源：default.默认入组  diagnosis.诊断入组  pushData.数据推送入组 goods.商品入组 drug.药品入组 servicePackage.服务包入组 healthRecord.健康档案入组 doctor.所属医生入组
 */
public enum FollowUpJoinSourceTypeEnum {

    DEFAULT(0, "default", "默认入组", ""),
    DIAGNOSIS(1, "diagnosis", "诊断入组", "处方诊断:"),
    PUSH_DATA(2, "pushData", "数据推送入组", "体检指标入组:"),
    GOODS(3, "goods", "商品入组", "购买商品:"),
    DRUGS(4, "drug", "药品入组", "购买药品:"),
    SERVICE_PACKAGE(5, "servicePackage", "服务包入组", "购买服务包:"),
    HEALTH_RECORD(6, "healthRecord", "健康档案入组", ""),
    DOCTOR(7, "doctor", "所属医生入组", "数据推送");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    @Getter
    @Setter
    private String reason;


    FollowUpJoinSourceTypeEnum(Integer index, String name, String desc, String reason) {
        this.index = index;
        this.name = name;
        this.desc = desc;
        this.reason = reason;
    }

    public static FollowUpJoinSourceTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpJoinSourceTypeEnum[] metaArr = FollowUpJoinSourceTypeEnum.values();
        for (FollowUpJoinSourceTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpJoinSourceTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpJoinSourceTypeEnum[] metaArr = FollowUpJoinSourceTypeEnum.values();
        for (FollowUpJoinSourceTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

    /**
    * 服务包 入组 ： 多人群组

    * 处方诊断  入组 ： 私聊 (之前是单人群组(提前判断是否有群组，需要创建), 后来需求改了)

    * 买药品 入组 ：  私聊

    * 买商品  入组 ： 私聊
    *
    */
    public static Integer findImChatType(Integer index) {
        if (Arrays.asList(DEFAULT.index, PUSH_DATA.index, GOODS.index, DRUGS.index, HEALTH_RECORD.index, DOCTOR.index, DIAGNOSIS.index).contains(index)) {
            return ImTypeEnum.ONE_TO_ONE.getIndex() ;
        }
        if (Arrays.asList(SERVICE_PACKAGE.index).contains(index)) {
            return ImTypeEnum.MULTIPLE_GROUP.getIndex() ;
        }
        return  null ;
    }


}
