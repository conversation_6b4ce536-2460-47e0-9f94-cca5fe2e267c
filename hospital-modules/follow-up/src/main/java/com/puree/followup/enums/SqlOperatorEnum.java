package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  sql操作符
 */
public enum SqlOperatorEnum {

    OR(0, " or ", "或"),
    AND(1, " and ", "与");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    SqlOperatorEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static SqlOperatorEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        SqlOperatorEnum[] metaArr = SqlOperatorEnum.values();
        for (SqlOperatorEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static SqlOperatorEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        SqlOperatorEnum[] metaArr = SqlOperatorEnum.values();
        for (SqlOperatorEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
