package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者随访记录表 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientFollowUpRecordVO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 入组类型：autoJoinIn:自动添加 autoInvite:自动邀请 manualAdd:手动添加 manualInvite:手动邀请
     */
    private String joinType;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inviteTime;

    /**
     * 入组/接受邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    /**
     * 终止/完成/拒绝邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 终止人类型：1.医院后台 2.医生
     */
    private Integer terminatorType;

    /**
     * 终止人
     */
    private String terminator;

    /**
     * 医生id
     */
    private Long doctorId;

    /**
     * 科室id
     */
    private Long deptId;

    /**
     * 患者随访状态：0.邀请中 1.随访中 2.已完成 3.提前终止 4.已拒绝
     */
    private Integer joinStatus;

    /**
     * 入组原因
     */
    private String joinReason;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;


}