package com.puree.followup.queue.consumer;

import com.alibaba.fastjson.JSON;
import com.puree.followup.admin.drools.service.DroolsActuatorService;
import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.service.IFollowUpAsyncService;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.admin.medical.service.ReportRecordService;
import com.puree.followup.domain.event.MedicalReportUploadEvent;
import com.puree.followup.domain.followup.dto.FollowUpInsertDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.FollowUpJoinRule;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.query.PatientFollowUpRecordQuery;
import com.puree.followup.domain.medical.dto.ReportFollowUpJoinInDTO;
import com.puree.followup.domain.medical.model.ReportRecord;
import com.puree.followup.enums.*;
import com.puree.hospital.app.api.RemotePatientFamilyService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 体检报告入组 消费者
 *
 * @ClassName: MedicalReportConsumer
 * @Date 2024/7/8 19:59
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
@Slf4j
public class MedicalReportFollowUpJoinInConsumer extends RedisStreamConsumer<MedicalReportUploadEvent> {

    @Autowired
    private PatientFollowUpRecordMapper patientFollowUpRecordMapper;

    @Autowired
    private FollowUpMapper followUpMapper;

    @Autowired
    private DroolsActuatorService droolsActuatorService;

    @Autowired
    private IFollowUpService followUpService;

    @Resource
    private IFollowUpAsyncService followUpAsyncService;

    @Resource
    private ReportRecordService reportRecordService;

    @Resource
    private RemotePatientFamilyService remotePatientFamilyService;

    /**
     * 患者体检报告 消费者方法
     *
     * @param message 消息内容
     */
    @Override
    @Transactional
    public void onMessage(RedisMessage<MedicalReportUploadEvent> message) {
        try{
            MedicalReportUploadEvent event = message.getBody();
            log.info("体检报告上传随访入组监听事件：{}", event.getReportRecordId());
            if (Objects.isNull(event)) {
                return;
            }
            Long hospitalId = event.getHospitalId();
            ReportRecord reportRecord = reportRecordService.getById(event.getReportRecordId());
            if (reportRecord == null) {
                log.warn("报告记录不存在，报告上传id：{}", event.getReportRecordId());
                return;
            }
            BusPatientFamilyVo userVO = remotePatientFamilyService.queryPatientInfo(reportRecord.getPatientIdNumber(), hospitalId).getData();
            //就诊人被删除后，不入组随访
            if(userVO == null || YesNoEnum.NO.getCode().equals(userVO.getVisible())){
                return;
            }

            ExamReportInfoDTO dto = JSON.parseObject(reportRecord.getReportJson(), ExamReportInfoDTO.class);
            ReportFollowUpJoinInDTO followUpJoinInDTO = new ReportFollowUpJoinInDTO();
            followUpJoinInDTO.setExamReportInfoDTO(dto);
            followUpJoinInDTO.setHospitalId(event.getHospitalId());
            followUpJoinInDTO.setUserVO(userVO);

            log.info("患者体检报告消费者：reportRecordId：{}，hospitalId：{}", event.getReportRecordId(), event.getHospitalId());
            //触发智能执行
            followUpAsyncService.reportSmartExecutor(followUpJoinInDTO);



            userVO.setHospitalId(hospitalId);

            //检验是否存在开启了数据推送入组的随访，并且查出对应的随访
            FollowUpJoinRule joinRule = new FollowUpJoinRule();
            joinRule.setHospitalId(hospitalId);
            List<FollowUp> followUps = followUpMapper.getListByJoinRule(joinRule);

            for (FollowUp followUp : followUps) {

                //未启用或未发布的随访不能自动入组
                if (followUp.getIsEnable().equals(YesNoEnum.NO.getCode())
                        || followUp.getIsPublish().equals(YesNoEnum.NO.getCode())) {
                    continue;
                }

                List<FollowUpJoinRule> rules = followUp.getRules();
                //数据推送方式
                FollowUpJoinRule pushDataRule = null;
                //健康档案方式
                FollowUpJoinRule healthRecordRule = null;

                for (FollowUpJoinRule rule : rules) {
                    if (rule.getJoinType().equals(FollowUpJoinRuleTypeEnum.PUSH_DATA.getIndex()) && rule.getRuleId() != null) {
                        pushDataRule = rule;
                    }
                    if (rule.getJoinType().equals(FollowUpJoinRuleTypeEnum.HEALTH_RECORD.getIndex()) && rule.getRuleId() != null) {
                        healthRecordRule = rule;
                    }
                }

                //校验对应随访该用户是否入组
                if (userVO == null) {
                    continue;
                }
                //查询本医院正在进行的随访
                PatientFollowUpRecordQuery recordQuery = new PatientFollowUpRecordQuery();
                recordQuery.setFollowUpId(followUp.getId());
                recordQuery.setHospitalId(hospitalId);
                recordQuery.setIsDelete(YesNoEnum.NO.getCode());
                recordQuery.setStatusList(Arrays.asList(FollowUpJoinStatusEnum.INVITING.getIndex(), FollowUpJoinStatusEnum.FOLLOWUPING.getIndex()));
                recordQuery.setPatientId(userVO.getId());
                recordQuery.setUserId(userVO.getPatientId());
                List<PatientFollowUpRecord> records = patientFollowUpRecordMapper.getList(recordQuery);
                if (CollectionUtils.isNotEmpty(records)) {
                    continue;
                }

                if (pushDataRule != null) {
                    //给我对应随访的规则的ID
                    //更多入组方式
                    Boolean b = droolsActuatorService.joinRuleActuator(pushDataRule.getRuleId(), dto);

                    if (b) {

                        if (healthRecordRule != null) {
                            //更多入组方式
                            b = droolsActuatorService.joinRuleActuator(healthRecordRule.getRuleId(), userVO);
                            if (!b) {
                                continue;
                            }
                        }
                        //加入随访
                        FollowUpInsertDTO insertDTO = new FollowUpInsertDTO();
                        insertDTO.setFollowUpId(followUp.getId());
                        insertDTO.setHospitalId(hospitalId);
                        //患者id
                        insertDTO.setUserId(userVO.getPatientId());
                        //就诊人id
                        insertDTO.setPatientId(userVO.getId());
                        insertDTO.setJoinType(followUp.getJoinType());
                        insertDTO.setJoinStatus(FollowUpJoinTypeEnum.getByIndex(followUp.getJoinType()).equals(FollowUpJoinTypeEnum.AUTO) ? FollowUpJoinStatusEnum.FOLLOWUPING.getIndex() : FollowUpJoinStatusEnum.INVITING.getIndex());
                        insertDTO.setJoinReason(FollowUpJoinReasonTypeEnum.AUTO.getDesc() + FollowUpJoinSourceTypeEnum.PUSH_DATA.getReason());
                        //构建rediskey
                        insertDTO.setRedisKey("joinin:" + hospitalId + ":" + followUp.getId() + ":" + userVO.getPatientId() + ":" + userVO.getId());
                        Boolean success = followUpService.patientJoinIn(insertDTO).getIsSuccess();
                        log.info("加入随访结果：{}", success);
                    }
                }
            }
        } catch (Exception ex) {
            log.error("MedicalReportFollowUpJoinInConsumer failed : ", ex);
        }
    }

}
