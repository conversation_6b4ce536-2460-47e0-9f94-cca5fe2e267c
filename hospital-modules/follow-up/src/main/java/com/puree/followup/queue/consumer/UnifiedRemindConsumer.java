package com.puree.followup.queue.consumer;

import com.puree.followup.admin.followup.mapper.FollowUpItemTaskMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskHistoryMapper;
import com.puree.followup.admin.util.JudgeExecuteDateUtil;
import com.puree.followup.domain.followup.model.FollowUpItemTask;
import com.puree.followup.domain.followup.model.PatientFollowUpItemRecord;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.PatientTaskHistory;
import com.puree.followup.enums.FollowUpJoinStatusEnum;
import com.puree.followup.enums.PatientTaskStatusEnum;
import com.puree.followup.service.ImWxMessageService;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @ClassName: UnifiedRemindConsumer
 * @Date 2024/5/21 10:59
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public class UnifiedRemindConsumer extends RedisStreamConsumer<PatientFollowUpItemRecord> {

    private Logger logger = LoggerFactory.getLogger(UnifiedRemindConsumer.class);

    @Autowired
    private PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper ;
    @Autowired
    private FollowUpItemTaskMapper followUpItemTaskMapper ;
    @Autowired
    private PatientFollowUpRecordMapper patientFollowUpRecordMapper ;
    @Autowired
    private ImWxMessageService imWxMessageService ;
    @Autowired
    private PatientTaskHistoryMapper patientTaskHistoryMapper ;



    @Override
    public void onMessage(RedisMessage<PatientFollowUpItemRecord> message)  {

        try{

            PatientFollowUpItemRecord patientItemRecord = message.getBody();
            doUnifiedRemind(patientItemRecord) ;

        }catch (Exception ex) {
            logger.error("UnifiedRemindConsumer failed : " , ex);
        }

    }

    private void doUnifiedRemind(PatientFollowUpItemRecord patientItemRecord) {

        List<PatientFollowUpItemRecord> patientItemRecordList = patientFollowUpItemRecordMapper.selectPatientItemByHospitalUser(patientItemRecord);
        if ( null==patientItemRecordList || patientItemRecordList.isEmpty() ) {
            return ;
        }

        label1:
        for (PatientFollowUpItemRecord patientFollowUpItemRecord : patientItemRecordList) {

            FollowUpItemTask req = new FollowUpItemTask() ;
            req.setHospitalId(patientFollowUpItemRecord.getHospitalId()) ;
            req.setFollowUpId(patientFollowUpItemRecord.getFollowUpId()) ;
            req.setItemId(patientFollowUpItemRecord.getItemId()) ;
            req.setIsDelete(0) ;
            List<FollowUpItemTask> followUpItemTaskList = followUpItemTaskMapper.getList(req);

            if ( null==followUpItemTaskList || followUpItemTaskList.isEmpty() ) {
                continue ;
            }

            for ( FollowUpItemTask followUpItemTask : followUpItemTaskList ) {

                boolean flag = JudgeExecuteDateUtil.isExecuteDate(followUpItemTask.getIntervalType(), followUpItemTask.getIntervals(), followUpItemTask.getWeekDays(), patientItemRecord.getDayOfWeek(),
                        patientItemRecord.getNow() , patientFollowUpItemRecord.getBeginDay(), patientFollowUpItemRecord.getFirstDayOfJoinWeek());

                if (!flag) {
                    continue ;
                }

                PatientTaskHistory taskHistory = new PatientTaskHistory();
                taskHistory.setHospitalId(patientFollowUpItemRecord.getHospitalId()) ;
                taskHistory.setFollowUpId(patientFollowUpItemRecord.getFollowUpId()) ;
                taskHistory.setItemId(patientFollowUpItemRecord.getItemId()) ;
                taskHistory.setTaskId(followUpItemTask.getId()) ;
                taskHistory.setFollowUpRecordId(patientFollowUpItemRecord.getFollowUpRecordId()) ;
                taskHistory.setItemRecordId(patientFollowUpItemRecord.getId()) ;
                taskHistory.setUserId(patientFollowUpItemRecord.getUserId()) ;
                taskHistory.setPatientId(patientFollowUpItemRecord.getPatientId()) ;
                taskHistory.setBeginDay(patientItemRecord.getNow()) ;

                PatientTaskHistory patientTaskHistory = patientTaskHistoryMapper.selectTodayPatientTaskHistory(taskHistory);
                if ( null!=patientTaskHistory ) {
                    if ( PatientTaskStatusEnum.FINISHED.getIndex().equals(patientTaskHistory.getTaskStatus())
                    || PatientTaskStatusEnum.HASEXPIRED.getIndex().equals(patientTaskHistory.getTaskStatus())
                    || PatientTaskStatusEnum.TERMINATED.getIndex().equals(patientTaskHistory.getTaskStatus()) ) {

                        continue ;

                    }

                }

                PatientFollowUpRecord patientFollowUpRecord = patientFollowUpRecordMapper.getById(patientFollowUpItemRecord.getFollowUpRecordId());

                if ( FollowUpJoinStatusEnum.FINISHED.getIndex().equals(patientFollowUpRecord.getJoinStatus())
                        || FollowUpJoinStatusEnum.TERMINATED.getIndex().equals(patientFollowUpRecord.getJoinStatus())
                        || FollowUpJoinStatusEnum.REFUSED.getIndex().equals(patientFollowUpRecord.getJoinStatus()) ) {

                    continue ;
                }

                logger.info("start to unify remind-patient task history is : {}", patientTaskHistory);
                imWxMessageService.sendUnifiedRemindTodoMsg(followUpItemTask.getHospitalId(),
                        patientFollowUpRecord.getPhoneNum(),
                        patientFollowUpRecord.getUserId(),
                        patientFollowUpRecord.getPatientId(),
                        patientFollowUpItemRecord.getFollowUpId(),
                        patientFollowUpItemRecord.getFollowUpRecordId());
                break label1 ;

            }

        }

    }


}
