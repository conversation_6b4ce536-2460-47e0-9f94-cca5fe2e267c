package com.puree.followup.domain.followup.dto;

import com.puree.followup.domain.followup.model.SmartExecutorCondition;
import com.puree.followup.domain.followup.model.TaskEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 智能执行 DTO
 * <AUTHOR>
 * @date 2024-07-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpSmartExecutorDTO {

    /**
     * 智能执行id
     */
    private Long id;

    /**
     * 智能执行开关：enable:满足任一 all:满足全部
     */
    private String executorSwitch;

    /**
     * 智能执行的排序
     */
    private Integer sortNum;

    /**
     * 数据源的类型：questionnaire.问卷数据 healthRecord.健康档案
     */
    private String dataSourceType;

    /**
     * 问卷id
     */
    private Long questionnaireId;

    /**
     * 问卷名称
     */
    private String questionnaireName;

    /**
     * 健康档案的时效性：x天内的患者最新指标
     */
    private Integer validDays;

    /**
     * 预警启用标识：false.禁用 true.启用
     */
    private Boolean warnSwitch = false;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 执行事件列表
     */
    private List<TaskEvent> events;

    /**
     * 条件列表
     */
    private List<SmartExecutorCondition> conditions;

    /**
     * 分项编辑时的行为：add、update、del
     */
    private String action;

}