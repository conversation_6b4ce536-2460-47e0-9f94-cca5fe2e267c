package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  提醒时间的类型：0.统一提醒 1.单独提醒
 */
public enum FollowUpRemindTimeTypeEnum {

    UNIFORM(0, "统一提醒"),
    ALONE(1, "单独提醒");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    FollowUpRemindTimeTypeEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static FollowUpRemindTimeTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpRemindTimeTypeEnum[] metaArr = FollowUpRemindTimeTypeEnum.values();
        for (FollowUpRemindTimeTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpRemindTimeTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpRemindTimeTypeEnum[] metaArr = FollowUpRemindTimeTypeEnum.values();
        for (FollowUpRemindTimeTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
