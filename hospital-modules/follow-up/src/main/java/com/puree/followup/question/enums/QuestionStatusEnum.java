package com.puree.followup.question.enums;

import cn.hutool.core.util.StrUtil;

/**
 * @ClassName: QuestionStatusEnum
 * @Date 2024/6/18 18:03
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
public enum QuestionStatusEnum {

    CLOSE(0, "close", "禁用") ,
    ENABLE(1, "enable", "关闭") ,
    PUBLISH(2, "publish", "发布") ,

    ;

    private Integer index;
    private String name;
    private String desc;

    QuestionStatusEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static QuestionStatusEnum getByName(String name) {
        if (StrUtil.isBlank(name) ) {
            return null;
        }
        QuestionStatusEnum[] metaArr = QuestionStatusEnum.values();
        for (QuestionStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static QuestionStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        QuestionStatusEnum[] metaArr = QuestionStatusEnum.values();
        for (QuestionStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }


}
