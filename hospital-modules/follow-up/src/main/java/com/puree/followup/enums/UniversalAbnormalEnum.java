package com.puree.followup.enums;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName UniversalAbnormalEnum
 * <AUTHOR>
 * @Description 通用检查异常枚举
 * @Date 2024/3/27 14:28
 * @Version 1.0
 */
@Getter
public enum UniversalAbnormalEnum {
    NORMAL(0, "normal", "正常", "正常"),
    HIGH(1, "high", "偏高", "偏高"),
    LOW(2, "low", "偏低", "偏低"),
    OTHER_ABNORMALITIES(3, "other_abnormalities", "其它异常情况", "其它异常情况"),
    ;

    /**
     * @Param code
     * @Return com.puree.followup.enums.UniversalAbnormalEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/3/31 16:16
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static UniversalAbnormalEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(UniversalAbnormalEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
    }

    UniversalAbnormalEnum(Integer number, String code, String name, String desc) {
        this.number = number;
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    /**
     * 编号
     */
    private Integer number;
    /**
     * 代码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String desc;
}
