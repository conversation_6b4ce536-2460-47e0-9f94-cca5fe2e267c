package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: ChooseQuestion
 * @Date 2024/6/5 14:46
 * <AUTHOR> jian
 * @Description: 问卷选择题
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ChooseQuestion {

    /**
     * 选择题内容
     */
    private String content ;

    /**
     * 选择题分数
     */
    private BigDecimal score ;

    /**
     * 是否被选
     */
    private Integer isChosen ;

    /**
     * 当该选项被选了，设置需要显示的题目id
     */
    private List<Long> visibleIdList ;



}
