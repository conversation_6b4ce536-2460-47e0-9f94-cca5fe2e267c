package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  随访周期类型(到期时间)：0.长期 1.加入随访X天后结束
 */
public enum FollowUpExpireTypeEnum {

    LONG_TERM(0, "longTerm", "长期"),
    JOIN_IN(1, "joinIn", "加入随访X天后结束");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    FollowUpExpireTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    @Getter
    @Setter
    private String desc;

    public static FollowUpExpireTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpExpireTypeEnum[] metaArr = FollowUpExpireTypeEnum.values();
        for (FollowUpExpireTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpExpireTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpExpireTypeEnum[] metaArr = FollowUpExpireTypeEnum.values();
        for (FollowUpExpireTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
