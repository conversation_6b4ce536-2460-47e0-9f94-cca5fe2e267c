package com.puree.followup.admin.classification.controller;

import com.puree.followup.admin.classification.service.IClassificationService;
import com.puree.followup.domain.classification.dto.ClassificationDTO;
import com.puree.followup.domain.classification.vo.ClassificationVO;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.common.api.domain.Paging;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.StringUtils;
import com.puree.hospital.common.core.web.controller.BaseController;
import com.puree.hospital.operate.api.annotation.Log;
import com.puree.hospital.operate.api.model.enums.BusinessType;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/7 16:10
 * @description 医院后台 随访 控制器
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/hospital-admin/classification")
public class ClassificationHospitalController extends BaseController {

    private final IClassificationService classificationService;

    /**
     * 医院后台 分类列表
     *
     * @param dto 查询条件
     * @return 分类列表
     */
//    @PreAuthorize(hasPermi = "follow-up:classification:hospital:page")
    @GetMapping
    @Log(title = "医院后台-分页获取分类列表", businessType = BusinessType.QUERY)
    public Paging<List<ClassificationVO>> hospitalPageList(ClassificationDTO dto) {
        return PageUtil.buildPage(classificationService.hospitalPageList(dto));
    }

    /**
     * 医院后台 根据分类id查询分类详情
     *
     * @param id  分类id
     * @return 分类详情
     */
//    @PreAuthorize(hasPermi = "follow-up:classification:hospital:info")
    @GetMapping("/{id}")
    @Log(title = "医院后台-分类详情", businessType = BusinessType.QUERY)
    public AjaxResult<ClassificationVO> getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(classificationService.getInfo(id));
    }

    /**
     * 医院后台 添加分类
     *
     * @param dto 分类详情
     * @return
     */
//    @PreAuthorize(hasPermi = "follow-up:classification:hospital:add")
    @Log(title = "医院后台-添加分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Boolean> add(@Valid @RequestBody ClassificationDTO dto) {
        if(StringUtils.isEmpty(dto.getName())){
            throw new ServiceException("请填写分类名称");
        }
        if(dto.getName().length() > 30){
            throw new ServiceException("分类名称过长");
        }
        return AjaxResult.success(classificationService.hospitalAdd(dto));
    }

    /**
     * 医院后台 修改分类
     *
     * @param dto 分类详情
     * @return
     */
//    @PreAuthorize(hasPermi = "follow-up:classification:hospital:edit")
    @Log(title = "医院后台-修改分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Boolean> edit(@Valid @RequestBody ClassificationDTO dto) {
        if(StringUtils.isEmpty(dto.getName())){
            throw new ServiceException("请填写分类名称");
        }
        if(dto.getName().length() > 20){
            throw new ServiceException("分类名称过长");
        }
        return AjaxResult.success(classificationService.hospitalEdit(dto));
    }

    /**
     * 医院后台 根据分类id删除分类
     *
     * @param id        分类id
     * @param revision  乐观锁
     * @return
     */
//    @PreAuthorize(hasPermi = "follow-up:classification:hospital:remove")
    @Log(title = "医院后台-删除分类", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult<Boolean> remove(@PathVariable("id") Long id,
                                      @RequestParam("revision") Integer revision) {
        return AjaxResult.success(classificationService.hospitalRemove(id, revision));
    }


}