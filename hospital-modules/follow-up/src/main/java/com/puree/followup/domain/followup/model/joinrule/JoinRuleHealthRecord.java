package com.puree.followup.domain.followup.model.joinrule;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.puree.followup.domain.drools.HealthRecordDTO;
import com.puree.followup.domain.drools.constant.ComparisonOperatorEnum;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import lombok.Data;

/**
 * @ClassName JoinRuleFive
 * <AUTHOR>
 * @Description 入组方式5 更多条件
 * @Date 2024/4/16 15:57
 * @Version 1.0
 */
@Data
public class JoinRuleHealthRecord {
    /**
     * 第一级选项
     */
    private String firstLevelOption;
    /**
     * 第二级选项
     */
    private String secondLevelOptions;
    /**
     * 比较运算符
     */
    private ComparisonOperatorEnum comparisonOperatorEnum;
    /**
     * 预期值
     */
    private String expectedValue;
    /**
     * 额外的预期值，用于区间内的比较
     */
    private String additionalExpectedValue;
    /**
     * 规则 class
     */
    @JsonIgnore
    private Class<?> ruleClass = HealthRecordDTO.class;
    /**
     * 前端设置条件的标识字段
     */
    private String settingType;
}
