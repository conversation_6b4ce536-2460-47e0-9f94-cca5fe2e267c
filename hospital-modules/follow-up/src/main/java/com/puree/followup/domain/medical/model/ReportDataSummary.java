package com.puree.followup.domain.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.domain.medical.constant.RecordJsonTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportDataSummary
 * <AUTHOR>
 * @Description 体检报告数据汇总表
 * @Date 2024/5/13 18:02
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportDataSummary {

    /**
     * 主键
     */
    private Long  id ;
    /**
     * 名称;（如果为小结，则有具体项目名称，如果为指标则固定名称为指标）
     */
    private String itemName ;
    /**
     * json类型;INSPECT_SUMMARY 检验小结, INSPECT_INDICATORS 检验指标,EXAM_SUMMARY 检查小结,EXAM_INDICATORS 检查指标
     */
    private RecordJsonTypeEnum jsonType ;
    /**
     * json串
     */
    private String json ;
    /**
     * 医院ID
     */
    private Long hospitalId ;
    /**
     * 患者ID
     */
    private Long patientId ;
    /**
     * 患者身份证号
     */
    private String patientIdNumber ;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime ;
}
