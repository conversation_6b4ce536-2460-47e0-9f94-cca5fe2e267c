package com.puree.followup.enums;

import cn.hutool.core.util.StrUtil;

/**
 * @ClassName: ImageTextEnum
 * @Date 2024/4/26 9:00
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum ImageTextEnum {

    TEXT(1, "文本") ,
    IMAGE(2, "图片")

    ;


    private Integer index ;
    private String desc ;

    public Integer getIndex() {
        return index;
    }

    public String getDesc() {
        return desc;
    }

    ImageTextEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public static ImageTextEnum getPicTextEnumByIndex(Integer index) {
        if (null==index) {
            return null;
        }
        ImageTextEnum[] metaArr = ImageTextEnum.values();
        for (ImageTextEnum imageTextEnum : metaArr) {
            if (imageTextEnum.getIndex().equals(index)) {
                return imageTextEnum;
            }
        }
        return null;
    }


    public static ImageTextEnum getPicTextEnumByDesc(String desc) {
        if (StrUtil.isBlank(desc)) {
            return null;
        }
        ImageTextEnum[] metaArr = ImageTextEnum.values();
        for (ImageTextEnum imageTextEnum : metaArr) {
            if (imageTextEnum.getDesc().equals(desc)) {
                return imageTextEnum;
            }
        }
        return null;
    }


}
