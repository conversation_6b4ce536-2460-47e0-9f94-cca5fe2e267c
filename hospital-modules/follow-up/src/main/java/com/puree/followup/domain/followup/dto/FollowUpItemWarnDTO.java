package com.puree.followup.domain.followup.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.config.TaskEventTypeHandler;
import com.puree.followup.config.WarnConditionHandler;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.domain.followup.model.WarnCondition;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访预警
 * <AUTHOR>
 * @date 2024-08-14 14:29:33
 */
@Data
public class FollowUpItemWarnDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     *
     * 执行事件类型：none：无  message：发送消息  tutorial：发送患教   questionnaire：发送问卷  recommend：发送推荐  joinInNewItem：加入新分项
     *              terminateCurrentItem：终止本分项  joinInNewFollowUp：入组新随访  terminateCurrentFollowUp：终止本随访
     */
    private String eventType ;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 预警类型：questionnaire.问卷预警 indicator.指标预警 expire.超期预警
     */
    private String warnType;

    /**
     * 预警状态：undone.未处理 done.已处理
     */
    private String warnStatus;

    /**
     * 开始日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginDate;

    /**
     * 结束日期
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 乐观锁
     */
    private Integer revision;

}