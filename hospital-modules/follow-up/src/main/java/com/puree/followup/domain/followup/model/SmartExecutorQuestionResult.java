package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.SmartExecutorConditionTypeHandler;
import com.puree.followup.config.TaskEventTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访-分项-智能执行配置
 * <AUTHOR>
 * @date 2024-07-15 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class SmartExecutorQuestionResult implements Serializable {

    private static final long serialVersionUID = 1L;

//    /**
//     * 命中的记录条数
//     */
//    private Integer hitCount;
//
//    /**
//     * 总的数据记录条数
//     */
//    private Integer totalCount;


    /**
     * 题目id
     */
    private String id;

    /**
     * 随机数ID，只用于sql查询中做每条记录的唯一标识。
     * 为什么不用上面的题目id做唯一标识，因为智能执行设置条件时，可以重复选择题目
     */
    private String randomId;

    /**
     * 用户所填写的答案
     */
    private String answerContent;
}