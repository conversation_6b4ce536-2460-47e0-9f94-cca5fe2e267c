package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 患者分项概览 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientItemOverviewVO {

    /**
     * 分项id
     */
    private Long id;

    /**
     * 患者随访分项记录id
     */
    private Long itemRecordId;

    /**
     * 分项名称
     */
    private String name;

    /**
     * 分项排序
     */
    private Integer sortNum;

    /**
     * 任务列表
     */
    private List<PatientItemTaskOverviewVO> tasks;

    /**
     * 患者在该分项的开始时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date itemBeginDate;

    /**
     * 患者在该分项的结束时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date itemEndDate;

    /**
     * 是否应该介绍接下来的查询
     */
    private Boolean isEnd;

    /**
     * 本次查询结束的时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date queryEndDate;


    /**
     * 首次查询分项任务列表的最大时间区间
     */
    private Integer firstQueryItemTaskMaxDays;

}