package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName: PatientIndexReport
 * @Date 2024/6/5 14:47
 * <AUTHOR> jian
 * @Description: 问卷-患者指标, 患者报告
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientIndexReport {

    /**
     * 是否必填
     */
    private Integer isRequired ;

    private String key ;

    private String value ;

    private BigDecimal score ;

}
