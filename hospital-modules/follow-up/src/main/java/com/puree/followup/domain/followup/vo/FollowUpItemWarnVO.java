package com.puree.followup.domain.followup.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.config.TaskEventTypeHandler;
import com.puree.followup.config.WarnConditionHandler;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.domain.followup.model.WarnCondition;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访预警 VO
 * <AUTHOR>
 * @date 2024-08-14 14:29:33
 */
@Data
public class FollowUpItemWarnVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 分项名称
     */
    private String itemName;

    /**
     * 随访名称
     */
    private String followUpName;

    /**
     * 问卷名称
     */
    private String questionnaireName;

    /**
     * 患者名称
     */
    private String patientName;

    /**
     * 执行事件列表
     */
    private List<TaskEvent> events;

    /**
     * 触发的条件列表
     */
    private List<WarnCondition> conditions;

    /**
     * 预警类型：questionnaire.问卷预警 indicator.指标预警 expire.超期预警
     */
    private String warnType;

    /**
     * 预警状态：undone.未处理 done.已处理
     */
    private String warnStatus;

    /**
     * 时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 乐观锁
     */
    private Integer revision;

}