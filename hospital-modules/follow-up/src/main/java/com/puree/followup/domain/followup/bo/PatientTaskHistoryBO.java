package com.puree.followup.domain.followup.bo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.puree.followup.domain.followup.model.PatientTaskEventHistory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 患者随访任务执行历史 BO类
 * <AUTHOR>
 * @date 2024-05-08
 */
@Data
@EqualsAndHashCode
public class PatientTaskHistoryBO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 患者随访分项记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemRecordId;

    /**
     * 任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 任务开始时间
     */
    private Date beginDay;

    /**
     * 任务结束时间
     */
    private Date endDay;

    /**
     * 查询开始时间
     */
    private Date minQueryDay;

    /**
     * 查询结束时间
     */
    private Date maxQueryDay;

    /**
     * 任务提醒时间
     */
    private Date remindTime;

    /**
     * 任务实际完成时间
     */
    private Date taskFinishTime;

    /**
     * 任务提醒状态：0.未提醒 1.已提醒
     */
    private Integer taskRemindStatus;

    /**
     * 任务完成状态：0.未开始(即将开始，未来的) 1.进行中(未完成、待完成，今天的) 2.已完成 3.已过期 4.已终止
     */
    private Integer taskStatus;

    /**
     * 是否终止未来任务：0.否 1.是
     */
    private Integer isTerminateFuture;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0 ;

    /**
     * 智能执行标识
     */
    private Integer smartExecutorFlag ;

    /**
     * 要查询的任务完成状态列表
     */
    private List<Integer> taskStatusList;

    /**
     * 执行事件列表
     */
    private List<PatientTaskEventHistory> events;

    /**
     * 分项名称
     */
    private String itemName;

    /**
     * 事件类型
     */
    private Integer eventType;


}