package com.puree.followup.question.domain.dto;

import lombok.Data;
import lombok.ToString;

/**
 *  问卷事件业务数据
 * <AUTHOR>
 * @date 2025/3/21 11:18
 */
@Data
@ToString
public class QuestionEventBusinessDTO {


    /**
     * hospitalId
     */
    private Long hospitalId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 问卷id
     */
    private Long questionId;

    /**
     *  患者填写问卷记录表Id
     */
    private Long patientQuestionRecordId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 问卷内容是否过长
     */
    private boolean contentIsTooLong;
}
