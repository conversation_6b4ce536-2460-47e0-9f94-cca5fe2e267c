package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: AttachmentQuestion
 * @Date 2024/6/5 16:10
 * <AUTHOR> jian
 * @Description: 附件题目
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class AttachmentQuestion {

    /**
     * 附件类型 : 0-图片附件  1-视频附件  2-文件附件
     * AttachmentTypeEnum
     */
    private Integer attachmentType ;

    /**
     * 附件url
     */
    private List<String> fileUrlList ;

    /**
     *  得分
     */
    private BigDecimal score ;



}
