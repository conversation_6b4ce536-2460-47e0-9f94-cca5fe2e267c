package com.puree.followup.question.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * @ClassName: FindPatientToSendDTO
 * @Date 2024/6/21 10:39
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */

@Data
public class FindPatientToSendDTO {

    /**
     * 问卷主键id
     */
    private Long id ;

    private String snowId ;

    private Integer pageNum ;

    private Integer pageSize ;

    /**
     * 不发送问卷的患者id
     */
    private List<Long> excludeIdList;

}
