package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: FillQuestion
 * @Date 2024/6/5 16:04
 * <AUTHOR>
 * @Description: 单行文本  多行文本  多行填空
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FillQuestion {

    /**
     *  0-数字  1-手机号  2-邮箱  3-身份证号码  4-日期选择器  5-时间选择器  6-日期时间选择器
     *  FillFormatEnum
     */
    private Integer formatType ;

    /**
     * 单行文本 多行文本 多行填空
     */
    private List<String> textList ;

    /**
     * 得分
     */
    private BigDecimal score ;


}
