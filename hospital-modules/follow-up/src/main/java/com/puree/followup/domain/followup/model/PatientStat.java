package com.puree.followup.domain.followup.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 随访患者列表数据 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientStat {

    /**
     * 随访总患者
     */
    private Integer totalPatients;

    /**
     * 随访中患者数
     */
    private Integer ongoingPatients;

    /**
     * 随访已完成患者数
     */
    private Integer finishedPatients;

    /**
     * 提前终止的患者数
     */
    private Integer terminatedPatients;

    /**
     * 今日新增患者数
     */
    private Integer todayNewPatients;

    /**
     * 随访名称
     */
    private String followUpName;

    /**
     * 发布标识：false.否 true.是
     */
    private Boolean isPublish;

    /**
     * 创建时间
     */
    private Date createTime;




}