package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.JoinRuleTypeHandler;
import com.puree.followup.domain.followup.model.joinrule.JoinRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访表
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUp implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 随访名称
     */
    private String name;

    /**
     * 随访描述
     */
    private String desc;

    /**
     * 备注说明(仅内部可见)
     */
    private String remark;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 启用标识：0.否 1.是
     */
    private Integer isEnable;

    /**
     * 发布标识：0.否 1.是
     */
    private Integer isPublish;

    /**
     * 随访周期类型(到期时间)：0.长期 1.加入随访X天后结束
     */
    private Integer expireType;

    /**
     * 加入随访X天后结束
     */
    private Integer expireDay;

    /**
     * 可用科室：0.不限 1.自定义
     */
    private Integer departmentLimit;

    /**
     * 可用医生：0.不限 1.自定义
     */
    private Integer doctorLimit;

    /**
     * 随访任务过期的启用标识：0.否 1.是
     */
    private Integer taskExpireSwitch;

    /**
     * 随访任务过期X天后不能再完成
     */
    private Integer taskExpireDay;

    /**
     * 随访超期预警的启用标识：0.否 1.是
     */
    private Integer taskExpireWarnSwitch;

    /**
     * 随访任务连续X次过期进行超期预警
     */
    private Integer taskExpireTimes;

    /**
     * 入组类型：0.自动入组 1.需患者同意
     */
    private Integer joinType;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

    private List<FollowUpJoinRule> rules;

    @TableField(typeHandler = JoinRuleTypeHandler.class)
    private JoinRule rule;

    /**
     * 入组开关：0.关闭 1.开启(任一) 2.满足全部
     */
    private Integer joinSwitch;

    /**
     * 随访记录id
     */
    private Long followUpRecordId;

    /**
     * 小程序码
     */
    private String appletQrCodeAuto;

    /**
     * 小程序码
     */
    private String appletQrCodeAgree;
}