package com.puree.followup.domain.drools.builder;

import cn.hutool.core.text.CharSequenceUtil;
import com.puree.followup.domain.drools.constant.ComparisonOperatorEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName ComparisonOperatorBuilder
 * <AUTHOR>
 * @Description 比较生成
 * @Date 2024/4/11 10:35
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class ComparisonOperatorBuilder {

    /**
     * 预期变量名称，需要比较的变量名
     */
    private String expectedVariableName;
    /**
     * 比较操作符
     */
    private ComparisonOperatorEnum comparisonOperatorEnum;
    /**
     * 比较操作符外层拓展，用于嵌套查询
     */
    private String outerLayerExpansion;
    /**
     * 预期值
     */
    private String expectedValue;
    /**
     * 额外的预期值，用于区间内的比较
     */
    private String additionalExpectedValue;

    /**
     * @Param
     * @Return java.lang.String
     * @Description 生成 String 串
     * <AUTHOR>
     * @Date 2024/4/22 12:32
     **/
    public String builderComparisonOperatorString() {
        // 特殊处理：如果是空指针保护（NOT_NULL 且 expectedVariableName 为空格）
        if (comparisonOperatorEnum == ComparisonOperatorEnum.NOT_NULL &&
                (" ".equals(expectedVariableName) || expectedVariableName == null || expectedVariableName.trim().isEmpty())) {
            if (CharSequenceUtil.isNotEmpty(outerLayerExpansion)) {
                // 对于 /patientFamilyVo[{}] 这样的常量，去掉方括号部分
                String path = outerLayerExpansion;
                if (path.contains("[")) {
                    path = path.substring(0, path.indexOf('['));
                }
                return path;
            }
            return "";
        }

        // 获取比较操作符的模板
        String operatorTemplate = comparisonOperatorEnum.getValue();

        // 处理 outerLayerExpansion 中的占位符
        String result = "";
        if (CharSequenceUtil.isNotEmpty(outerLayerExpansion)) {
            //四值 区间操作 {变量} < {值1} && {变量} < {值2}
            if (CharSequenceUtil.isAllNotEmpty(expectedVariableName, expectedValue, additionalExpectedValue)) {
                // outerLayerExpansion: "/patientFamilyVo[{}]"
                // operatorTemplate: "{} < {} && {} < {}"
                // 需要组合成: "/patientFamilyVo[age < 18 && age < 65]"
                String condition = CharSequenceUtil.format(operatorTemplate, expectedValue, expectedVariableName, expectedVariableName, additionalExpectedValue);
                result = CharSequenceUtil.format(outerLayerExpansion, condition);
            }
            //双值 普通比较操作 {变量} {操作符} {值}
            else if (CharSequenceUtil.isAllNotEmpty(expectedVariableName, expectedValue)) {
                // outerLayerExpansion: "/patientFamilyVo[{}]"
                // operatorTemplate: "{} != {}"
                // 需要组合成: "/patientFamilyVo[detailAddress != null]"
                String condition = CharSequenceUtil.format(operatorTemplate, expectedVariableName, expectedValue);
                result = CharSequenceUtil.format(outerLayerExpansion, condition);
            }
            //单值：只有变量名，通常用于 IS_NULL, NOT_NULL
            else if (CharSequenceUtil.isNotEmpty(expectedVariableName) && !expectedVariableName.trim().equals(" ")) {
                // outerLayerExpansion: "/patientFamilyVo[{}]"
                // operatorTemplate: "{} != null"
                // 需要组合成: "/patientFamilyVo[detailAddress != null]"
                String condition = CharSequenceUtil.format(operatorTemplate, expectedVariableName);
                result = CharSequenceUtil.format(outerLayerExpansion, condition);
            }
            else {
                // 如果没有有效的变量名，直接返回路径（去掉占位符）
                result = outerLayerExpansion.replace("[{}]", "").replace("{}", "");
            }
        }
        else {
            // 没有 outerLayerExpansion 的情况
            if (CharSequenceUtil.isAllNotEmpty(expectedVariableName, expectedValue, additionalExpectedValue)) {
                result = CharSequenceUtil.format(operatorTemplate, expectedValue, expectedVariableName, expectedVariableName, additionalExpectedValue);
            } else if (CharSequenceUtil.isAllNotEmpty(expectedVariableName, expectedValue)) {
                result = CharSequenceUtil.format(operatorTemplate, expectedVariableName, expectedValue);
            } else if (CharSequenceUtil.isNotEmpty(expectedVariableName)) {
                result = CharSequenceUtil.format(operatorTemplate, expectedVariableName);
            }
        }

        return result;
    }



}
