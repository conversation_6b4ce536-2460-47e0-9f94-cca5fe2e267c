package com.puree.followup.question.service.impl;

import com.puree.followup.question.enums.QuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.followup.question.service.SycQuestionStatusService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName: SycQuestionStatusServiceImpl
 * @Date 2024/7/30 11:47
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
@Service
public class SycQuestionStatusServiceImpl implements SycQuestionStatusService {

    @Autowired
    private QuestionMapper questionMapper;
    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper;


    public void sycQuestionStatus(){
        Date now = new Date();
        questionMapper.updateStatusByCloseType(QuestionStatusEnum.ENABLE.getIndex() ,  YesNoEnum.YES.getCode() , now );
        patientQuestionRecordMapper.batchUpdateFillStatus();
    }

}


