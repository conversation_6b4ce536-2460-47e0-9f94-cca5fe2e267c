package com.puree.followup.domain.medical.constant;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName RegularRecordEnum
 * <AUTHOR>
 * @Description 常规记录枚举
 * @date 2024/5/9 17:37
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum RegularRecordEnum {

    TALL(1, "TALL", "TALL", "cm", "BM", "身高", 0),
    WEIGHT(2, "WEIGHT", "WEIGHT", "kg", "BM", "体重", 1),
    BMI(3, "BMI", "BMI", "", "BM", "BMI", 2),
    SYSTOLIC(4, "SYSTOLIC", "SYSTOLIC", "mmHg", "BP", "收缩压(右)", 3),
    DIASTOLIC(5, "DIASTOLIC", "DIASTOLIC", "mmHg", "BP", "舒张压(右)", 4),
    HEART_RATE(6, "HEART_RATE", "HEART_RATE", "次/分钟", "BP", "心率", 5),
    FASTING_BLOOD_SUGAR(7, "FASTING_BLOOD_SUGAR", "FASTING_BLOOD_SUGAR", "mmol/L", "BS", "空腹血糖", 0),
    POSTPRANDIAL_BLOOD_SUGAR(8, "POSTPRANDIAL_BLOOD_SUGAR", "POSTPRANDIAL_BLOOD_SUGAR", "mmol/L", "BS", "餐后血糖", 0),
    RANDOM_BLOOD_SUGAR(9, "RANDOM_BLOOD_SUGAR", "RANDOM_BLOOD_SUGAR", "mmol/L", "BS", "随机血糖", 2),
    BODY_TEMPERATURE(10, "BODY_TEMPERATURE", "BODY_TEMPERATURE", "℃", "BT", "温度", 0),
    TOTAL_CHOLESTEROL(11, "TOTAL_CHOLESTEROL", "TOTAL_CHOLESTEROL", "mmol/L", "BL", "总胆固醇", 0),
    TRIGLYCERIDES(12, "TRIGLYCERIDES", "TRIGLYCERIDES", "mmol/L", "BL", "甘油三脂", 1),
    HDL(13, "HDL", "HDL", "mmol/L", "BL", "高密度脂蛋白", 2),
    LDL(14, "LDL", "LDL", "mmol/L", "BL", "低密度脂蛋白", 3),
    URIC_ACID(15, "URIC_ACID", "URIC_ACID", "mmol/L", "UA", "尿酸", 0),
    BLOOD_OXYGEN(16, "BLOOD_OXYGEN", "BLOOD_OXYGEN", "%", "BO", "血氧", 0),
    PULSE_RATE(17, "PULSE_RATE", "PULSE_RATE", "次/分钟", "BO", "脉搏", 1),
    BLOOD_TYPE(18, "BLOOD_TYPE", "BLOOD_TYPE", "", "BTYPE", "血型", 0),

    /**
     * 对接脑心健设备
     */
    LEFT_SYSTOLIC(19, "LEFT_SYSTOLIC", "LEFT_SYSTOLIC", "mmHg", "BP", "收缩压(左)", 0),
    LEFT_DIASTOLIC(20, "LEFT_DIASTOLIC", "LEFT_DIASTOLIC", "mmHg", "BP", "舒张压(左)", 1),
    IAD(21, "IAD", "IAD", "mmHg", "BP", "IAD", 3),
    ;


    /**
     * @param code 枚举code
     * @return com.puree.followup.domain.medical.constant.RegularRecordEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @date 2024/5/13 15:39
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RegularRecordEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(RegularRecordEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
    }

    /**
     * @param parentCode 父 CODE
     * @return java.util.List<com.puree.followup.domain.medical.constant.RegularRecordEnum>
     * @Description 根据父 CODE 获取枚举列表
     * <AUTHOR>
     * @date 2024/5/23 18:22
     **/
    public static List<RegularRecordEnum> getEnumByParentCode(String parentCode) {
        if (parentCode == null) {
            return null;
        }
        return Arrays.stream(RegularRecordEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getParentCode(), parentCode))
                .collect(Collectors.toList());
    }

    /**
     * 编号
     */
    private final Integer number;

    /**
     * 代码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;
    /**
     * 值单位
     */
    private final String unit;
    /**
     * 范围值CODE
     */
    private final String parentCode;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 排序
     */
    private final int sort;
}
