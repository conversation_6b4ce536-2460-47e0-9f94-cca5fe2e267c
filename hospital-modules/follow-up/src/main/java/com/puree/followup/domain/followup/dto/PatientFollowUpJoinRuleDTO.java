package com.puree.followup.domain.followup.dto;

import com.puree.hospital.business.api.model.BusTcmSyndromeDTO;
import lombok.Data;

import java.util.List;

/**
 * 患者加入随访
 * <AUTHOR>
 * @date 2023/5/18 10:49
 */
@Data
public class PatientFollowUpJoinRuleDTO {

    /**
     * 商品id列表
     */
    private List<Long> goodsIds;

    /**
     * 药品id列表
     */
    private List<Long> drugIds;

    /**
     * 服务包id列表
     */
    private List<Long> packIds;

    /**
     * 中医诊断id列表
     */
    private List<BusTcmSyndromeDTO> tcmList;

    /**
     * 西医诊断id类别
     */
    private List<Long> mmIds;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 药品就诊人id
     */
    private Long drugFamilyId;

    /**
     * 商品就诊人id
     */
    private Long goodsFamilyId;


    /**
     * 就诊人id
     */
    private Long familyId;

    /**
     * 群组id
     */
    private Long groupId;

    /**
     * 处方诊断入组中，要查询的总的诊断个数
     */
    private Integer totalDiags;


    /**
     * 订单编号
     */
    private String orderNo;
}
