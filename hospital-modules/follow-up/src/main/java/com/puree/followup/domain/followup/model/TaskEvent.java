package com.puree.followup.domain.followup.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/15 11:03
 * @description 事件
 */
@Data
public class TaskEvent {

    /**
     *  事件id（后端使用）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     *  事件序号(前端使用)
     */
    private Integer eventNum;

    /**
     * 1-发送消息    2-发送患教   3-发送问卷   4-发送推荐   10-加入新分项   11-终止本分项  12-入组新随访  13-终止本随访
     *
     * TaskEventTypeEnum
     */
    private Integer eventType ;

    /**
     * 1-文本  2-图片
     */
    private Integer messageType ;

    /**
     *  消息内容
     */
    private String message ;

    /**
     *  图片地址
     */
    private String picAddr;

    /**
     *  患教id， 问卷id
     */
    private Long sendId ;

    /**
     * 加入新分项的id或入组新新随访的id
     */
    private Long followUpDataId ;

    /**
     *  患教 name， 问卷 name
     */
    private String sendName ;


    /**
     *  额外提醒 : 公众号 + 短信 + 语音电话
     *  字符串数组, 1,0,1
     */
    private String extraRemind ;

    /**
     *  1-推荐医生  2-推荐科室  3-推荐商品  4-推荐服务包
     *
     */
    private Integer recommendSourceId ;

    /**
     * 医生id、科室id、商品id、服务包id   用string的原因是 科室存在级联关系
     */
    private String recommendId ;

    /**
     * 医生name 科室name
     */
    private String recommendName ;

    /**
     * 版本号
     */
    private Integer revision;

    /**
     * 事件编辑时的行为：add、update、del
     */
    private String action;

    private String doctorPhoto ;

    private String doctorDept ;



}
