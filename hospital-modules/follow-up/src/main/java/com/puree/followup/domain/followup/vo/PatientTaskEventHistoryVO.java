package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.puree.followup.domain.followup.model.TaskEvent;
import lombok.Data;

import java.util.Date;

/**
 * 患者随访事件执行历史表 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
public class PatientTaskEventHistoryVO {

    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 患者随访记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemRecordId;

    /**
     * 任务执行ID
     */
    private Long taskHistoryId;

    /**
     * 任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;
    /**
     * 事件ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long eventId;
    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 执行事件
     */
    private TaskEvent events;

    /**
     * 事件名称
     */
    private String showData;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 事件执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date eventExecuteTime;

    /**
     * 事件提醒时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date remindTime;

    /**
     * 事件提醒状态：0.未提醒 1.已提醒
     */
    private String eventRemindStatus;

    /**
     * 事件完成状态：0.未开始 1.消息未读/问卷未填写 2.消息已读/问卷已填写 3.已过期 4.已终止
     */
    private String eventFinishStatus;

    /**
     * 患者端事件完成状态：iscoming.未开始 ongoing.未完成 finished.已完成 hasexpired.已过期 terminated.已终止
     */
    private String patientEventFinishStatus;

    /**
     * 删除标识
     */
    private Integer isDelete;

    /**
     * 事件类型
     */
    private String eventType;

}