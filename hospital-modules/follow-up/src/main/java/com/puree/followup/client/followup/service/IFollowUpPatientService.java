package com.puree.followup.client.followup.service;


import com.puree.followup.domain.followup.dto.PatientFollowUpQueryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskEventHistoryDTO;
import com.puree.followup.domain.followup.dto.PatientTaskHistoryQueryDTO;
import com.puree.followup.domain.followup.dto.QRJoinInDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.PatientTaskHistory;
import com.puree.followup.domain.followup.model.TaskAndEventStatus;
import com.puree.followup.domain.followup.vo.FollowUpInvitePageVO;
import com.puree.followup.domain.followup.vo.PatientFollowUpListVO;
import com.puree.followup.domain.followup.vo.PatientTaskHistoryTreeVO;
import com.puree.followup.domain.followup.vo.TaskEventVO;
import com.puree.hospital.common.api.domain.AjaxResult;
import com.puree.hospital.followup.api.model.AppletQrCodeVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/8 15:53
 * @description 随访 服务类
 */
public interface IFollowUpPatientService {


    Boolean updateEventStatus(PatientTaskEventHistoryDTO dto, TaskAndEventStatus status);

    AjaxResult<AppletQrCodeVO> qrcodeJoinIn(FollowUp followUp, QRJoinInDTO dto);

    AjaxResult<List<PatientTaskHistoryTreeVO>> taskHistory(PatientTaskHistoryQueryDTO dto);

    List<PatientFollowUpListVO> followUpList(PatientFollowUpQueryDTO dto);

    Boolean finishEventAhead(PatientTaskEventHistoryDTO dto, TaskAndEventStatus status);

    TaskEventVO getEventHistoryInfo(PatientTaskEventHistoryDTO dto);

    FollowUpInvitePageVO invitePage(PatientFollowUpRecord record, Long hospitalId);

    Boolean refuseJoining(PatientFollowUpRecord record, Long hospitalId);

    Boolean confirmJoining(PatientFollowUpRecord record, FollowUp followUp);

    TaskEventVO getEventAndTaskStatus(Long id);

    Long handleTaskAndEventHistory(PatientTaskEventHistoryDTO dto);

    Boolean updateEventsStatus(List<Long> ids, Long hospitalId);

    void syncUpdateTaskStatus(Map<Long, PatientTaskHistory> updateTasksMap, Long hospitalId);

}
