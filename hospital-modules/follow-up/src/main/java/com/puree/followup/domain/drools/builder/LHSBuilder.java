package com.puree.followup.domain.drools.builder;

import com.puree.followup.domain.drools.constant.LeftHandSideConditionEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @ClassName LHSBuilds
 * <AUTHOR>
 * @Description 规则的条件部分构建
 * @Date 2024/4/2 16:00
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class LHSBuilder {
    /**
     * 并
     */
    private final static String AND = " and ";
    /**
     * 或
     */
    private final static String OR = " or ";
    /**
     * 变量名前缀
     */
    private final static String VARIABLE_SYMBOL = "$";
    /**
     * 括号开始
     */
    private final static String PARENTHESES_START = "(";
    /**
     * 括号结束
     */
    private final static String PARENTHESES_END = ")";

    /**
     * 条件
     */
    private LeftHandSideConditionEnum condition;
    /**
     * 是否为 or ,如果为 fasle 则为 and
     */
    private Boolean isOr = true;
    /**
     * 变量名，可选
     */
    private String variableName;
    /**
     * 简单 class name（如 calculat）
     */
    private String simpleClassName;
    /**
     * 比较运算符
     */
    private List<ComparisonOperatorBuilder> comparisonOperatorBuilderList;

    public String builderLHSString() {
        StringBuilder builder = new StringBuilder();
        // 去重已添加的条件
        Set<String> addedConditions = new HashSet<>();

        if (comparisonOperatorBuilderList != null && !comparisonOperatorBuilderList.isEmpty()) {
            boolean hasConditions = false;

            for (ComparisonOperatorBuilder op : comparisonOperatorBuilderList) {
                // 生成条件字符串
                String conditionStr = op.builderComparisonOperatorString();

                // 如果是无效条件（如 null != null），跳过
                if (conditionStr.contains("null != null") ||
                        conditionStr.trim().isEmpty() ||
                        addedConditions.contains(conditionStr)) {
                    continue;
                }

                if (hasConditions) {
                    builder.append(AND);
                }

                builder.append("exists ")
                        .append(PARENTHESES_START)
                        .append(simpleClassName)
                        .append(PARENTHESES_START)
                        .append(conditionStr)
                        .append(PARENTHESES_END)
                        .append(PARENTHESES_END);

                addedConditions.add(conditionStr);
                hasConditions = true;
            }
            builder.append("\n");
        }
        return builder.toString();
    }










}
