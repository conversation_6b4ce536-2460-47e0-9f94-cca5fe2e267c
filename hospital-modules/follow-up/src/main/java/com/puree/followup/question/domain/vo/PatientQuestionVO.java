package com.puree.followup.question.domain.vo;

import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import lombok.Data;

/**
 * @ClassName: PatientQuestionVO
 * @Date 2024/6/21 11:53
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
public class PatientQuestionVO {

    private Question question ;

    private PatientQuestionRecord patientQuestionRecord ;

}
