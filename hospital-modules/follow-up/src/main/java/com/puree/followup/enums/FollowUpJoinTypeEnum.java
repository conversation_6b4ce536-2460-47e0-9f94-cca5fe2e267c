package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  入组类型：0.自动入组 1.需患者同意
 */
public enum FollowUpJoinTypeEnum {

    AUTO(0, "auto", "自动入组"),
    PATIENT_AGREE(1, "patientAgree", "需患者同意");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpJoinTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpJoinTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpJoinTypeEnum[] metaArr = FollowUpJoinTypeEnum.values();
        for (FollowUpJoinTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpJoinTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpJoinTypeEnum[] metaArr = FollowUpJoinTypeEnum.values();
        for (FollowUpJoinTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
