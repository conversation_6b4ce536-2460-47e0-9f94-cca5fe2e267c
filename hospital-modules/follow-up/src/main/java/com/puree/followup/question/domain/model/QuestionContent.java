package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @ClassName: QuestionContent
 * @Date 2024/6/5 16:41
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class QuestionContent {

    /**
     *  基础题目，包含选择题，填空题 等等
     */
    private List<BaseQuestionContent> baseQuestionContentList ;

    /**
     * 患者指标
     */
    private List<PatientIndexReport> patientIndexList;

    /**
     *  患者报告
     */
    private List<PatientIndexReport> patientReportList ;


}
