package com.puree.followup.enums;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;

/**
 * @ClassName: DayOfWeekEnum
 * @Date 2024/4/11 10:01
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum DayOfWeekEnum {

    MONDAY("1", "一", 1, "Monday") ,
    TUESDAY("2", "二", 2, "Tuesday") ,
    WEDNESDAY("3", "三", 4, "Wednesday") ,
    THURSDAY("4", "四", 8, "Thursday") ,
    FRIDAY("5", "五", 16, "Friday") ,
    SATURDAY("6", "六", 32, "Saturday") ,
    SUNDAY("7", "日", 64, "Sunday")
    ;


    private String index;
    private String name ;

    private Integer bitMapValue ;

    private String desc ;

    DayOfWeekEnum(String index, String name, Integer bitMapValue, String desc) {
        this.index = index;
        this.name = name;
        this.bitMapValue = bitMapValue ;
        this.desc = desc;
    }

    public String getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public Integer getBitMapValue() {
        return bitMapValue;
    }

    public String getDesc() {
        return desc;
    }

    public static DayOfWeekEnum getEnumByName(String name) {
        if (StrUtil.isBlank(name)){
            return null;
        }
        DayOfWeekEnum dayOfWeekEnum = Arrays.stream(DayOfWeekEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getName(), name) )
                .findAny()
                .orElse(null);
        return dayOfWeekEnum;
    }

    public static DayOfWeekEnum getEnumByIndex(String index) {
        if (StrUtil.isBlank(index)){
            return null;
        }
        DayOfWeekEnum dayOfWeekEnum = Arrays.stream(DayOfWeekEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getIndex(), index) )
                .findAny()
                .orElse(null);
        return dayOfWeekEnum;
    }

    public static DayOfWeekEnum getEnumByDesc(String desc) {
        if (StrUtil.isBlank(desc)){
            return null;
        }
        DayOfWeekEnum dayOfWeekEnum = Arrays.stream(DayOfWeekEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getDesc(), desc) )
                .findAny()
                .orElse(null);
        return dayOfWeekEnum;
    }

    public static String getWeekIndexByName(String name) {
        DayOfWeekEnum enumByName = getEnumByName(name);
        return enumByName.getIndex();
    }

    public static String transferWeekInfoByName(String weekdays) {
        if (StrUtil.isNotBlank(weekdays)) {
            String[] split = weekdays.split(",") ;
            for (int t=0;t<split.length;t++) {
                split[t] = DayOfWeekEnum.getWeekIndexByName(split[t].trim()) ;
            }
            return String.join("," , split) ;
        }
        return null ;
    }

    public static String transferWeekInfoByIndex(String weekdays) {
        if (StrUtil.isNotBlank(weekdays)) {
            String[] split = weekdays.split(",") ;
            for (int t=0;t<split.length;t++) {
                DayOfWeekEnum enumByIndex = DayOfWeekEnum.getEnumByIndex(split[t].trim());
                split[t] = enumByIndex.getName() ;
            }
            return String.join("," , split) ;
        }
        return null ;
    }

    public static String getWeekIndexByDesc(String desc) {
        DayOfWeekEnum dayOfWeekEnum = getEnumByDesc(desc);
        return dayOfWeekEnum.getIndex();
    }

}
