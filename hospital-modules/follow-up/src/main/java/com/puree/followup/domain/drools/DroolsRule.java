package com.puree.followup.domain.drools;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName DroolsRule
 * <AUTHOR>
 * @Description 规则实体
 * @Date 2024/4/26 16:31
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class DroolsRule  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id ;
    /**
     * string类型规则
     */
    private String strRule ;
    /**
     * 修改时间
     */
    private Date updateTime ;
    /**
     * 创建时间
     */
    private Date createTime ;
    /**
     * 删除标记;0未删除，1已删除
     */
    private Integer isDelete ;
    /**
     * 是否全部匹配;是否全部匹配;
     */
    private Boolean isAll ;
}
