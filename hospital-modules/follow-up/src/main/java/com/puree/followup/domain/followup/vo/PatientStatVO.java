package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 随访患者列表数据 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientStatVO {

    /**
     * 随访总患者
     */
    private Integer totalPatients;

    /**
     * 随访中患者数
     */
    private Integer ongoingPatients;

    /**
     * 随访已完成患者数
     */
    private Integer finishedPatients;

    /**
     * 提前终止的患者数
     */
    private Integer terminatedPatients;

    /**
     * 今日新增患者数
     */
    private Integer todayNewPatients;


//  `task_status` tinyint NOT NULL DEFAULT 1 COMMENT '任务完成状态：1.进行中 2.已完成 3.已过期',


    /**
     * 任务总数
     */
    private Integer totalTasks;

    /**
     * 未开始的任务数
     */
    private Integer iscomingTasks;

    /**
     * 进行中的任务数
     */
    private Integer ongoingTasks;

    /**
     * 已完成任务数
     */
    private Integer finishedTasks;

    /**
     * 已过期任务数
     */
    private Integer hasExpiredTasks;

    /**
     * 已终止任务数
     */
    private Integer terminatedTasks;

    /**
     * 随访进度(百分比)
     */
    private BigDecimal finishedFollowUpRate;

    /**
     * 任务完成率(百分比)
     */
    private BigDecimal finishedTaskRate;

    /**
     * 问卷提交
     */
    private Integer finishedQuestionnaires;


    /**
     * 随访名称
     */
    private String followUpName;

    /**
     * 发布标识：false.否 true.是
     */
    private Boolean isPublish;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 随访周期类型(到期时间)：longTerm:长期、joinIn:加入随访X天后结束
     */
    private String expireType;


}