package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.JoinRuleTypeHandler;
import com.puree.followup.domain.followup.model.joinrule.JoinRule;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 随访预警-触发条件
 * <AUTHOR>
 * @date 2024-08-14 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class WarnCondition implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 触发项目名称-第一项
     */
    private String firstItemName;

    /**
     * 触发项目名称-第二项
     */
    private String secondItemName;

    /**
     * 值（用户所填写的）
     */
    private String value;

    /**
     * 判断范围(设置的条件)
     */
    private String conditionDesc;


}