package com.puree.followup.domain.medical.dto;

import com.puree.followup.domain.medical.constant.IsHandleEnum;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import com.puree.followup.domain.medical.constant.RecordTypeEnum;
import com.puree.followup.domain.medical.constant.RegularRecordEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName ReportRegularRecordDTO
 * <AUTHOR>
 * @Description 新增 DTO
 * @Date 2024/5/24 16:59
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
public class ReportRegularRecordInsertDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目类型;TALL为 身高,WEIGHT为 体重,BMI为 体重指数,SYSTOLIC为 收缩压,DIASTOLIC为 舒张压,
     * PULSE_RATE为 脉搏,BLOOD_SUGAR为 血糖,BODY_TEMPERATURE为 体温,TOTAL_CHOLESTEROL为 总胆固醇,
     * TRIGLYCERIDES为 甘油三脂,HDL为 高密度脂蛋白,LDL为 低密度脂蛋白,URIC_ACID为 尿酸,BLOOD_OXYGEN为 血氧
     * FASTING_BLOOD_SUGAR 为 空腹血糖,POSTPRANDIAL_BLOOD_SUGAR为 餐后血糖,RANDOM_BLOOD_SUGAR 为 随机血糖
     */
    private RegularRecordEnum itemType;
    /**
     * 项目值
     */
    private String itemValue;
    /**
     * 来源类型;DATA_PUSH 数据推送，PATIENT_ADDED 患者添加，BACKGROUND_ADD 后台添加或医生添加，DOCTOR_ADDED 医生添加
     */
    private RecordSourceEnum source ;
    /**
     * 项目值单位
     */
    private String itemUnit;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 患者身份证号
     */
    private String patientIdNumber;
}
