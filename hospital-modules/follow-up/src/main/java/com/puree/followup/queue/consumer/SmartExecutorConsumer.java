package com.puree.followup.queue.consumer;

import cn.hutool.core.util.IdUtil;
import com.puree.followup.admin.followup.mapper.FollowUpMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskEventHistoryMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskHistoryMapper;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.domain.followup.dto.FollowUpInsertDTO;
import com.puree.followup.domain.followup.dto.FollowUpItemInsertDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.FollowUpItemExecutor;
import com.puree.followup.domain.followup.model.PatientFollowUpItemRecord;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.model.PatientJoinInResult;
import com.puree.followup.domain.followup.model.PatientTaskEventHistory;
import com.puree.followup.domain.followup.model.PatientTaskHistory;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.followup.enums.FollowUpItemStatusEnum;
import com.puree.followup.enums.FollowUpJoinStatusEnum;
import com.puree.followup.enums.FollowUpJoinTypeEnum;
import com.puree.followup.enums.FollowUpTerminatorTypeEnum;
import com.puree.followup.enums.PatientEventStatusEnum;
import com.puree.followup.enums.PatientTaskStatusEnum;
import com.puree.followup.enums.TaskEventTypeEnum;
import com.puree.followup.enums.TaskRemindStatusEnum;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.service.ImWxMessageService;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: SmartExecutorConsumer 智能执行--》执行事件的消费者
 * @Date 2024/7/21 9:14
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
@Component
public class SmartExecutorConsumer extends RedisStreamConsumer<FollowUpItemExecutor> {

    private Logger logger = LoggerFactory.getLogger(SmartExecutorConsumer.class);

    @Autowired
    private PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper ;
    @Autowired
    private PatientTaskHistoryMapper patientTaskHistoryMapper ;
    @Autowired
    private FollowUpMapper followUpMapper;
    @Autowired
    private PatientFollowUpRecordMapper patientFollowUpRecordMapper ;
    @Autowired
    private PatientTaskEventHistoryMapper patientTaskEventHistoryMapper ;
    @Autowired
    private ImWxMessageService imWxMessageService ;
    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper ;
    @Autowired
    private IFollowUpService followUpService;

    @Override
    public void onMessage(RedisMessage<FollowUpItemExecutor> message) {
        try{
            FollowUpItemExecutor executor = message.getBody();
            this.smartExecutorTaskEvent(executor);
        }catch (Exception ex) {
            logger.error("SmartExecutorConsumer failed : " ,  ex);
        }
    }

    private void smartExecutorTaskEvent(FollowUpItemExecutor executor){
        List<TaskEvent> events = executor.getEvents();
        for (TaskEvent event : executor.getEvents()) {
            //如果是发送消息、发送患教、发送问卷、发送推荐则走如下消息推送逻辑
            if(TaskEventTypeEnum.MESSAGE.getIndex().equals(event.getEventType())
                    || TaskEventTypeEnum.TUTORIAL.getIndex().equals(event.getEventType())
                    || TaskEventTypeEnum.QUESTIONNAIRE.getIndex().equals(event.getEventType())
                    || TaskEventTypeEnum.RECOMMEND.getIndex().equals(event.getEventType())){
                sendMessages(executor, event);
            }else if(TaskEventTypeEnum.JOIN_IN_NEW_ITEM.getIndex().equals(event.getEventType())
                    || TaskEventTypeEnum.TERMINATE_CURRENT_ITEM.getIndex().equals(event.getEventType())
                    || TaskEventTypeEnum.JOIN_IN_NEW_FOLLOW_UP.getIndex().equals(event.getEventType())
                    || TaskEventTypeEnum.TERMINATE_CURRENT_FOLLOW_UP.getIndex().equals(event.getEventType())){//如果是加入新分项、终止本分项、入组新随访、终止本随访则走如下业务代码
                joinOrTerminate(executor, event);
            }
        }
    }

    //加入新分项、终止本分项、入组新随访、终止本随访则走如下业务代码
    @Transactional(rollbackFor = Exception.class)
    public void joinOrTerminate(FollowUpItemExecutor executor, TaskEvent event) {

        switch(TaskEventTypeEnum.getEnumByIndex(event.getEventType())) {
            case JOIN_IN_NEW_ITEM: //加入新分项
                joinFollowUpItem(executor, event);
                break;
            case TERMINATE_CURRENT_ITEM: //终止本分项
                terminateFollowUpItem(executor);
                break;
            case JOIN_IN_NEW_FOLLOW_UP: //入组新随访
                joinFollowUp(executor, event);
                break;
            case TERMINATE_CURRENT_FOLLOW_UP: //终止本随访
                terminateFollowUp(executor.getFollowUpRecordId());
                break;
        }
    }

    //加入新分项
    private void joinFollowUpItem(FollowUpItemExecutor executor, TaskEvent event) {

        if(event.getFollowUpDataId() == null)
            return;

        FollowUp followUp = followUpMapper.getById(executor.getFollowUpId());
        if(followUp == null)
            return;

        //判断患者已加入该随访
        PatientFollowUpRecord followUpRecord = patientFollowUpRecordMapper.getById(executor.getFollowUpRecordId());
        if(followUpRecord == null || followUpRecord.getIsDelete().equals(YesNoEnum.YES.getCode()))
            return;
        //只能加入随访中的
        if(!followUpRecord.getJoinStatus().equals(FollowUpJoinStatusEnum.FOLLOWUPING.getIndex()))
            return;

        FollowUpItemInsertDTO insertDTO = new FollowUpItemInsertDTO();
        insertDTO.setFollowUpId(executor.getFollowUpId());
        insertDTO.setFollowUpRecordId(executor.getFollowUpRecordId());
        insertDTO.setItemId(event.getFollowUpDataId());
        insertDTO.setHospitalId(executor.getHospitalId());
        insertDTO.setRedisKey("joinin-item:" + executor.getHospitalId() + ":" + executor.getFollowUpId() + ":" + event.getFollowUpDataId() + ":" + executor.getUserId() + ":" + executor.getPatientId());

        followUpService.patientJoinInItem(insertDTO, followUpRecord, followUp);
    }

    //终止本分项
    private void terminateFollowUpItem(FollowUpItemExecutor executor) {

        //判断患者已加入该随访且已加入该分项
        PatientFollowUpRecord followUpRecord = patientFollowUpRecordMapper.getById(executor.getFollowUpRecordId());
        if(followUpRecord == null || followUpRecord.getIsDelete().equals(YesNoEnum.YES.getCode()))
            return;
        //只终止随访中的
        if(!followUpRecord.getJoinStatus().equals(FollowUpJoinStatusEnum.FOLLOWUPING.getIndex()))
            return;

        PatientFollowUpItemRecord updateItemRecord = new PatientFollowUpItemRecord();
        updateItemRecord.setFollowUpRecordId(executor.getFollowUpRecordId());
        updateItemRecord.setItemId(executor.getItemId());
        updateItemRecord.setEndDate(new Date());
        updateItemRecord.setTerminatorType(FollowUpTerminatorTypeEnum.ADMIN.getIndex());
        updateItemRecord.setTerminator(PatientTaskEventConsumer.TASK);
        updateItemRecord.setItemStatus(FollowUpItemStatusEnum.TERMINATED.getIndex());
        updateItemRecord.setItemStatusList(Arrays.asList(FollowUpItemStatusEnum.ONGOING.getIndex()));
        updateItemRecord.setUpdateTime(new Date());
        updateItemRecord.setIsDelete(YesNoEnum.NO.getCode());
        patientFollowUpItemRecordMapper.updateItemRecord(updateItemRecord);

        //终止任务
        PatientTaskHistory updateTaskHistory = new PatientTaskHistory();
        updateTaskHistory.setFollowUpRecordId(executor.getFollowUpRecordId());
        updateTaskHistory.setItemId(executor.getItemId());
        updateTaskHistory.setTaskStatus(PatientTaskStatusEnum.TERMINATED.getIndex());
        updateTaskHistory.setTaskStatusList(Arrays.asList(PatientTaskStatusEnum.ONGOING.getIndex()));//已过期的不可以终止
        updateTaskHistory.setUpdateBy(PatientTaskEventConsumer.TASK);
        updateTaskHistory.setUpdateTime(new Date());
        updateTaskHistory.setIsDelete(YesNoEnum.NO.getCode());
        patientTaskHistoryMapper.updateTask(updateTaskHistory);
    }

    //入组新随访
    private void joinFollowUp(FollowUpItemExecutor executor, TaskEvent event){
        if(event.getFollowUpDataId() == null)
            return;
        FollowUp followUp = followUpMapper.getById(event.getFollowUpDataId());
        if(followUp == null)
            return;
        FollowUpInsertDTO insertDTO = new FollowUpInsertDTO();
        insertDTO.setFollowUpId(event.getFollowUpDataId());
        insertDTO.setUserId(executor.getUserId());
        insertDTO.setPatientId(executor.getPatientId());
        insertDTO.setJoinType(followUp.getJoinType());
        insertDTO.setJoinStatus(FollowUpJoinTypeEnum.getByIndex(followUp.getJoinType()).equals(FollowUpJoinTypeEnum.AUTO) ? FollowUpJoinStatusEnum.FOLLOWUPING.getIndex() : FollowUpJoinStatusEnum.INVITING.getIndex());
        insertDTO.setJoinReason("(智能执行)");
        //构建rediskey
        insertDTO.setRedisKey("joinin:" + executor.getHospitalId() + ":" + event.getFollowUpDataId() + ":" + executor.getUserId() + ":" + executor.getPatientId());
        //患者入组
        PatientJoinInResult result = followUpService.patientJoinIn(insertDTO);
        logger.debug("患者加入新随访的结果：{}", result);
    }

    //终止本随访
    private void terminateFollowUp(Long id) {
        //判断患者随访是否存在
        PatientFollowUpRecord followUpRecord = patientFollowUpRecordMapper.getById(id);
        if(followUpRecord == null || followUpRecord.getIsDelete().equals(YesNoEnum.YES.getCode()))
            return;
        //只终止随访中的
        if(!followUpRecord.getJoinStatus().equals(FollowUpJoinStatusEnum.FOLLOWUPING.getIndex()))
            return;
        try{
            followUpService.terminateFollowUp(id);
        }catch (Exception e){
            logger.error("终止随访失败：", e);
        }
    }

    //消息推送
    @Transactional(rollbackFor = Exception.class)
    public void sendMessages(FollowUpItemExecutor executor, TaskEvent event){
        FollowUp followUp = followUpMapper.getById(executor.getFollowUpId());
        if(followUp == null)
            return;
        PatientFollowUpRecord followUpRecord = patientFollowUpRecordMapper.getById(executor.getFollowUpRecordId());
        if(followUpRecord == null)
            return;
        PatientTaskHistory taskHistory = new PatientTaskHistory();
        taskHistory.setFollowUpId(executor.getFollowUpId());
        taskHistory.setFollowUpRecordId(executor.getFollowUpRecordId());
        taskHistory.setItemRecordId(-IdUtil.getSnowflake(1,1).nextId());
        taskHistory.setTaskId(-IdUtil.getSnowflake(1,1).nextId());
        taskHistory.setTaskName("智能执行");
        taskHistory.setItemId(executor.getItemId());
        taskHistory.setHospitalId(executor.getHospitalId());
        taskHistory.setUserId(executor.getUserId());
        taskHistory.setPatientId(executor.getPatientId());
        taskHistory.setBeginDay(new Date());
        taskHistory.setRemindTime(new Date());
        taskHistory.setTaskRemindStatus(TaskRemindStatusEnum.REMINDED.getIndex());
        taskHistory.setTaskStatus(PatientTaskStatusEnum.ONGOING.getIndex());
        taskHistory.setIsTerminateFuture(YesNoEnum.NO.getCode());
        taskHistory.setCreateTime(new Date());
        taskHistory.setCreateBy("task");
        taskHistory.setUpdateTime(new Date());
        taskHistory.setUpdateBy("task");
        taskHistory.setIsDelete(YesNoEnum.NO.getCode());
        try{
            patientTaskHistoryMapper.insert(taskHistory);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            return;
        }

        PatientTaskEventHistory eventHistory = new PatientTaskEventHistory();
        eventHistory.setFollowUpId(executor.getFollowUpId());
        eventHistory.setFollowUpRecordId(executor.getFollowUpRecordId());
        eventHistory.setItemRecordId(taskHistory.getItemRecordId());
        eventHistory.setTaskHistoryId(taskHistory.getId());
        eventHistory.setTaskId(taskHistory.getTaskId());
        eventHistory.setEventId(event.getId());
        eventHistory.setEventType(event.getEventType());
        eventHistory.setItemId(executor.getItemId());
        eventHistory.setEvents(event);
        eventHistory.setHospitalId(executor.getHospitalId());
        eventHistory.setUserId(executor.getUserId());
        eventHistory.setPatientId(executor.getPatientId());
        eventHistory.setRemindTime(new Date());
        eventHistory.setEventRemindStatus(TaskRemindStatusEnum.REMINDED.getIndex());
        eventHistory.setEventFinishStatus(PatientEventStatusEnum.ONGOING.getIndex());
        eventHistory.setIsTerminateFuture(YesNoEnum.NO.getCode());
        eventHistory.setCreateTime(new Date());
        eventHistory.setCreateBy("task");
        eventHistory.setUpdateTime(new Date());
        eventHistory.setUpdateBy("task");
        eventHistory.setIsDelete(YesNoEnum.NO.getCode());
        try{
            patientTaskEventHistoryMapper.insert(eventHistory);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            return;
        }
        if (TaskEventTypeEnum.QUESTIONNAIRE.getIndex().equals(event.getEventType()) &&  null!=event.getSendId() )
            this.eventSendQuestion(eventHistory, event.getSendId());
        PatientFollowUpItemRecord itemRecord = new PatientFollowUpItemRecord();
        itemRecord.setUserId(executor.getUserId());
        itemRecord.setPatientId(executor.getPatientId());
        itemRecord.setFollowUpRecordId(executor.getFollowUpRecordId());
        itemRecord.setHospitalId(executor.getHospitalId());
        imWxMessageService.sendImWxMessage(followUp, itemRecord, event, followUpRecord , eventHistory.getId()) ;
    }

    //患者和问卷记录的关联
    @Transactional(rollbackFor = Exception.class)
    public void eventSendQuestion(PatientTaskEventHistory eventHistory, Long questionId){
        PatientQuestionRecord patientQuestionRecord = new PatientQuestionRecord();
        patientQuestionRecord.setHospitalId(eventHistory.getHospitalId()) ;
        patientQuestionRecord.setUserId(eventHistory.getUserId()) ;
        patientQuestionRecord.setPatientId(eventHistory.getPatientId());
        patientQuestionRecord.setQuestionId(questionId) ;
        patientQuestionRecord.setFollowUpId(eventHistory.getFollowUpId()) ;
        patientQuestionRecord.setPatientEventId(eventHistory.getId()) ;
        patientQuestionRecord.setFillStatus(FillQuestionStatusEnum.INVITING.getIndex());
        patientQuestionRecord.setCreateBy(PatientTaskEventConsumer.TASK) ;
        patientQuestionRecord.setUpdateBy(PatientTaskEventConsumer.TASK);
        patientQuestionRecord.setCreateTime(new Date()) ;
        patientQuestionRecord.setUpdateTime(new Date()) ;
        patientQuestionRecord.setIsDelete(YesNoEnum.NO.getCode()) ;
        try{
            patientQuestionRecordMapper.insert(patientQuestionRecord) ;
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            return;
        }
    }


}
