package com.puree.followup.domain.followup.model.joinrule;

import lombok.Data;

import java.util.List;

/**
 * @ClassName JoinRuleFive
 * <AUTHOR>
 * @Description 入组规则
 * @Date 2024/4/16 15:57
 * @Version 1.0
 */
@Data
public class JoinRule {

    /**
     * 入组方式-诊断
     */
    private List<JoinRuleDiagnosis> joinRuleDiagnosis;

    /**
     * 入组方式-数据推送
     */
    private List<JoinRulePushData> joinRulePushData;

    /**
     * 入组方式-商品
     */
    private List<JoinRuleGoods> joinRuleGoods;

    /**
     * 入组方式-药品
     */
    private List<JoinRuleDrugs> joinRuleDrug;

    /**
     * 入组方式-服务包
     */
    private List<JoinRuleServicePackage> joinRuleServicePackage;

    /**
     * 入组方式-健康档案
     */
    private List<JoinRuleHealthRecord> joinRuleHealthRecord;


}
