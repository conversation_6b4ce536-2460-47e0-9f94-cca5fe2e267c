package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.ToString;

/**
 *  随访事件业务数据
 * <AUTHOR>
 * @date 2025/3/21 11:18
 */
@Data
@ToString
public class FollowUpEventBusinessDTO {


    /**
     * hospitalId
     */
    private Long hospitalId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 随访id
     */
    private Long followUpId;

    /**
     * 随访记录id
     */
    private Long followUpRecordId;

    /**
     * 群组id
     */
    private Long groupId;

    /**
     * im类型
     */
    private Integer imType;

    /**
     * 患教ID
     */
    private Long tutorialId;

    /**
     * 任务事件历史记录id
     */
    private Long taskEventHistoryId;

    /**
     * 问卷内容是否过长
     */
    private boolean contentIsTooLong;

    /**
     * 问卷id
     */
    private Long questionId;

    /**
     * 入组类型：0.直接添加 1.邀请入组(需患者同意) 2.手动直接添加 3.手动邀请入组(需患者同意)
     */
    private Integer joinType;


}
