package com.puree.followup.domain.medical.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName QuestionnaireDataSummaryDTO
 * <AUTHOR>
 * @Description 问卷数据记录 DTO
 * @Date 2024/7/10 18:02
 * @Version 1.0
 */
@Data
public class QuestionnaireDataSummaryDTO {
    /**
     * 患者身份证
     */
    @NotBlank(message = "患者身份证 不能为空")
    private String patientIdNumber;
    /**
     * 医院ID
     */
    @NotNull(message = "医院ID 不能为空")
    private Long hospitalId;
    /**
     * 指标名
     */
    @NotNull(message = "指标名 不能为空")
    private List<String> itemNames;
}
