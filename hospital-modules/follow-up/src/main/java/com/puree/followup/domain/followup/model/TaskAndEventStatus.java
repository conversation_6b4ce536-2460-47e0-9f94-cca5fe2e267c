package com.puree.followup.domain.followup.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/11 11:03
 * @description 事件和任务状态
 */
@Data
public class TaskAndEventStatus {

    /**
     * 事件完成情况：ongoing.未完成 finished.已完成
     */
    private String eventStatus;

    /**
     * 事件所属任务完成情况：iscoming:未开始、 ongoing:待完成、 finished:已完成、 hasexpired:已过期、 terminated:已终止
     */
    private String taskStatus;

}
