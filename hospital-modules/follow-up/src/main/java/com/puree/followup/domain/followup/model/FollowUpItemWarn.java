package com.puree.followup.domain.followup.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.config.TaskEventTypeHandler;
import com.puree.followup.config.WarnConditionHandler;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 随访预警
 * <AUTHOR>
 * @date 2024-08-14 14:29:33
 */
@Data
public class FollowUpItemWarn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 患者随访分项记录ID
     */
    private Long itemRecordId;

    /**
     * 智能执行ID
     */
    private Long executorId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 问卷ID
     */
    private Long questionnaireId;

    /**
     * 任务连续X次过期
     */
    private Integer expireTimes;

    /**
     * 执行事件列表
     */
    @TableField(typeHandler = TaskEventTypeHandler.class)
    private List<TaskEvent> events;

    /**
     * 触发的条件列表
     */
    @TableField(typeHandler = WarnConditionHandler.class)
    private List<WarnCondition> conditions;

    /**
     * 预警类型：0.问卷预警 1.指标预警 2.超期预警
     */
    private Integer warnType;

    /**
     * 预警状态：0.未处理 1.已处理
     */
    private Integer warnStatus;

    /**
     * 预警备注
     */
    private String warnRemark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 开始日期
     */
    private Date beginDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

    /**
     * 执行事件类型
     */
    private Integer eventType;
}