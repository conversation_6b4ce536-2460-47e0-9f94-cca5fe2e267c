package com.puree.followup.admin.followup.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.domain.classification.model.Classification;
import com.puree.followup.domain.followup.context.FollowUpProcessContext;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.vo.FollowUpVO;
import com.puree.followup.enums.DepartmentLimitEnum;
import com.puree.followup.enums.DoctorLimitEnum;
import com.puree.followup.enums.FollowUpExpireTypeEnum;
import com.puree.followup.enums.FollowUpJoinTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 随访基本信息组装
 * <AUTHOR>
 * @date 2025/1/4 14:54
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FollowUpBasicInfoAssembler implements FollowUpDataAssembler<FollowUpVO> {

    private final IFollowUpService followUpService;

    /**
     * 随访基本信息处理
     * @param context 上下文
     * @return FollowUpVO
     */
    @Override
    public FollowUpVO assemble(FollowUpProcessContext context) {
        FollowUp followUp = context.getFollowUp();
        FollowUpVO followUpVO = BeanUtil.copyProperties(followUp, FollowUpVO.class);
        Classification cla = followUpService.getClaInfo(followUp.getClassifyId(), followUp.getId(), SecurityUtils.getHospitalId());
        followUpVO.setIsEnable(null);
        followUpVO.setIsPublish(null);
        followUpVO.setClassifyName(cla == null ? null : cla.getName());
        followUpVO.setClassifyId(cla == null ? null : cla.getId());
        followUpVO.setExpireType(FollowUpExpireTypeEnum.getByIndex(followUp.getExpireType()).getName());
        followUpVO.setDepartmentLimit(DepartmentLimitEnum.getByIndex(followUp.getDepartmentLimit()).getName());
        followUpVO.setDoctorLimit(DoctorLimitEnum.getByIndex(followUp.getDoctorLimit()).getName());
        followUpVO.setTaskExpireSwitch(!followUp.getTaskExpireSwitch().equals(YesNoEnum.NO.getCode()));
        followUpVO.setTaskExpireWarnSwitch(!followUp.getTaskExpireWarnSwitch().equals(YesNoEnum.NO.getCode()));
        followUpVO.setJoinType(FollowUpJoinTypeEnum.getByIndex(followUp.getJoinType()).getName());
        //如果随访任务过期未启用，则随访任务过期X天不展示
        if(!followUpVO.getTaskExpireSwitch()) {
            followUpVO.setTaskExpireDay(null);
        }
        return followUpVO;
    }
}
