package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/5/27 11:55
 * @Description  患者筛选结果枚举
 */
public enum RuleFilterResultEnum {

    //ongoing:数据准备中  finished：数据已准备完成  failed:数据准备失败
    ONGOING(0, "ongoing", "数据准备中"),
    FINISHED(1, "finished", "数据已准备完成"),
    FAILED(2, "failed", "数据准备失败");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    RuleFilterResultEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static RuleFilterResultEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        RuleFilterResultEnum[] metaArr = RuleFilterResultEnum.values();
        for (RuleFilterResultEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static RuleFilterResultEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        RuleFilterResultEnum[] metaArr = RuleFilterResultEnum.values();
        for (RuleFilterResultEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
