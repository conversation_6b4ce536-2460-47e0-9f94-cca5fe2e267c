package com.puree.followup.domain.medical.dto;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName InspectExamDTO
 * <AUTHOR>
 * @Description 枚举
 * @Date 2024/7/11 16:18
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum InspectExamTypeDTO {
    INSPECT(1, "INSPECT", "INSPECT", "化验单"),
    EXAM(2, "EXAM", "EXAM", "检查单");

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;

    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static InspectExamTypeDTO getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        InspectExamTypeDTO type = Arrays.stream(InspectExamTypeDTO.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
        return type;
    }
}
