package com.puree.followup.queue.consumer;

import com.puree.followup.admin.followup.mapper.FollowUpItemTaskMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpItemRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientFollowUpRecordMapper;
import com.puree.followup.admin.followup.mapper.PatientTaskHistoryMapper;
import com.puree.followup.admin.followup.service.IFollowUpService;
import com.puree.followup.admin.medical.service.MedicalReportService;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;
import com.puree.followup.domain.followup.query.PatientFollowUpRecordQuery;
import com.puree.followup.enums.FollowUpJoinStatusEnum;
import com.puree.followup.service.ImWxMessageService;
import com.puree.hospital.app.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * 删除就诊人终止随访 消费者
 * @ClassName: TerminateFollowUpConsumer
 * @Date 2024/8/28 19:59
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
public class TerminateFollowUpConsumer extends RedisStreamConsumer<BusPatientFamilyVo> {

    private Logger logger = LoggerFactory.getLogger(TerminateFollowUpConsumer.class);

    @Autowired
    private PatientFollowUpItemRecordMapper patientFollowUpItemRecordMapper ;
    @Autowired
    private FollowUpItemTaskMapper followUpItemTaskMapper ;
    @Autowired
    private PatientFollowUpRecordMapper patientFollowUpRecordMapper ;
    @Autowired
    private ImWxMessageService imWxMessageService ;
    @Autowired
    private PatientTaskHistoryMapper patientTaskHistoryMapper ;
    @Autowired
    private MedicalReportService medicalReportService;
    @Autowired
    private IFollowUpService followUpService;


    /**
     * 终止就诊人随访 消费者方法
     * @param message 消息内容
     */
    @Override
    public void onMessage(RedisMessage<BusPatientFamilyVo> message)  {
        try{
            BusPatientFamilyVo patientFamily = message.getBody();
            //获取用户所有进行中的随访记录
            PatientFollowUpRecordQuery recordQuery = new PatientFollowUpRecordQuery();
            recordQuery.setHospitalId(patientFamily.getHospitalId());
            recordQuery.setIsDelete(YesNoEnum.NO.getCode());
            recordQuery.setStatusList(Arrays.asList(FollowUpJoinStatusEnum.INVITING.getIndex(), FollowUpJoinStatusEnum.FOLLOWUPING.getIndex()));
            recordQuery.setPatientId(patientFamily.getId());
            recordQuery.setUserId(patientFamily.getPatientId());
            List<PatientFollowUpRecord> records = patientFollowUpRecordMapper.getList(recordQuery);
            //终止随访
            for (PatientFollowUpRecord record : records) {
                followUpService.terminateFollowUp(record.getId());
            }
        }catch (Exception ex) {
            logger.error("TerminateFollowUpConsumer failed : " , ex);
        }
    }

}
