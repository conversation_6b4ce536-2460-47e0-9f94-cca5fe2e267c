package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description 终止人类型：1.医院后台 2.医生
 */
public enum FollowUpTerminatorTypeEnum {

    ADMIN(1, "admin", "医院后台"),
    DOCTOR(2, "doctor", "医生"),
    SELF(3, "doctor", "患者本人");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpTerminatorTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpTerminatorTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpTerminatorTypeEnum[] metaArr = FollowUpTerminatorTypeEnum.values();
        for (FollowUpTerminatorTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpTerminatorTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpTerminatorTypeEnum[] metaArr = FollowUpTerminatorTypeEnum.values();
        for (FollowUpTerminatorTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
