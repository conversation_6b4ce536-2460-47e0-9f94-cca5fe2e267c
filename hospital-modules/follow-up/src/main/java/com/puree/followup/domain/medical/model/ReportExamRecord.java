package com.puree.followup.domain.medical.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.followup.api.model.RecordSourceEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @ClassName ReportExam
 * <AUTHOR>
 * @Description 体检报告检查项记录表
 * @Date 2024/5/13 17:52
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportExamRecord {
    /**
     * 主键
     */
    private Long id;
    /**
     * 检查项目名称;如：血细胞分析
     */
    private String subject;
    /**
     * 检查时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date examDate;
    /**
     * 检查结论
     */
    private String summary;
    /**
     * 检查项具体指标json串
     */
    private String examItems;
    /**
     * 文件路径
     */
    private String fileUrl;
    /**
     * 医院ID
     */
    private Long hospitalId;
    /**
     * 患者ID
     */
    private Long patientId;
    /**
     * 患者身份证号
     */
    private String patientIdNumber;
    /**
     * 体检数据记录ID
     */
    private Long recordId;
    /**
     * 来源类型;DATA_PUSH 数据推送，PATIENT_ADDED 患者添加，BACKGROUND_ADD 后台添加或医生添加，DOCTOR_ADDED 医生添加
     */
    private RecordSourceEnum source;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除标记;0未删除1已删除
     */
    private Boolean isDelete;
    /**
     * 检测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
    /**
     * 机构ID
     */
    private String orgId;
    /**
     * 机构名称
     */
    private String orgName;
}
