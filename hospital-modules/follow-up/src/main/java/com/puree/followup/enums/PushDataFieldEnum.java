package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/5/27 11:55
 * @Description  患者报告 字段枚举
 */
public enum PushDataFieldEnum {

    REPORT_AND_SUMMARY(0, "体检报告+小结", "summary", "summary"),
    REPORT_AND_SUGGEST(1, "体检报告+总检建议", "suggest" , "suggest"),
    REPORT_AND_ITEM(2, "体检报告+二级指标", "result", "`desc`"),
    ITEM_AND_SUMMARY(3, "一级指标+小结", "summary", "summary"),
    ITEM(4, "一级指标+二级指标", "result", "desc");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String inspectField;

    @Getter
    @Setter
    private String examField;

    PushDataFieldEnum(Integer index, String name, String inspectField, String examField) {
        this.index = index;
        this.name = name;
        this.inspectField = inspectField;
        this.examField = examField;
    }

    public static PushDataFieldEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        PushDataFieldEnum[] metaArr = PushDataFieldEnum.values();
        for (PushDataFieldEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static PushDataFieldEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        PushDataFieldEnum[] metaArr = PushDataFieldEnum.values();
        for (PushDataFieldEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
