package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者随访分项表 DTO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientFollowUpItemRecordDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 分项开始时间
     */
    private Date beginDay;


    /**
     * 分项结束时间（通过计算得出的分项结束时间）
     */
    private Date endDay;

    /**
     * 分项结束/终止时间（指用户操作的时间）
     */
    private Date endDate;

    /**
     * 终止人类型：1.医院后台 2.医生
     */
    private Integer terminatorType;

    /**
     * 终止人
     */
    private String terminator;

    /**
     * 分项状态：1.进行中 2.已结束 3.提前终止
     */
    private Integer itemStatus;

    /**
     * begin_day所在周的第一天
     */
    private Date firstDayOfJoinWeek;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

}