package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/8/14 17:55
 * @Description  预警状态：undone.未处理 done.已处理
 */
public enum FollowUpWarnStatusEnum {

    UNDONE(0, "undone", "未处理"),
    DONE(1, "done", "已处理");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpWarnStatusEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpWarnStatusEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpWarnStatusEnum[] metaArr = FollowUpWarnStatusEnum.values();
        for (FollowUpWarnStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpWarnStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpWarnStatusEnum[] metaArr = FollowUpWarnStatusEnum.values();
        for (FollowUpWarnStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
