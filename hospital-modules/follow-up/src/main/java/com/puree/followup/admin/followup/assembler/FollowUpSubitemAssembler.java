package com.puree.followup.admin.followup.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.puree.followup.admin.followup.mapper.FollowUpItemMapper;
import com.puree.followup.domain.followup.context.FollowUpProcessContext;
import com.puree.followup.domain.followup.model.FollowUpItem;
import com.puree.followup.domain.followup.vo.FollowUpItemVO;
import com.puree.followup.enums.FollowUpExecuteTypeEnum;
import com.puree.followup.enums.FollowUpIntervalTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 随访分项信息组装
 * <AUTHOR>
 * @date 2025/1/4 14:54
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FollowUpSubitemAssembler implements FollowUpDataAssembler<List<FollowUpItemVO>> {

    private final FollowUpItemMapper followUpItemMapper;
    private final FollowUpItemTaskAssembler followUpItemTaskAssembler;

    @SuppressWarnings("ConstantConditions")
    @Override
    public List<FollowUpItemVO> assemble(FollowUpProcessContext context) {

        //获取分项列表
        List<FollowUpItemVO> itemList = new ArrayList<>();
        FollowUpItem itemQuery = new FollowUpItem();
        itemQuery.setFollowUpId(context.getFollowUp().getId());
        itemQuery.setIsDelete(YesNoEnum.NO.getCode());
        List<FollowUpItem> items = followUpItemMapper.getList(itemQuery);
        items.forEach(item -> {
            FollowUpItemVO vo = BeanUtil.copyProperties(item, FollowUpItemVO.class);
            vo.setBeginDay(vo.getBeginDay() == 0 ? null : vo.getBeginDay());
            vo.setEndDay(vo.getEndDay() == 0 ? null : vo.getEndDay());
            vo.setExecuteType(FollowUpExecuteTypeEnum.getByIndex(item.getExecuteType()).getName());
            vo.setIntervalType(FollowUpIntervalTypeEnum.getByIndex(item.getIntervalType()).getName());
            context.setFollowUpItemVO(vo);
            itemList.add(followUpItemTaskAssembler.assemble(context));
        });
        return itemList;
    }

}
