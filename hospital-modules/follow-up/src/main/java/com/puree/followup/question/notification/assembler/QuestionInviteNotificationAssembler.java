package com.puree.followup.question.notification.assembler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.followup.question.domain.dto.QuestionEventBusinessDTO;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.business.api.model.BusPartnersVO;
import com.puree.hospital.common.notification.assembler.INotificationAssembler;
import com.puree.hospital.common.notification.constant.TemplateMessageConstants;
import com.puree.hospital.common.notification.domain.bo.WxOfficialAccountTemplateData;
import com.puree.hospital.common.notification.domain.bo.WxUniAppTemplateData;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 *  问卷邀请通知组装器
 * <AUTHOR>
 * @date 2025/3/21 10:50
 */
@Getter
public class QuestionInviteNotificationAssembler implements INotificationAssembler {

    private final QuestionInviteEvent event;

    private final QuestionEventBusinessDTO businessDTO;

    public QuestionInviteNotificationAssembler(QuestionInviteEvent event, QuestionEventBusinessDTO businessDTO) {
        this.event = event;
        this.businessDTO = businessDTO;
    }

    /**
     * 获取微信公众号模板参数
     *
     * @return 微信公众号模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxOfficialAccountTemplateParam() {
        Map<String, WxOfficialAccountTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.THING2, new WxOfficialAccountTemplateData(StrUtil.isBlank(businessDTO.getDoctorName()) ?
                TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME : businessDTO.getDoctorName()));
        templateParam.put(TemplateMessageConstants.TIME3, new WxOfficialAccountTemplateData(DateUtil.format(event.getEventTime(), DatePattern.NORM_DATETIME_FORMAT)));
        templateParam.put(TemplateMessageConstants.CONST4, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_EVENT_QUESTION));
        return convert(templateParam);
    }

    /**
     * 获取微信小程序模板参数
     *
     * @return 小程序模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxUniAppTemplateParam() {
        Map<String, WxUniAppTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.NAME3, new WxUniAppTemplateData(StrUtil.isBlank(businessDTO.getDoctorName()) ?
                TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME : businessDTO.getDoctorName()));
        templateParam.put(TemplateMessageConstants.THING2, new WxUniAppTemplateData(TemplateMessageConstants.QUESTION_WX_UNI_UNIFIED_MSG));
        templateParam.put(TemplateMessageConstants.THING4, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_EVENT_QUESTION));
        return convert(templateParam);
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxOfficialAccountMsgKey() {
        return TemplateMessageConstants.WX_OFFICIAL_ACCOUNT_QUESTION_MSG_KEY;
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxUniAppMsgKey() {
        return TemplateMessageConstants.WX_UNI_APP_QUESTION_MSG_KEY;
    }

    /**
     * 获取微信公众号重定向页面
     *
     * @param wechatConfig 医院微信公众号配置
     * @param partnersVO   合作机构方信息
     * @return 重定向页面
     */
    @Override
    public String getWxOfficialAccountRedirectPage(BusHospitalWechatConfig wechatConfig, BusPartnersVO partnersVO) {
        return getQuestionDefaultRedirectPage();
    }

    /**
     * 获取微信小程序重定向页面
     *
     * @return 重定向页面
     */
    @Override
    public String getWxUniAppRedirectPage(BusHospitalWechatConfig wechatConfig) {
        return getQuestionDefaultRedirectPage();
    }

    /**
     *  获取问卷默认的跳转链接
     * @return  跳转链接
     */
    private String getQuestionDefaultRedirectPage() {
        if (businessDTO.isContentIsTooLong()) {
            // 患者填写问卷记录表Id + 患者ID
            return String.format(TemplateMessageConstants.QUESTION_OVER_URL, businessDTO.getPatientQuestionRecordId(), businessDTO.getPatientId());
        } else {
            return String.format(TemplateMessageConstants.QUESTION_URL, businessDTO.getPatientQuestionRecordId(), businessDTO.getPatientId());
        }
    }
}
