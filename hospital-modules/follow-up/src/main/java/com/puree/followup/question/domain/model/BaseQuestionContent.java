package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: BaseQuestionContent
 * @Date 2024/6/5 14:35
 * <AUTHOR> jian
 * @Description: 问卷试题
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class BaseQuestionContent {

    /**
     * ID
     */
    private Long id ;

    /**
     * 题目
     */
    private String name ;

    /**
     * 题目描述
     */
    private String desc ;

    /**
     * 是否必填
     */
    private Integer isRequired ;

    /**
     * 0-无  1-单选题 2-多选题    3-单行文本 4-多行文本 5-多行填空    6-量表NSP  7-附件
     * QuestionTypeEnum
     */
    private String type ;

    /**
     * 表示一道 单选题 或者 一道多选题 ; 多个选择项，所以用list
     */
    private List<ChooseQuestion> chooseQuestionList ;

    /**
     * 表示一道 单行文本 或者 多行文本 或者 多行填空
     */
    private FillQuestion fillQuestion ;

    /**
     * 表示一道 量表NSP
     */
    private NspQuestion nspQuestion ;

    /**
     * 表示一道上传附件题目
     */
    private AttachmentQuestion attachmentQuestion ;

    /**
     * 题目是否可见 0-不可见   1-可见
     */
    private Integer isVisible ;

    private List<BaseQuestionContent> children = new ArrayList<>();



}
