package com.puree.followup.question.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @ClassName: PatientQuestionDTO
 * @Date 2024/6/21 11:28
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
public class PatientQuestionDTO {

    private Long hospitalId ;
    private Long questionId ;
    private Long userId ;
    private Long patientId ;
    private Integer fillStatus ;
    private List<Integer> fillStatusList ;
    private Integer isDelete ;

    private Integer pageSize;
    private Integer pageNum;

    private Long id ;

    /**
     * 问卷答案
     */
    private String answerContent ;

    private Long doctorId ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date measureTime ;

    /**
     * 患者手机号，名字
     */
    private String patientInfo ;

    /**
     * 随访id
     */
    private Long followUpId ;

    /**
     * 随访分项id
     */
    private Long followUpItemId ;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginDate ;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime ;


    //-----------------------下面为随访相关字段-----------------------------
    /**
     * 事件执行历史记录id   也即是patient_task_event_history表的id
     */
    private Long eventHistoryId;

    /**
     * 患者随访分项记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemRecordId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 事件ID
     */
    private Long eventId;

    /**
     * 事件提醒状态：0.未提醒 1.已提醒
     */
    private String eventRemindStatus;

    /**
     * 事件完成状态：0.未开始 1.消息未读/问卷未填写 2.消息已读/问卷已填写 3.已过期 4.已终止
     */
    private String eventFinishStatus;

    /**
     * 该事件所属任务的开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date taskBeginDay;

    //-----------------------结束-----------------------------



}
