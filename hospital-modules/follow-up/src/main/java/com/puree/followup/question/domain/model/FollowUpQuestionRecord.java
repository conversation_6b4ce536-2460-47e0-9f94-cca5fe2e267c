package com.puree.followup.question.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @ClassName: FollowUpQuestionRecord
 * @Date 2024/6/6 9:39
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpQuestionRecord {

    /**
     * ID
     */
    private Long id;

    /**
     * questionId
     */
    private Long questionId ;

    /**
     * followUpId
     */
    private Long followUpId;

    /**
     * itemId
     */
    private Long itemId;

    /**
     * 随访-分项-任务-event id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long eventId ;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0;





}
