package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  患者随访状态：0.邀请中 1.随访中 2.已完成 3.提前终止 4.已拒绝
 */
public enum FollowUpJoinStatusEnum {

    INVITING(0, "inviting", "邀请中"),
    FOLLOWUPING(1, "followuping", "随访中"),
    FINISHED(2, "finished", "已完成"),
    TERMINATED(3, "terminated", "提前终止"),
    REFUSED(4, "refused", "已拒绝");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;


    FollowUpJoinStatusEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpJoinStatusEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpJoinStatusEnum[] metaArr = FollowUpJoinStatusEnum.values();
        for (FollowUpJoinStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpJoinStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpJoinStatusEnum[] metaArr = FollowUpJoinStatusEnum.values();
        for (FollowUpJoinStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
