package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description 分项执行时间的类型：0.全周期 1.加入随访后第X至Y天 2.通过事件加入后至Y天
 */
public enum FollowUpExecuteTypeEnum {

    LONG_TERM(0, "longTerm", "全周期"),
    JOIN_IN(1, "joinIn", "加入随访后第X至Y天"),
    EVENT_JOIN_IN(2, "eventJoinIn", "通过事件加入后至Y天");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpExecuteTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpExecuteTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpExecuteTypeEnum[] metaArr = FollowUpExecuteTypeEnum.values();
        for (FollowUpExecuteTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpExecuteTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpExecuteTypeEnum[] metaArr = FollowUpExecuteTypeEnum.values();
        for (FollowUpExecuteTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
