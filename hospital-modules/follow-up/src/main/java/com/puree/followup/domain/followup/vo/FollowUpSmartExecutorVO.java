package com.puree.followup.domain.followup.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.followup.config.SmartExecutorConditionTypeHandler;
import com.puree.followup.config.TaskEventTypeHandler;
import com.puree.followup.domain.followup.model.SmartExecutorCondition;
import com.puree.followup.domain.followup.model.TaskEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 随访分项任务配置表 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpSmartExecutorVO {

    /**
     * 智能执行ID
     */
    private Long id;

    /**
     * 智能执行排序
     */
    private Integer sortNum;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 医院ID（运营平台为0）
     */
    private Long hospitalId;

    /**
     * 智能执行开关： enable:满足任一 all:满足全部
     */
    private String executorSwitch;

    /**
     * 数据源的类型：questionnaire.问卷数据 healthRecord.健康档案
     */
    private String dataSourceType;

    /**
     * 问卷id
     */
    private Long questionnaireId;

    /**
     * 问卷名称
     */
    private String questionnaireName;

    /**
     * 健康档案的时效性：x天内的患者最新指标
     */
    private Integer validDays;

    /**
     * 预警启用标识：false.禁用 true.启用
     */
    private Boolean warnSwitch;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 执行事件列表
     */
    private List<TaskEvent> events;

    /**
     * 条件列表
     */
    private List<SmartExecutorCondition> conditions;

}