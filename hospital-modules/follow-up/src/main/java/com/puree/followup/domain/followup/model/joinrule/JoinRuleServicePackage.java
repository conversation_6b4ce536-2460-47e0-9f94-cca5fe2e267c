package com.puree.followup.domain.followup.model.joinrule;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 入组方式-服务包
 */

@Data
public class JoinRuleServicePackage {

    /**
     * 前端设置条件的标识字段
     */
    private String settingType;

    /**
     * 服务包id
     */
    private Long id;
    private String servicePackType;
    private String servicePackName;
    private String servicePackImg;
    private String servicePackVideo;
    private Double servicePackPrice;
    private String diseaseType;
    private String suitablePopulation;
    private Long serviceCycle;
    private Long salesVolume;
    private String detailedIntroduction;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfTime;
    private String status;
    private Long leaderId;
    private String doctorName;
    private List<String> photos;
    private Long groupId;
    private String groupName;
    private String serviceCycleLabel;
    private Long workingGroupId;
    private String servicePeriodAmount;
    private Integer totalNumber;
    private Long roleId;
    private String workGroupStatus;
    private String partnersName;
    private String whetherDelete;
}
