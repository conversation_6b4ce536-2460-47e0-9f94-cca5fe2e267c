package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/5/24 17:55
 * @Description  入组规则中的婚姻状况枚举：0 未婚 1 已婚
 */
public enum RuleMaritalEnum {

    SINGLE(0, "未婚"),
    MARRIED(1, "已婚");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    RuleMaritalEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static RuleMaritalEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        RuleMaritalEnum[] metaArr = RuleMaritalEnum.values();
        for (RuleMaritalEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static RuleMaritalEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        RuleMaritalEnum[] metaArr = RuleMaritalEnum.values();
        for (RuleMaritalEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
