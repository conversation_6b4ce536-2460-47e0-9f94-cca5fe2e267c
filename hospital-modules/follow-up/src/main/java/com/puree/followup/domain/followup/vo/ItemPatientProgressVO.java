package com.puree.followup.domain.followup.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 患者分项完成进度 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ItemPatientProgressVO {

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 分项名称
     */
    private String name;

    /**
     * 分项下的任务总数
     */
    private Integer totalTasks;

    /**
     * 分项下已进行任务数
     */
    private Integer hasProcessTasks;



}