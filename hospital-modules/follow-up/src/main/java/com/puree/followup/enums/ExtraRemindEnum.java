package com.puree.followup.enums;

import cn.hutool.core.util.StrUtil;

/**
 * @ClassName: ExtraRemindEnum
 * @Date 2024/4/26 9:07
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum ExtraRemindEnum {

    WE_CHAT_PUBLIC(1, "公众号消息") ,
    MESSAGE(2,"短信"),
    VOICE_PHONE(3, "语音电话")
    ;


    private Integer index ;
    private String desc;

    public Integer getIndex() {
        return index;
    }

    public String getDesc() {
        return desc;
    }

    ExtraRemindEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public static ExtraRemindEnum getExtraRemindEnumByIndex(Integer index) {
        if (null==index) {
            return null;
        }
        ExtraRemindEnum[] metaArr = ExtraRemindEnum.values();
        for (ExtraRemindEnum extraRemindEnum : metaArr) {
            if (extraRemindEnum.getIndex().equals(index)) {
                return extraRemindEnum;
            }
        }
        return null;
    }

    public static ExtraRemindEnum getExtraRemindEnumByDesc(String desc) {
        if (StrUtil.isBlank(desc)) {
            return null;
        }
        ExtraRemindEnum[] metaArr = ExtraRemindEnum.values();
        for (ExtraRemindEnum extraRemindEnum : metaArr) {
            if (extraRemindEnum.getDesc().equals(desc)) {
                return extraRemindEnum;
            }
        }
        return null;
    }




}
