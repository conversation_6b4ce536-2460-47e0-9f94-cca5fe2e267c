package com.puree.followup.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * 自定义线程池
 * <AUTHOR>
 * @date 2024/4/26 17:32
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {

    @Bean("taskExecutor")
    public Executor asyncServiceExecutor(){
        //核心线程数设置的建议：
        //如果任务量较少且每个任务都非常耗时，可以适当减少核心线程数以节省资源，例如将corePoolSize设置为2-3；
        //如果有大量的耗时短的任务，可以适当增加核心线程数，例如将corePoolSize设置为10-20等。
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int processNum = Runtime.getRuntime().availableProcessors();
        int corePoolSize = (int) (processNum / (1 - 0.2));
        int maxPoolSize = (int) (processNum / (1 - 0.5));
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setKeepAliveSeconds(3 * 60);
        executor.setThreadNamePrefix("follow-up-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }

}
