package com.puree.followup.domain.followup.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者随访备注记录表
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientFollowUpRemark implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 备注记录的内容
     */
    private String content;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

}