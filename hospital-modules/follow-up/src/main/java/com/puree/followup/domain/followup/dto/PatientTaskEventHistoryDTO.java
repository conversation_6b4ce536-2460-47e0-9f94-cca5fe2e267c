package com.puree.followup.domain.followup.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.puree.followup.domain.followup.model.TaskEvent;
import com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 患者随访事件执行历史表 DTO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
public class PatientTaskEventHistoryDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 事件id列表
     */
    private List<Long> ids;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 患者随访分项记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemRecordId;

    /**
     * 任务执行ID
     */
    private Long taskHistoryId;

    /**
     * 任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;
    /**
     * 事件ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long eventId;
    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 执行事件
     */
    private TaskEvent events;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 事件执行时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date eventExecuteTime;

    /**
     * 事件提醒时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date remindTime;

    /**
     * 该事件所属任务的开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date taskBeginDay;


    /**
     * 事件提醒状态：0.未提醒 1.已提醒
     */
    private String eventRemindStatus;

    /**
     * 事件完成状态：0.未开始 1.消息未读/问卷未填写 2.消息已读/问卷已填写 3.已过期 4.已终止
     */
    private String eventFinishStatus;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 随访中的问卷填写状态：0-邀请中  1-填写中  2-完成 3-已拒绝
     * FillQuestionStatusEnum
     */
    private Integer fillStatus ;


}