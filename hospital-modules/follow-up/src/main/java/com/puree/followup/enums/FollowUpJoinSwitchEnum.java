package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  入组开关：0.关闭 1.开启(任一) 2.满足全部
 */
public enum FollowUpJoinSwitchEnum {

    CLOSE(0, "close", "关闭"),
    ENABLE(1, "enable", "开启(任一)"),
    ALL(2, "all", "满足全部");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;


    FollowUpJoinSwitchEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpJoinSwitchEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpJoinSwitchEnum[] metaArr = FollowUpJoinSwitchEnum.values();
        for (FollowUpJoinSwitchEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpJoinSwitchEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpJoinSwitchEnum[] metaArr = FollowUpJoinSwitchEnum.values();
        for (FollowUpJoinSwitchEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
