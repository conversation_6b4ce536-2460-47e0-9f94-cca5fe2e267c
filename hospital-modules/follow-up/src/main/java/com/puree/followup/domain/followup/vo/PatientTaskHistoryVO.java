package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 患者随访任务执行历史表 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientTaskHistoryVO implements Comparable<PatientTaskHistoryVO>{

    /**
     * ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 随访名称
     */
    private String followUpName;

    /**
     * 患者随访记录ID
     */
    private Long followUpRecordId;

    /**
     * 患者随访分项记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long itemRecordId;

    /**
     * 任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 任务名称
     */
    private String showData;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 分项名称
     */
    private String itemName;

    /**
     * 分项ID
     */
    private Long itemId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 任务开始时间
     */
//    @JsonFormat(pattern = "yyyy-MM-dd")
    private String beginDay;

    /**
     * 任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endDay;

    /**
     * 该字段用于方便统计，表示当前任务是否为长期随访下的任务
     */
    private Boolean isLongTerm = false;

    /**
     * 任务提醒时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date remindTime;

    /**
     * 任务实际完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskFinishTime;

    /**
     * 任务提醒状态：0.未提醒 1.已提醒
     */
    private String taskRemindStatus;

    /**
     * 任务完成状态：0.未开始(即将开始，未来的) 1.进行中(未完成、待完成，今天的) 2.已完成 3.已过期 4.已终止
     */
    private String taskStatus;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 删除标记
     */
    private Integer isDelete;

    /**
     * 随访事件执行历史列表
     */
    private List<PatientTaskEventHistoryVO> children;

    @Override
    public int compareTo(PatientTaskHistoryVO other) {
        return this.getRemindTime().compareTo(other.getRemindTime());
    }

    /**
     * 患者随访任务是否为虚拟生成的(这里我们约定：存储在patient_task_history表中的患者随访任务为非虚拟的，用代码生成的未来任务是虚拟的)：false.否  true.是
     */
    private Boolean isVirtualTask = false;

}