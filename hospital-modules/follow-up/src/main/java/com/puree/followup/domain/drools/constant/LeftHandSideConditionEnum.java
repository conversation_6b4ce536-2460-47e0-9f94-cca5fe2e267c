package com.puree.followup.domain.drools.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName LHSEnum
 * <AUTHOR>
 * @Description Left Hand Side condition 规则条件部分枚举
 * @Date 2024/4/10 12:11
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum LeftHandSideConditionEnum {
    EVAL(1, "eval", "eval ({})","评估（类似于 IF）"),
    NOT(2, "not", "not","不满足条件"),
    EXISTS(3, "exists", "exists ({})","满足条件（注意，如果不使用，且写入了多个满足条件的对象，则对应的规则 then 部分会被执行多次）"),
    //暂时不使用
//    EXTENDS(6, "继承", "extends "),
    ;

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;
}
