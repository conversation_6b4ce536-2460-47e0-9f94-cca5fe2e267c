package com.puree.followup.queue.consumer;

import com.alibaba.fastjson.JSON;
import com.puree.followup.admin.medical.service.MedicalReportService;
import com.puree.followup.domain.event.MedicalReportUploadEvent;
import com.puree.hospital.common.redis.mq.RedisMessage;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 体检报告记录 消费者
 * @ClassName: MedicalReportConsumer
 * @Date 2024/7/8 19:59
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
public class MedicalReportConsumer extends RedisStreamConsumer<MedicalReportUploadEvent> {

    private static final Logger logger = LoggerFactory.getLogger(MedicalReportConsumer.class);

    @Autowired
    private MedicalReportService medicalReportService;

    /**
     * 患者体检报告 消费者方法
     * @param message 消息内容
     */
    @Override
    public void onMessage(RedisMessage<MedicalReportUploadEvent> message)  {
        try{
            MedicalReportUploadEvent event = message.getBody();
            logger.info("体检报告数据解析事件监听：{}", event.getReportRecordId());
            medicalReportService.dataPushHandler(event.getReportRecordId());
        }catch (Exception ex) {
            logger.error("MedicalReportConsumer failed : " , ex);
        }
    }

}
