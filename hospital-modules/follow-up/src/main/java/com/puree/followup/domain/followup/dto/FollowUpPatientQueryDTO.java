package com.puree.followup.domain.followup.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 随访患者总览查询 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpPatientQueryDTO {

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 患者姓名/手机号
     */
    private String patientKeyword;

    /**
     *  患者随访状态：inviting:邀请中 followuping:随访中 finished:已完成 terminated:提前终止 refused:已拒绝
     */
    private String joinStatusCode;

    /**
     * 患者随访状态：0.邀请中 1.随访中 2.已完成 3.提前终止 4.已拒绝
     */
    private Integer joinStatus;

    /**
     *  入组类型：autoJoinIn:自动添加 autoInvite:自动邀请 manualAdd:手动添加 manualInvite:手动邀请
     */
    private String joinTypeCode;

    /**
     * 入组类型：0.直接添加 1.邀请入组(需患者同意) 2.手动直接添加 3.手动邀请入组(需患者同意)
     */
    private Integer joinType;

    /**
     * 入组日期起始值
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinBeginTime;

    /**
     * 入组日期结束值
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinEndTime;

    /**
     * 终止/完成日期起始值
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishedBeginTime;

    /**
     * 终止/完成日期结束值
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finishedEndTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

    /**
     * 医院id
     */
    private Long hospitalId;

    /**
     * patientId列表
     */
    private List<Long> patientIds;

    /**
     * 每页多少个
     */
    private Integer pageSize;

    /**
     * 多少页
     */
    private Integer pageNum;

    /**
     * 患者入组的来源：0.默认入组  1.诊断入组  2.数据推送入组 3.商品入组 4.药品入组 5.服务包入组 6.健康档案入组 7.所属医生入组
     */
    private Integer sourceType;

    /**
     * 群组id（服务包和处方诊断入组时所属的群组）
     */
    private Long groupId;

}