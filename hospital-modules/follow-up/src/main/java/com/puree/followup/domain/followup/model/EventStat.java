package com.puree.followup.domain.followup.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 *
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class EventStat {

    /**
     * 事件总数(不包含已终止的事件)
     */
    private Integer totalEvents = 0;

    /**
     * 即将开始事件数
     */
    private Integer iscomingEvents = 0;

    /**
     * 待完成事件数
     */
    private Integer ongoingEvents = 0;

    /**
     * 已完成事件
     */
    private Integer finishedEvents = 0;

    /**
     * 已过期事件数
     */
    private Integer hasExpiredEvents = 0;

    /**
     * 已终止事件数
     */
    private Integer terminatedEvents = 0;

}