package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 患者随访记录表 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientListVO {

    /**
     * 患者随访记录ID
     */
    private Long id;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 账号id
     */
    private Long userId;

    /**
     * 患者id
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者手机号
     */
    private String patientPhone;

    /**
     * 患者年龄
     */
    private Integer patientAge;

    /**
     * 患者性别
     */
    private String patientSex;

    /**
     * 入组/接受邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    /**
     * 入组类型：autoJoinIn:自动添加 autoInvite:自动邀请 manualAdd:手动添加 manualInvite:手动邀请
     */
    private String joinType;

    /**
     * 患者随访状态：inviting:邀请中 followuping:随访中 finished:已完成 terminated:提前终止 refused:已拒绝
     */
    private String joinStatus;

    /**
     * 医生id
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private Long doctorName;

    /**
     * 备注
     */
    private String remarkRecord;

    /**
     * 终止/完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inviteTime;

    /**
     * 随访周期类型(到期时间)：longTerm:长期、joinIn:加入随访X天后结束
     */
    private String expireType;

    /**
     * 任务总数
     */
    private Integer totalTasks;

    /**
     * 进行中的任务数
     */
    private Integer ongoingTasks;

    /**
     * 已完成任务数
     */
    private Integer finishedTasks;

    /**
     * 已过期任务数
     */
    private Integer hasExpiredTasks;

    /**
     * 随访进度(百分比)
     */
    private BigDecimal finishedFollowUpRate;

    /**
     * 任务完成率(百分比)
     */
    private BigDecimal finishedTaskRate;

    /**
     * 未处理的预警数
     */
    private Integer undealtWarns;
}