package com.puree.followup.notification.assembler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.puree.followup.domain.followup.dto.FollowUpEventBusinessDTO;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.business.api.model.BusPartnersVO;
import com.puree.hospital.common.notification.constant.TemplateMessageConstants;
import com.puree.hospital.common.notification.domain.bo.WxOfficialAccountTemplateData;
import com.puree.hospital.common.notification.domain.bo.WxUniAppTemplateData;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 随访加入通知组装器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 18:41
 */
public class FollowUpJoinNotificationAssembler extends BaseFollowUpNotificationAssembler {

    public FollowUpJoinNotificationAssembler(BaseFollowUpEvent event, FollowUpEventBusinessDTO businessDTO) {
        super(event, businessDTO);
    }

    /**
     * 获取微信公众号模板参数
     *
     * @return 微信公众号模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxOfficialAccountTemplateParam() {
        Map<String, WxOfficialAccountTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.THING2, new WxOfficialAccountTemplateData(TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME));
        templateParam.put(TemplateMessageConstants.TIME3, new WxOfficialAccountTemplateData(DateUtil.format(getEvent().getEventTime(), DatePattern.NORM_DATETIME_FORMAT)));
        templateParam.put(TemplateMessageConstants.CONST4, new WxOfficialAccountTemplateData(TemplateMessageConstants.PATIENT_JOIN_FOLLOW_UP_TOPIC));
        return convert(templateParam);
    }

    /**
     * 获取微信小程序模板参数
     *
     * @return 小程序模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxUniAppTemplateParam() {
        Map<String, WxUniAppTemplateData> templateParam = new HashMap<>();
        templateParam.put(TemplateMessageConstants.NAME3, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_DEFAULT_DOCTOR_NAME));
        templateParam.put(TemplateMessageConstants.THING2, new WxUniAppTemplateData(TemplateMessageConstants.FOLLOW_UP_WX_UNI_UNIFIED_MSG));
        templateParam.put(TemplateMessageConstants.THING4, new WxUniAppTemplateData(assemblerRemark()));
        return convert(templateParam);
    }

    /**
     *  组装备注信息
     */
    private String assemblerRemark() {
        String remark;
        if (Objects.isNull(getBusinessDTO().getJoinType())) {
            remark = TemplateMessageConstants.CONSULTATION_REMIND_UNIFIED_TITLE;
        } else if (getBusinessDTO().getJoinType().equals(0) || getBusinessDTO().getJoinType().equals(2)) {
            remark = TemplateMessageConstants.FOLLOW_UP_JOIN_MSG;
        } else if (getBusinessDTO().getJoinType().equals(1) || getBusinessDTO().getJoinType().equals(3)){
            remark = TemplateMessageConstants.FOLLOW_UP_INVITE_JOIN_MSG;
        } else {
            remark = TemplateMessageConstants.CONSULTATION_REMIND_UNIFIED_TITLE;
        }
        return remark;
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxOfficialAccountMsgKey() {
        return TemplateMessageConstants.WX_OFFICIAL_ACCOUNT_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxUniAppMsgKey() {
        return TemplateMessageConstants.WX_UNI_APP_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取微信公众号重定向页面
     *
     * @param wechatConfig 医院微信公众号配置
     * @param partnersVO   合作机构方信息
     * @return 重定向页面
     */
    @Override
    public String getWxOfficialAccountRedirectPage(BusHospitalWechatConfig wechatConfig, BusPartnersVO partnersVO) {
        return followUpJoinDefaultRedirectPage();
    }

    /**
     * 获取微信小程序重定向页面
     *
     * @return 重定向页面
     */
    @Override
    public String getWxUniAppRedirectPage(BusHospitalWechatConfig wechatConfig) {
        return followUpJoinDefaultRedirectPage();
    }

    /**
     * 默认跳转页面
     */
    private String followUpJoinDefaultRedirectPage() {
        return String.format(TemplateMessageConstants.FOLLOW_UP_EVENT_JOIN_URL, getBusinessDTO().getFollowUpId(), getBusinessDTO().getFollowUpRecordId(), getBusinessDTO().getPatientId(), getBusinessDTO().getHospitalId());
    }
}
