package com.puree.followup.question.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName: DuplicateClassificationDTO
 * @Date 2024/6/19 10:26
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
@Data
public class DuplicateClassificationDTO {

    /**
     *  问卷分类id
     */
    private Long id ;

    /**
     *  问卷分类名字
     */
    private String name ;

    private Integer dataType ;

    private Integer source ;

    private Long hospitalId ;

}
