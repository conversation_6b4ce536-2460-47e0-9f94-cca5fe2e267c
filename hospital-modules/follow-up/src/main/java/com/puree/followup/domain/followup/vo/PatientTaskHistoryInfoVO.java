package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 患者随访任务执行历史 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientTaskHistoryInfoVO {

    /**
     * 任务执行记录ID
     */
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 分项名称
     */
    private String subitemName;

    /**
     * 即将开始和待完成的任务条数
     */
    private Integer undoneTasks;

    /**
     * 事件名称
     */
    private List<PatientTaskEventHistoryListVO> eventList;

}