package com.puree.followup.service;

import com.puree.hospital.app.api.RemoteCommunicationMessageService;
import com.puree.hospital.common.api.constant.Constants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.ImTypeEnum;
import com.puree.hospital.im.api.RemoteImService;
import com.puree.hospital.im.api.model.FollowUpImWxDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * @ClassName: ImService
 * @Date 2024/6/20 15:32
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */

@Service
@RefreshScope
@Slf4j
public class ImService {

    @Autowired
    private RemoteImService remoteImService;
    @Autowired
    private RemoteCommunicationMessageService remoteCommunicationMessageService;


    /*
     * 伴伴 发送im , 包含 私聊， 群组
     */
    public void sendImMessage(FollowUpImWxDTO followUpImWxDTO) {

        if ( ImTypeEnum.ONE_TO_ONE.getIndex().equals( followUpImWxDTO.getImType() ) ) {
            // 私聊
            remoteImService.sendOneToOneMsg(followUpImWxDTO) ;
        }else {
            // 群组
            remoteImService.adminSendGroupMsg(followUpImWxDTO) ;
        }

    }

    /*
     * 医生往群组中发送问卷
     */
    public void doctorSendQuestion(FollowUpImWxDTO followUpImWxDTO){
        //往群组里面发问卷（不管是问诊群组还是服务包群组）之前，都需要提前校验该群组是否为静默状态(默认7天)，如果已静默则重新激活
        R r = remoteCommunicationMessageService.checkSilenceGroup(followUpImWxDTO.getGroupId(), followUpImWxDTO.getHospitalId());
        if(!Constants.SUCCESS.equals(r.getCode())){
            log.error("医生发送问卷--群组唤醒失败：followUpImWxDTO={}", followUpImWxDTO);
            return;
        }
        //非静默状态或成功唤醒的群组才发送消息
        remoteImService.doctorSendQuestionMsg(followUpImWxDTO) ;
    }


















}
