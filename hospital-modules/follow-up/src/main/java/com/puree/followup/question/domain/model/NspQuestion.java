package com.puree.followup.question.domain.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @ClassName: NspQuestion
 * @Date 2024/6/5 16:13
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class NspQuestion {

    private String nsp ;

    /**
     * 得分
     */
    private BigDecimal score ;


}
