package com.puree.followup.domain.drools.builder;

import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName RHSBuilder
 * <AUTHOR>
 * @Description 规则行动部分
 * @Date 2024/4/2 16:01
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class RHSBuilder {

    /**
     * 立即终止后面所有规则执行
     */
    private final static String HALT = "drools.halt();";
    /**
     * 是否满足全部条件，为 true 则为全部匹配，为 false 则为满足任一
     */
    private Boolean isAll = false;

    private String isNotAllExecute ;

    private static Integer count = 1;
    /**
     * 规则名称
     */
    private  String ruleName;
    /**
     * @Param
     * @Return java.lang.String
     * @Description 构建 RHS
     * <AUTHOR>
     * @Date 2024/4/28 11:09
     **/
    public String builderRHSString() {
        StrBuilder builder = new StrBuilder();
        if (isAll.equals(Boolean.FALSE)) {
            builder.append(StrPool.C_TAB).append(isNotAllExecute).append(StrPool.C_LF);
            builder.append(StrPool.C_TAB).append(HALT);
        }
        return builder.toString();
    }
}
