package com.puree.followup.notification;

import com.google.common.collect.Lists;
import com.puree.hospital.common.api.enums.ClientTypeEnum;
import com.puree.hospital.common.notification.queue.consumer.AbstractNotificationConsumerBuilder;
import com.puree.hospital.common.notification.queue.consumer.DefaultNotificationConsumer;
import com.puree.hospital.common.notification.service.ITemplateMessageService;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpJoinEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendImageEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendQuestionEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendRemindEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendTextEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 随访事件通知消费构建者
 * <AUTHOR>
 * @date 2025/3/21 10:11
 */
@Slf4j
@Component
public class FollowUpEventNotificationConsumerBuilder<E extends BaseFollowUpEvent> extends AbstractNotificationConsumerBuilder<E> {

    @Resource(name = "followUpWxOfficialAccountTemplateMessageService")
    private ITemplateMessageService<E> followUpWxOfficialAccountTemplateMessageService;

    @Resource(name = "followUpWxUniAppTemplateMessageService")
    private ITemplateMessageService<E> followUpWxUniAppTemplateMessageService;

    @Override
    protected ITemplateMessageService<E> getWxOfficialAccountMessageService() {
        return followUpWxOfficialAccountTemplateMessageService;
    }

    @Override
    protected ITemplateMessageService<E> getWxUniAppMessageService() {
        return followUpWxUniAppTemplateMessageService;
    }

    @Override
    protected ITemplateMessageService<E> getWxZnxybUniAppMessageService() {
        return null;
    }

    @Override
    public List<DefaultNotificationConsumer<E>> buildConsumerList() {
        List<DefaultNotificationConsumer<E>> consumers = Lists.newArrayList();
        for (ClientTypeEnum clientType : Lists.newArrayList(ClientTypeEnum.WX_OFFICIAL_ACCOUNT, ClientTypeEnum.WX_UNI_APP)) {
            ITemplateMessageService<E> messageService = super.getMessageService(clientType);
            if (messageService == null) {
                continue;
            }
            String group = getGroup(clientType);
            //1.随访加入事件
            consumers.add(new DefaultNotificationConsumer<>(group, FollowUpJoinEvent.TOPIC, messageService, FollowUpJoinEvent.class));
            //2.随访发送文本事件
            consumers.add(new DefaultNotificationConsumer<>(group, FollowUpSendTextEvent.TOPIC, messageService, FollowUpSendTextEvent.class));
            //3.随访发送图文事件
            consumers.add(new DefaultNotificationConsumer<>(group, FollowUpSendImageEvent.TOPIC, messageService, FollowUpSendImageEvent.class));
            //4.随访发送提醒事件
            consumers.add(new DefaultNotificationConsumer<>(group, FollowUpSendRemindEvent.TOPIC, messageService, FollowUpSendRemindEvent.class));
            //5.随访发送问卷事件
            consumers.add(new DefaultNotificationConsumer<>(group, FollowUpSendQuestionEvent.TOPIC, messageService, FollowUpSendQuestionEvent.class));
        }
        return consumers;
    }
}
