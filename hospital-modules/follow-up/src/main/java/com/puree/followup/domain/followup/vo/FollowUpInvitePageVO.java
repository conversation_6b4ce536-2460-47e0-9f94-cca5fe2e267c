package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 随访邀请页 VO
 * <AUTHOR>
 * @date 2024-05-22 11:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpInvitePageVO {

    /**
     * 随访ID
     */
    private Long id;

    /**
     * 随访记录
     */
    private Long recordId;

    /**
     * 随访名称
     */
    private String name;

    /**
     * 随访描述
     */
    private String desc;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 患者id
     */
    private Long userId;

    /**
     * 就诊人id
     */
    private Long patientId;

    /**
     * 患者随访状态：inviting.邀请中 followuping.随访中 finished.已完成 terminated.提前终止 refused.已拒绝
     */
    private String joinStatus;

    /**
     * 启用标识：false.否 true.是
     */
    private Boolean isEnable;

    /**
     * 发布标识：false.否 true.是
     */
    private Boolean isPublish;

}