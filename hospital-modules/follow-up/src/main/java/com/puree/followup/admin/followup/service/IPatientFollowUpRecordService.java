package com.puree.followup.admin.followup.service;

import com.puree.followup.domain.followup.dto.PatientFollowUpJoinRuleDTO;
import com.puree.followup.domain.followup.model.FollowUp;
import com.puree.followup.domain.followup.model.PatientFollowUpRecord;

/**
 * <AUTHOR>
 * @date 2024/4/8 15:53
 * @description 患者随访记录 服务类
 */
public interface IPatientFollowUpRecordService {



    void patientJoinFollowUpIm(PatientFollowUpRecord patientFollowUpRecord, FollowUp followUp) ;

    Boolean patientJoinInFollowUpWithAutoRule(PatientFollowUpJoinRuleDTO dto);

    Boolean terminateFollowUpWithRefund(PatientFollowUpJoinRuleDTO dto);
}
