package com.puree.followup.question.enums;

import cn.hutool.core.util.StrUtil;

/**
 * @ClassName: FillFormatEnum
 * @Date 2024/6/5 15:19
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum FillFormatEnum {

    NUMBER(0, "number", "数字") ,
    PHONE(1, "phone", "手机号") ,
    EMAIL(2, "email", "邮箱") ,
    ID_NO(3,"idNo", "身份证号码") ,
    DATE(4, "date", "日期选择器") ,
    TIME(5, "time", "时间选择器") ,
    DATE_TIME(6, "dateTime", "日期时间选择器") ,

    ;

    private Integer index;
    private String name;
    private String desc;

    FillFormatEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static FillFormatEnum getByName(String name) {
        if (StrUtil.isBlank(name) ) {
            return null;
        }
        FillFormatEnum[] metaArr = FillFormatEnum.values();
        for (FillFormatEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FillFormatEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FillFormatEnum[] metaArr = FillFormatEnum.values();
        for (FillFormatEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }



}
