package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  客户端来源：0.运营平台 1.医院后台 2.医生端 3.患者端
 */
public enum ClientSourceEnum {

    PLATFORM(0, "运营平台"),
    HOSPITAL(1, "医院后台"),
    DOCTOR(2, "医生端"),
    PATIENT(3, "患者端");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    ClientSourceEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static ClientSourceEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        ClientSourceEnum[] metaArr = ClientSourceEnum.values();
        for (ClientSourceEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static ClientSourceEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        ClientSourceEnum[] metaArr = ClientSourceEnum.values();
        for (ClientSourceEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
