package com.puree.followup.admin.followup.assembler;

import cn.hutool.core.bean.BeanUtil;
import com.puree.followup.admin.followup.mapper.FollowUpJoinRuleMapper;
import com.puree.followup.domain.followup.context.FollowUpProcessContext;
import com.puree.followup.domain.followup.model.FollowUpJoinRule;
import com.puree.followup.domain.followup.model.joinrule.JoinRuleDrugs;
import com.puree.followup.domain.followup.model.joinrule.JoinRuleGoods;
import com.puree.followup.domain.followup.model.joinrule.JoinRuleServicePackage;
import com.puree.followup.domain.followup.vo.FollowUpJoinRuleVO;
import com.puree.followup.enums.FollowUpJoinRuleTypeEnum;
import com.puree.followup.enums.FollowUpJoinSwitchEnum;
import com.puree.hospital.business.api.RemoteBusHospitalOfficinaService;
import com.puree.hospital.business.api.model.BusDirectoryDrugsVO;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.five.api.RemoteServicePackService;
import com.puree.hospital.five.api.model.BusFiveServicePackVO;
import com.puree.hospital.shop.api.RemoteShopGoodsService;
import com.puree.hospital.shop.api.model.BusShopGoods;
import com.puree.hospital.shop.api.model.dto.BusShopGoodsDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 随访入组方式信息组装
 * <AUTHOR>
 * @date 2025/1/4 14:54
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FollowUpJoinRuleAssembler implements FollowUpDataAssembler<List<FollowUpJoinRuleVO>> {

    private final FollowUpJoinRuleMapper followUpJoinRuleMapper;
    private final RemoteShopGoodsService remoteShopGoodsService;
    private final RemoteBusHospitalOfficinaService remoteBusHospitalOfficinaService;
    private final RemoteServicePackService remoteServicePackService;

    /**
     * 入组方式处理
     * @param context 上下文
     * @return List<FollowUpJoinRuleVO>
     */
    @Override
    public List<FollowUpJoinRuleVO> assemble(FollowUpProcessContext context) {
        //获取入组方式列表
        List<FollowUpJoinRuleVO> joinRuleList = new ArrayList<>();
        FollowUpJoinRule joinRuleQuery = new FollowUpJoinRule();
        joinRuleQuery.setFollowUpId(context.getFollowUp().getId());
        List<FollowUpJoinRule> joinRules = followUpJoinRuleMapper.getList(joinRuleQuery);
        joinRules.forEach(item -> joinRuleList.add(handleJoinRuleVO(item)));
        return joinRuleList;
    }

    /**
     * FollowUpJoinRule的VO处理
     * @param joinRule 随访入组方式
     * @return VO
     */
    @SuppressWarnings("ConstantConditions")
    private FollowUpJoinRuleVO handleJoinRuleVO(FollowUpJoinRule joinRule) {
        FollowUpJoinRuleVO vo = BeanUtil.copyProperties(joinRule, FollowUpJoinRuleVO.class);
        vo.setJoinType(FollowUpJoinRuleTypeEnum.getByIndex(joinRule.getJoinType()).getName());
        vo.setJoinSwitch(FollowUpJoinSwitchEnum.getByIndex(joinRule.getJoinSwitch()).getName());

        //商品信息查询
        if(FollowUpJoinRuleTypeEnum.GOODS.getName().equals(vo.getJoinType())
                && !CollectionUtils.isEmpty(vo.getRule().getJoinRuleGoods())){
            BusShopGoodsDTO query = new BusShopGoodsDTO();
            query.setHospitalId(SecurityUtils.getHospitalId());
            query.setStatus(YesNoEnum.YES.getCode());
            query.setGoodsIdList(vo.getRule().getJoinRuleGoods().stream().map(JoinRuleGoods::getId).collect(Collectors.toList()));
            List<BusShopGoods> result = remoteShopGoodsService.getGoodsByIds(query).getData();
            List<JoinRuleGoods> goodsList = new ArrayList<>();
            if(result != null) {
                goodsList = result.stream()
                        .map(goods -> BeanUtil.copyProperties(goods, JoinRuleGoods.class))
                        .collect(Collectors.toList());
            }
            vo.getRule().setJoinRuleGoods(goodsList);
        }

        //药品信息查询
        if(FollowUpJoinRuleTypeEnum.DRUGS.getName().equals(vo.getJoinType())
                && !CollectionUtils.isEmpty(vo.getRule().getJoinRuleDrug())){
            String ids = vo.getRule().getJoinRuleDrug().stream().map(JoinRuleDrugs::getDrugsId).map(Object::toString).collect(Collectors.joining(","));
            List<BusDirectoryDrugsVO> result = remoteBusHospitalOfficinaService.associatedDrugsIds(ids, SecurityUtils.getHospitalId()).getData();
            List<JoinRuleDrugs> drugList = new ArrayList<>();
            if(result != null) {
                drugList = result.stream()
                        .map(drug -> BeanUtil.copyProperties(drug, JoinRuleDrugs.class))
                        .collect(Collectors.toList());
            }
            vo.getRule().setJoinRuleDrug(drugList);
        }

        //服务包信息查询
        if(FollowUpJoinRuleTypeEnum.SERVICE_PACKAGE.getName().equals(vo.getJoinType())
                && !CollectionUtils.isEmpty(vo.getRule().getJoinRuleServicePackage())){
            String ids = vo.getRule().getJoinRuleServicePackage().stream().map(JoinRuleServicePackage::getId).map(Object::toString).collect(Collectors.joining(","));
            List<BusFiveServicePackVO> result = remoteServicePackService.getServicePacksByIds(ids).getData();
            List<JoinRuleServicePackage> servicePackages = new ArrayList<>();
            if(result != null) {
                servicePackages = result.stream()
                        .map(servicePack -> BeanUtil.copyProperties(servicePack, JoinRuleServicePackage.class))
                        .collect(Collectors.toList());
            }
            vo.getRule().setJoinRuleServicePackage(servicePackages);
        }
        return vo;
    }

}
