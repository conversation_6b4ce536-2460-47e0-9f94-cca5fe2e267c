package com.puree.followup.question.domain.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: PatientQuestionRecord
 * @Date 2024/6/6 9:42
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientQuestionRecord {

    /**
     * ID
     */
    private Long id;

    /**
     * hospitalId
     */
    private Long hospitalId ;

    /**
     * 账号id
     */
    private Long userId ;

    /**
     * 就诊人id
     */
    private Long patientId ;

    /**
     * questionId
     */
    private Long questionId ;

    /**
     * 发送此问卷的随访id
     */
    private Long followUpId;

    /**
     * 发送此问卷的patient event id
     */
    private Long patientEventId ;

    /**
     * 发送此问卷的医生id
     */
    private Long doctorId ;

    /**
     * 医生姓名
     */
    private String doctorName ;

    /**
     * 医助ID
     */
    private Long  assistantId;
    /**
     * 医助姓名
     */
    private String assistantName ;

    /**
     * 0-邀请中  1-填写中  2-完成 3-已拒绝
     * FillQuestionStatusEnum
     */
    private Integer fillStatus ;

    /**
     * 用户开始做问卷时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fillTime ;

    /**
     * 用户提交问卷时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime ;

    /**
    * 该问卷的总分数
    */
    private BigDecimal totalScore ;

    /**
     * 问卷答案
     */
    private String answerContent ;

    /**
     *  问卷评分详情
     */
    private ScoreValue answerScoreValue ;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date measureTime ;

    /**
     *  bus_doctor_patient_group 的 ID
     */
    private Long groupId ;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete = 0;

    // -------------------------

    /**
     * 患者name
     */
    private String patientName ;

    /**
     *  患者性别
     */
    private String patientSex ;

    /**
     * 患者age
     */
    private String patientAge ;

    /**
     * 患者填写问卷持续时间 , 100小时3分32秒
     */
    private String fillDuration ;

    private List<Long> patientIdList ;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginDate ;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate ;

    private String followUpName ;


}
