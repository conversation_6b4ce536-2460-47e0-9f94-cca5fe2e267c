package com.puree.followup.question.enums;

import cn.hutool.core.util.StrUtil;

/**
 * @ClassName: QuestionTypeEnum
 * @Date 2024/6/5 15:03
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */
public enum QuestionTypeEnum {

    //0.单选题  1.多选题  2.单行文本  3.多行文本  4.多行填空 5.量表  6.附件 7.说明文字题  8.患者指标 9.患者报告

    NONE(-1, "none", "无") ,
    SINGLE_CHOOSE(0, "singleChoose", "单选题") ,
    MULTI_CHOOSE(1, "multiChoose", "多选题") ,
    SINGLE_TEXT(2, "singleText", "单行文本") ,
    MULTI_TEXT(3, "multiText", "多行文本") ,
    MULTI_FILL(4, "multiFill", "多行填空") ,
    NSP(5, "nsp", "量表NSP") ,
    ATTACHMENT(6, "attachment", "附件") ,
    TEXT(7, "text", "说明文字题") ,
    INDICATOR(8, "indicator", "患者指标") ,
    REPORT(9, "report", "患者报告") ,

    ;



    private Integer index;
    private String name;
    private String desc;

    QuestionTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static QuestionTypeEnum getByName(String name) {
        if (StrUtil.isBlank(name) ) {
            return null;
        }
        QuestionTypeEnum[] metaArr = QuestionTypeEnum.values();
        for (QuestionTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static QuestionTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        QuestionTypeEnum[] metaArr = QuestionTypeEnum.values();
        for (QuestionTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }



}
