package com.puree.followup.enums;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;

/**
 * @ClassName: TaskEventTypeEnum
 * @Date 2024/4/12 11:03
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum TaskEventTypeEnum {

    // none：无  message：发送消息  tutorial：发送患教   questionnaire：发送问卷  recommend：发送推荐  joinInNewItem：加入新分项  terminateCurrentItem：终止本分项  joinInNewFollowUp：入组新随访  terminateCurrentFollowUp：终止本随访
    NONE(0, "none", "无") ,
    MESSAGE(1, "message", "发送消息") ,
    TUTORIAL(2, "tutorial", "发送患教") ,
    QUESTIONNAIRE(3, "questionnaire", "发送问卷"),
    RECOMMEND(4, "recommend", "发送推荐"),
    JOIN_IN_NEW_ITEM(10, "joinInNewItem", "加入新分项"),
    TERMINATE_CURRENT_ITEM(11, "terminateCurrentItem", "终止本分项"),
    JOIN_IN_NEW_FOLLOW_UP(12, "joinInNewFollowUp", "入组新随访"),
    TERMINATE_CURRENT_FOLLOW_UP(13, "terminateCurrentFollowUp", "终止本随访")
    ;


    private Integer index;
    private String name ;
    private String desc;


    TaskEventTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public static TaskEventTypeEnum getEnumByName(String name) {
        if (StrUtil.isBlank(name)){
            return null;
        }
        TaskEventTypeEnum taskEventTypeEnum = Arrays.stream(TaskEventTypeEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getName(), name) )
                .findAny()
                .orElse(null);
        return taskEventTypeEnum;
    }

    public static TaskEventTypeEnum getEnumByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        TaskEventTypeEnum[] metaArr = TaskEventTypeEnum.values();
        for (TaskEventTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
