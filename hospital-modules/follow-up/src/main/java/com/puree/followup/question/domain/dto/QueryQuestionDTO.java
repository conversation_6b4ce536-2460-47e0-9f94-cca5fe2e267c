package com.puree.followup.question.domain.dto;

import lombok.Data;

/**
 * @ClassName: QueryQuestionDTO
 * @Date 2024/6/13 17:22
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
@Data
public class QueryQuestionDTO {

    /**
     * 每页多少个
     */
    private Integer pageSize;

    /**
     * 多少页
     */
    private Integer pageNum;

    /**
     * question 主键id
     */
    private Long questionId ;

    /**
     * question name
     */
    private String questionName ;

    /**
     * classification 主键id
     */
    private Long classifyId ;

    /**
     * classify name
     */
    private String classifyName ;

    /**
     * 问卷状态: 0-关闭 1-启用 2-发布
     * QuestionStatusEnum
     */
    private Integer status ;

    /**
     * 关联随访 - 0.否 1.是
     */
    private Integer relateFollowUp ;






}
