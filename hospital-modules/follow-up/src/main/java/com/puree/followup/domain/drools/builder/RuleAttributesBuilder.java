package com.puree.followup.domain.drools.builder;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.puree.followup.domain.drools.constant.DialectEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @ClassName RuleAttributes
 * <AUTHOR>
 * @Description 规则属性
 * @Date 2024/4/2 15:53
 * @Version 1.0
 */
@Data
@Accessors(chain = true)
public class RuleAttributesBuilder {

    private final static String SALIENCE_KEY = "salience";
    private final static String DIALECT_KEY = "dialect";
    private final static String ENABLED_KEY = "enabled";
    private final static String DATE_EFFECTIVE_KEY = "date-effective";
    private final static String DATE_EXPIRES_KEY = "date-expires";
    private final static String ACTIVATION_GROUP_KEY = "activation-group";
    private final static String AGENDA_GROUP_KEY = "agenda-group";
    private final static String TIMER_KEY = "timer";
    private final static String AUTO_FOCUS_KEY = "auto-focus";
    private final static String NO_LOOP_KEY = "no-loop";

    /**
     * 指定规则执行优先级,从大到小执行
     */
    private Integer salience;
    /**
     * 指定规则使用的语言类型，java、mvel
     */
    private DialectEnum dialect = DialectEnum.JAVA;
    /**
     * 指定规则是否启用
     */
    private Boolean enabled;
    /**
     * 指定规则生效时间 格式 YYYY-MM-dd HH:mm
     */
    private String dateEffective;
    /**
     * 指定规则失效时间 格式 YYYY-MM-dd HH:mm
     */
    private String dateExpires;
    /**
     * 激活分组，具有相同分组名称的规则只能有一个规则触发
     */
    private String activationGroup;
    /**
     * 议程分组，只有获取焦点的组中的规则才有可能触发
     */
    private String agendaGroup;
    /**
     * 定时器，指定规则触发的时间
     * 一、(5s 2s) 五秒后触发，然后每隔两秒执行一次
     * 二、(cron:0/1 * * * * ?) 每秒触发一次
     */
    private String timer;
    /**
     * 自动获取焦点，一般结合agenda-group一起使用，当一个议程分组未获取焦点时，可以设置auto-focus属性来控制。
     */
    private Boolean autoFocus;
    /**
     * 防止死循环
     */
    private Boolean noLoop;

    /**
     * @Param
     * @Return java.lang.String
     * @Description 生成 string 串
     * <AUTHOR>
     * @Date 2024/4/9 16:20
     **/
    public String builderAttributesString(){
        StrBuilder builder = new StrBuilder();
        // 优先级
        if (ObjectUtil.isNotEmpty(salience)) {
            builder.append(StrPool.C_TAB).append(SALIENCE_KEY).append(StrPool.C_SPACE).append(salience).append(StrPool.LF);
        }
        // 语言类型
        if (ObjectUtil.isNotEmpty(dialect)) {
            builder.append(StrPool.C_TAB).append(DIALECT_KEY).append(StrPool.C_SPACE).append(CharPool.DOUBLE_QUOTES).append(dialect.getValue()).append(CharPool.DOUBLE_QUOTES).append(StrPool.LF);
        }
        if (ObjectUtil.isNotEmpty(enabled)) {
            builder.append(StrPool.C_TAB).append(ENABLED_KEY).append(StrPool.C_SPACE).append(enabled).append(StrPool.LF);
        }
        if (StrUtil.isNotEmpty(dateEffective)) {
            builder.append(StrPool.C_TAB).append(DATE_EFFECTIVE_KEY).append(StrPool.C_SPACE).append(dateEffective).append(StrPool.LF);
        }
        if (StrUtil.isNotEmpty(dateExpires)) {
            builder.append(StrPool.C_TAB).append(DATE_EXPIRES_KEY).append(StrPool.C_SPACE).append(dateExpires).append(StrPool.LF);
        }
        if (StrUtil.isNotEmpty(activationGroup)) {
            builder.append(StrPool.C_TAB).append(ACTIVATION_GROUP_KEY).append(StrPool.C_SPACE).append(activationGroup).append(StrPool.LF);
        }
        if (StrUtil.isNotEmpty(agendaGroup)) {
            builder.append(StrPool.C_TAB).append(AGENDA_GROUP_KEY).append(StrPool.C_SPACE).append(agendaGroup).append(StrPool.LF);
        }
        if (StrUtil.isNotEmpty(timer)) {
            builder.append(StrPool.C_TAB).append(TIMER_KEY).append(StrPool.C_SPACE).append(timer).append(StrPool.LF);
        }
        if (ObjectUtil.isNotEmpty(autoFocus)) {
            builder.append(StrPool.C_TAB).append(AUTO_FOCUS_KEY).append(StrPool.C_SPACE).append(autoFocus).append(StrPool.LF);
        }
        if (ObjectUtil.isNotEmpty(noLoop)) {
            builder.append(StrPool.C_TAB).append(NO_LOOP_KEY).append(StrPool.C_SPACE).append(noLoop).append(StrPool.LF);
        }
        return builder.toString();
    }

}
