package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/8/14 17:55
 * @Description  预警类型：questionnaire.问卷预警 indicator.指标预警 expire.超期预警
 */
public enum FollowUpWarnTypeEnum {

    QUESTIONNAIRE(0, "questionnaire", "问卷预警"),
    INDICATOR(1, "indicator", "指标预警"),
    EXPIRE(2, "expire", "超期预警");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpWarnTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpWarnTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpWarnTypeEnum[] metaArr = FollowUpWarnTypeEnum.values();
        for (FollowUpWarnTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpWarnTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpWarnTypeEnum[] metaArr = FollowUpWarnTypeEnum.values();
        for (FollowUpWarnTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
