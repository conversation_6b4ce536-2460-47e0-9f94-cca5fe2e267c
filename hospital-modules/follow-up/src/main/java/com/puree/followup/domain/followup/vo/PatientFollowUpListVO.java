package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 患者端 随访表列表 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientFollowUpListVO {


    /**
     * 随访记录ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 随访周期类型(到期时间)：0.长期 1.加入随访X天后结束
     */
    private Integer expireType;

    /**
     * 加入随访X天后结束
     */
    private Integer expireDay;

    /**
     * 随访名称
     */
    private String name;

    /**
     * 入组时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date joinTime;

    /**
     * 服务天数：单位为天，-1表示长期随访
     */
    private Integer days = -1;
}