package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  分类的数据类型：0.问卷分类 1.随访分类
 */
public enum ClassificationDataTypeEnum {

    QUESTIONNAIRE(0, "问卷分类"),
    FOLLOW_UP(1, "随访分类");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    ClassificationDataTypeEnum(Integer index, String name) {
        this.index = index;
        this.name = name;
    }

    public static ClassificationDataTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        ClassificationDataTypeEnum[] metaArr = ClassificationDataTypeEnum.values();
        for (ClassificationDataTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static ClassificationDataTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        ClassificationDataTypeEnum[] metaArr = ClassificationDataTypeEnum.values();
        for (ClassificationDataTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
