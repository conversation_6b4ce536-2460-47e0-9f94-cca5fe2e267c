package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/22 10:32
 * @Description  患者选择方式：0.指定范围 1.指定患者 2.小程序码
 */
public enum FollowUpSelectPatientTypeEnum {

    SPECIFIED_RANGE(0, "range", "指定范围"),
    SPECIFIED_PATIENT(1, "patient", "指定患者"),
    SMALL_PROGRAM_CODE(2, "code", "小程序码");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpSelectPatientTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpSelectPatientTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpSelectPatientTypeEnum[] metaArr = FollowUpSelectPatientTypeEnum.values();
        for (FollowUpSelectPatientTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpSelectPatientTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpSelectPatientTypeEnum[] metaArr = FollowUpSelectPatientTypeEnum.values();
        for (FollowUpSelectPatientTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
