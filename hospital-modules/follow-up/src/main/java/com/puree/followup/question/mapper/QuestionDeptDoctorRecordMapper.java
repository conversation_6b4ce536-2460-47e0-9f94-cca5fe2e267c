package com.puree.followup.question.mapper;

import com.puree.followup.question.domain.model.QuestionDeptDoctorRecord;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【question_dept_doctor_record(问卷-科室-医生映射表)】的数据库操作Mapper
* @createDate 2024-06-12 17:11:55
* @Entity com.puree.followup.question.domain.model.QuestionDeptDoctorRecord
*/
public interface QuestionDeptDoctorRecordMapper {

    int deleteById(Long id);

    int insert(QuestionDeptDoctorRecord record);

    int insertSelective(QuestionDeptDoctorRecord record);

    List<QuestionDeptDoctorRecord> selectByCondition(QuestionDeptDoctorRecord record);

    int updateByPrimaryKeySelective(QuestionDeptDoctorRecord record);

    int updateByPrimaryKey(QuestionDeptDoctorRecord record);

    void updateByQuestion(QuestionDeptDoctorRecord record) ;

}
