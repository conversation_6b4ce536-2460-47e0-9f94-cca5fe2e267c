package com.puree.followup.question.notification;

import com.puree.followup.helper.QuestionTemplateMessageHelper;
import com.puree.followup.question.domain.dto.QuestionEventBusinessDTO;
import com.puree.hospital.common.notification.assembler.INotificationAssembler;
import com.puree.hospital.common.notification.service.impl.AbstractWxOfficialAccountTemplateMessageService;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/3/21 10:05
 */
@Service("questionWxOfficialAccountTemplateMessageService")
public class QuestionWxOfficialAccountTemplateMessageService<E extends QuestionInviteEvent> extends AbstractWxOfficialAccountTemplateMessageService<E, QuestionEventBusinessDTO> {

    @Resource
    private QuestionTemplateMessageHelper questionTemplateMessageHelper;

    /**
     *  生成业务关键数据
     * @param event 事件
     * @return
     */
    @Override
    protected QuestionEventBusinessDTO getBusinessData(E event) {
        return questionTemplateMessageHelper.getBusinessData(event);
    }

    @Override
    protected Long getHospitalId(E event, QuestionEventBusinessDTO businessData) {
        return businessData.getHospitalId();
    }

    @Override
    protected Long getPatientId(E event, QuestionEventBusinessDTO businessData) {
        return businessData.getUserId();
    }

    @Override
    protected String getPartnerCode(E event, QuestionEventBusinessDTO businessData) {
        // 随访问卷这块目前没有关联机构Code，只推送医院的模板
        return null;
    }

    /**
     * 获取模板消息
     *
     * @param event      事件
     * @param businessData 业务数据
     * @return 模板消息
     */
    @Override
    protected INotificationAssembler getAssembler(E event, QuestionEventBusinessDTO businessData) {
        return questionTemplateMessageHelper.getAssembler(event, businessData);
    }
}
