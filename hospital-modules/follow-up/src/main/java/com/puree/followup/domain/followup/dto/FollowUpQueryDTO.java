package com.puree.followup.domain.followup.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 随访表查询 DTO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpQueryDTO {

    /**
     * ID
     */
    private Long id;

    /**
     * 随访名称
     */
    private String name;

    /**
     * 随访名称
     */
    private String keyword;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 分类id
     */
    private Long classifyId;

    /**
     * 启用标识：false.否 true.是
     */
    private Boolean isEnable;

    /**
     * 发布标识：false.否 true.是
     */
    private Boolean isPublish;

    /**
     * 乐观锁
     */
    private Integer revision;

    /**
     * 删除标记：0.未删除 1.已删除
     */
    private Integer isDelete;

    /**
     * 每页多少个
     */
    private Integer pageSize;

    /**
     * 多少页
     */
    private Integer pageNum;

}