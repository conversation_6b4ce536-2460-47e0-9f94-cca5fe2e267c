package com.puree.followup.question.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.queue.producer.event.question.QuestionInviteEventProducer;
import com.puree.followup.service.ImService;
import com.puree.followup.service.ImWxMessageService;
import com.puree.hospital.business.api.RemoteBusPatientService;
import com.puree.hospital.business.api.model.BusPatientDto;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.core.constant.TemplateMsgConstants;
import com.puree.hospital.common.api.domain.R;
import com.puree.hospital.common.core.enums.ImTypeEnum;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;
import com.puree.hospital.im.api.model.FollowUpImWxDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @ClassName: SendQuestionToPatientUtil
 * @Date 2024/6/21 10:56
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */

@Component
@Slf4j
@RefreshScope
public class SendQuestionToPatientUtil {

    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper ;
    @Autowired
    private RedisTemplate redisTemplate ;
    @Autowired
    private RemoteBusPatientService remoteBusPatientService ;
    @Value("${find.cachePatient.pageSize:2000}")
    private Integer findCachePatientPageSize;
    @Autowired
    private ImService imService ;
    @Autowired
    private ImWxMessageService imWxMessageService;
    @Value("${question.content.num:10}")
    private Integer questionContentNum ;
    @Resource
    private QuestionInviteEventProducer questionInviteEventProducer;



    @Async
    public void sendQuestionToAllPatient(BusPatientDto patientDto, Question question, String lockKey) {
        patientDto.setIsVisible(Boolean.TRUE);
        R<List<BusPatientFamilyVo>> r = remoteBusPatientService.listFamilyInfo(patientDto);
        if ( null==r.getData() || r.getData().isEmpty() ) {
            redisTemplate.delete(lockKey) ;
            return ;
        }

        List<BusPatientFamilyVo> allPatientFamilyVoList = r.getData();
        List<Long> allPatientIdList = allPatientFamilyVoList.stream().map(BusPatientFamilyVo::getId).distinct().collect(Collectors.toList());

        List<Long> finishPatientIdList = patientQuestionRecordMapper.findPatientByFillStatus(question.getHospitalId(), question.getId(),
                YesNoEnum.NO.getCode(), Arrays.asList(FillQuestionStatusEnum.FILLING.getIndex(), FillQuestionStatusEnum.FINISHED.getIndex()),
                question.getFillTimes() );

        List<BusPatientFamilyVo> unFinishPatientFamilyVoList  = new ArrayList<>() ;

        if ( null==finishPatientIdList || finishPatientIdList.isEmpty() ) {

            unFinishPatientFamilyVoList.addAll(allPatientFamilyVoList) ;

        }else {

            for ( Long patientId : allPatientIdList ) {
                if (!finishPatientIdList.contains(patientId)){
                    BusPatientFamilyVo patientFamilyVo = allPatientFamilyVoList.stream().filter(e -> patientId.equals(e.getId())).findFirst().get();
                    unFinishPatientFamilyVoList.add(patientFamilyVo) ;
                }
            }

        }

        if ( unFinishPatientFamilyVoList.isEmpty() ) {
            redisTemplate.delete(lockKey) ;
            return ;
        }

        Integer totalCount = unFinishPatientFamilyVoList.size() ;
        Integer totalPage = ( 0 == totalCount%findCachePatientPageSize ) ? (totalCount/findCachePatientPageSize) : ((totalCount/findCachePatientPageSize) + 1) ;

        List<CompletableFuture> completableFutureList = new ArrayList<>() ;
        for (int i=1;i<=totalPage;i++) {
            List<BusPatientFamilyVo> patientFamilyVoList = PageUtil.buildPage(unFinishPatientFamilyVoList, findCachePatientPageSize, i);
            CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
                this.doSendQuestionToPatient(question, patientFamilyVoList);
            });
            completableFutureList.add(completableFuture) ;
        }

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join() ;
        redisTemplate.delete(lockKey) ;

    }

    @Async
    public void sendQuestionToPatient(Question question, List<Long> excludeIdList , String lockKey, String patientKey) {

        Boolean flag = redisTemplate.hasKey(patientKey);
        if (!flag) {
            redisTemplate.delete(lockKey) ;
            return ;
        }

        Integer totalCount = redisTemplate.opsForList().size(patientKey).intValue() ;
        int totalPage = (0 == totalCount% findCachePatientPageSize) ? (totalCount/ findCachePatientPageSize) : ((totalCount/ findCachePatientPageSize)+1) ;

        List<CompletableFuture> completableFutureList = new ArrayList<>() ;
        for (int t=1;t<=totalPage;t++ ) {
            int start = (t-1)* findCachePatientPageSize;
            int end = start + findCachePatientPageSize -1 ;
            List<BusPatientFamilyVo> list = redisTemplate.opsForList().range(patientKey, start, end);
            list.removeIf(e-> null!=excludeIdList && !excludeIdList.isEmpty() && excludeIdList.contains(e.getId())) ;

            CompletableFuture completableFuture = CompletableFuture.runAsync(() -> {
                this.doSendQuestionToPatient(question, list);
            });
            completableFutureList.add(completableFuture) ;

        }

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join() ;
        redisTemplate.delete(lockKey) ;

    }



    public void doSendQuestionToPatient(Question question , List<BusPatientFamilyVo> patientFamilyVoList){

        Date now = new Date();
        String username = SecurityUtils.getUsername();

        patientFamilyVoList.forEach( patientFamilyVo -> {
            PatientQuestionRecord patientQuestionRecord = new PatientQuestionRecord();
            patientQuestionRecord.setHospitalId(question.getHospitalId()) ;
            patientQuestionRecord.setUserId(patientFamilyVo.getPatientId()) ;
            patientQuestionRecord.setPatientId(patientFamilyVo.getId());
            patientQuestionRecord.setQuestionId(question.getId()) ;
            patientQuestionRecord.setFillStatus(FillQuestionStatusEnum.INVITING.getIndex()) ;
            patientQuestionRecord.setCreateBy(username) ;
            patientQuestionRecord.setUpdateBy(username);
            patientQuestionRecord.setCreateTime(now) ;
            patientQuestionRecord.setUpdateTime(now) ;
            patientQuestionRecord.setIsDelete(YesNoEnum.NO.getCode()) ;
            try{
                patientQuestionRecordMapper.insert(patientQuestionRecord) ;
            }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
                log.error("患者问卷记录重复插入异常", e);
                return;
            }

            FollowUpImWxDTO followUpImWxDTO = new FollowUpImWxDTO();
            followUpImWxDTO.setId(question.getId());
            followUpImWxDTO.setIsSendIm(YesNoEnum.YES.getCode());
            followUpImWxDTO.setPatientId(patientFamilyVo.getPatientId());
            followUpImWxDTO.setFamilyId(patientFamilyVo.getId());
            followUpImWxDTO.setType(TemplateMsgConstants.FOLLOW_UP_FORWARD_QUESTIONNAIRE_TYPE);
            followUpImWxDTO.setName(question.getName());
            followUpImWxDTO.setDesc(question.getDesc());
            followUpImWxDTO.setHospitalId(question.getHospitalId());
            followUpImWxDTO.setPatientQuestionId(patientQuestionRecord.getId());
            followUpImWxDTO.setImType(ImTypeEnum.ONE_TO_ONE.getIndex());
            followUpImWxDTO.setPhoneNum(patientFamilyVo.getPhoneNumber());
            followUpImWxDTO.setQuestionOutNum(this.handleQuestionOutNum(question.getQuestionContent()));

            try{
                imService.sendImMessage(followUpImWxDTO);
                // 问卷邀请事件通知
                QuestionInviteEvent questionInviteEvent = new QuestionInviteEvent();
                questionInviteEvent.setPatientQuestionRecordId(patientQuestionRecord.getId());
                questionInviteEventProducer.send(questionInviteEvent);
                imWxMessageService.sendShortMsgForQuestion(followUpImWxDTO) ;
            }catch (Exception ex) {
                log.error("问卷发送im, wx-msg, short-msg失败", ex);
            }

        });

    }

    public Integer handleQuestionOutNum(String questionContent){
        return isContentTooLong(questionContent) ? YesNoEnum.YES.getCode() : YesNoEnum.NO.getCode() ;
    }

    /**
     *  问题数量是否超过设置长度
     * @param questionContent   问卷问题内容
     * @return  是否超过
     */
    public boolean isContentTooLong(String questionContent) {
        if (StrUtil.isBlank(questionContent)) {
            return Boolean.FALSE;
        }
        JSONObject jsonObject = JSONObject.parseObject(questionContent);
        if (null == jsonObject) {
            return Boolean.FALSE;
        }
        JSONArray jsonArray = jsonObject.getJSONArray("list");
        if (null == jsonArray) {
            return Boolean.FALSE;
        }
        return jsonArray.size() > questionContentNum;
    }

    public Long sendQuestionToPatientByQrCode(Question question , BusPatientFamilyVo patientFamilyVo){

        Date now = new Date();

        PatientQuestionRecord patientQuestionRecord = new PatientQuestionRecord();
        patientQuestionRecord.setHospitalId(question.getHospitalId()) ;
        patientQuestionRecord.setUserId(patientFamilyVo.getPatientId()) ;
        patientQuestionRecord.setPatientId(patientFamilyVo.getId());
        patientQuestionRecord.setQuestionId(question.getId()) ;
        patientQuestionRecord.setFillStatus(FillQuestionStatusEnum.INVITING.getIndex()) ;
        patientQuestionRecord.setCreateTime(now) ;
        patientQuestionRecord.setUpdateTime(now) ;
        patientQuestionRecord.setIsDelete(YesNoEnum.NO.getCode()) ;
        try{
            patientQuestionRecordMapper.insert(patientQuestionRecord) ;
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            log.error("患者问卷记录重复插入异常", e);
            throw new ServiceException("问卷发送失败");
        }
        return patientQuestionRecord.getId() ;

    }



}
