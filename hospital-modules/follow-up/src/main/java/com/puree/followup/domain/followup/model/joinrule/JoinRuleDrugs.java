package com.puree.followup.domain.followup.model.joinrule;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.shop.api.model.BusShopLabel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 入组方式-药品
 */

@Data
public class JoinRuleDrugs {

    /** 医院药店药品ID */
    private Long id;

    /** 药品ID */
    private Long drugsId;

    /** 药品编号 */
    private String drugsNumber;
    /**
     * 药品图片
     */
    private String drugsImg;
    /**
     * 医保标识
     */
    private String nationalDrugCode;
    /** 药品分类 */
    private String drugClassification;
    /**
     * 拼音码
     */
    private String pinyinCode;
    /**
     * 用法用量
     */
    private String drugsUsageValue;
    /** 药品名称 */
    private String drugsName;
    /** 药品规格 */
    private String drugsSpecification;
    /** 产商名称 */
    private String drugsManufacturer;
    /** 参考售价 */
    private BigDecimal referenceSellingPrice;
    /** 参考成本价 */
    private BigDecimal referencePurchasePrice;
    /** 销售价 */
    private BigDecimal sellingPrice;
    /** 库存 */
    private Integer stock;
    /** 药品类型 */
    private String drugsType;
    /** 状态 */
    private Integer status;
    /**
     * 是否
     */
    private String prescriptionIdentification;
    /**
     * 处方类型
     */
    private String preType;

    /** 配送企业名称 */
    private String enterpriseName;
    /** 配送企业ID */
    private Long enterpriseId;
    /** 标准通用名 */
    private String standardCommonName;
    /**医保类型*/
    private String medicalInsuranceType;
    private String decoctingMethod;

    private String decoctingMethodValue;
    private String drugsDosageFormValue;
    private String medicalInsuranceTypeValue;
    /**
     * 中药类型
     */
    private String tcmType;
    /**
     * 国药准字
     */
    private String nmpn;
    /**
     * 本位码
     */
    private String drugsStandardCode;

}
