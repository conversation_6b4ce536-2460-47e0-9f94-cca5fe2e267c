package com.puree.followup.domain.followup.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.puree.followup.config.BusPatientFamilyVOTypeHandler;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import com.puree.hospital.common.core.mybatis.typehandler.JsonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * (手动)添加患者记录表
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class FollowUpInvite implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 随访ID
     */
    private Long followUpId;

    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 添加方式：0.直接添加 1.邀请入组(需患者同意)
     */
    private Integer joinType;

    /**
     * 患者选择方式：0.指定范围 1.指定患者 2.小程序码
     */
    private Integer selectPatientType;

    /**
     * 患者选择方式为指定范围时的条件列表
     */
    private String conditions;

    /**
     * 患者选择方式为指定患者时的患者id列表
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<Long> patients;

    /**
     * 小程序码
     */
    private String qrcode;

    /**
     * 添加的患者总数
     */
    private Integer patientCount;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

}