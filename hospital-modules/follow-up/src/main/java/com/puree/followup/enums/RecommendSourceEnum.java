package com.puree.followup.enums;

/**
 * 事件的推荐内容类型
 * @date 2024/4/26 9:13
 * <AUTHOR>
 */
public enum RecommendSourceEnum {

    // 1、推荐医生  2、推荐科室  3、推荐商品  4、推荐服务包
    DOCTOR(1, "推荐医生") ,
    DEPARTMENT(2,"推荐科室"),
    GOODS(3, "推荐商品") ,
    SERVICE_PACK(4, "推荐服务包")
    ;


    private Integer index ;
    private String desc;

    public Integer getIndex() {
        return index;
    }

    public String getDesc() {
        return desc;
    }

    RecommendSourceEnum(Integer index, String desc) {
        this.index = index;
        this.desc = desc;
    }

    public static RecommendSourceEnum getRecommendSourceEnumByIndex(Integer index) {
        if (null==index) {
            return null;
        }
        RecommendSourceEnum[] metaArr = RecommendSourceEnum.values();
        for (RecommendSourceEnum recommendSourceEnum : metaArr) {
            if (recommendSourceEnum.getIndex().equals(index)) {
                return recommendSourceEnum;
            }
        }
        return null;
    }

}
