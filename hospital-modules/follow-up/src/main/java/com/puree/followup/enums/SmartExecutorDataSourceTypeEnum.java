package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description 智能执行中，数据源的类型：questionnaire.问卷数据 healthRecord.健康档案
 */
public enum SmartExecutorDataSourceTypeEnum {

    QUESTIONNAIRE(0, "questionnaire", "问卷数据"),
    HEALTH_RECORD(1, "healthRecord", "健康档案");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    SmartExecutorDataSourceTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static SmartExecutorDataSourceTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        SmartExecutorDataSourceTypeEnum[] metaArr = SmartExecutorDataSourceTypeEnum.values();
        for (SmartExecutorDataSourceTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static SmartExecutorDataSourceTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        SmartExecutorDataSourceTypeEnum[] metaArr = SmartExecutorDataSourceTypeEnum.values();
        for (SmartExecutorDataSourceTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
