package com.puree.followup.enums;

/**
 * @ClassName: TaskRemindStatusEnum
 * @Date 2024/4/24 15:17
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum TaskRemindStatusEnum {

    NOTREMIND(0, "notRemind", "未提醒"),
    REMINDED(1, "reminded", "已提醒")

    ;

    TaskRemindStatusEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    private Integer index ;
    private String name ;

    private String desc;

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static TaskRemindStatusEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        TaskRemindStatusEnum[] metaArr = TaskRemindStatusEnum.values();
        for (TaskRemindStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static TaskRemindStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        TaskRemindStatusEnum[] metaArr = TaskRemindStatusEnum.values();
        for (TaskRemindStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
