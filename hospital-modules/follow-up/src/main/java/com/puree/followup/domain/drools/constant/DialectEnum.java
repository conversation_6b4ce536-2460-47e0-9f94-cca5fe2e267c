package com.puree.followup.domain.drools.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName DialectEnum
 * <AUTHOR>
 * @Description 表达式枚举
 * @Date 2024/4/8 11:23
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum DialectEnum {

    JAVA(1,"java" , "java","JAVA"),
    MVEL(2, "mvel", "mvel","mvel 表达式")
    ;


    /**
     * 编号
     */
    private Integer number;
    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;


}
