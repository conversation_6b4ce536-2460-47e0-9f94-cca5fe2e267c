package com.puree.followup.queue.producer.event.question;

import com.puree.hospital.common.redis.mq.RedisStreamProducer;
import com.puree.hospital.common.redis.mq.annotation.RedisProducer;
import com.puree.hospital.followup.api.model.event.question.QuestionInviteEvent;

/**
 *  问卷邀请事件生产者
 * <AUTHOR>
 * @date 2025/2/28 14:34
 */
@RedisProducer(topic = QuestionInviteEvent.TOPIC)
public class QuestionInviteEventProducer extends RedisStreamProducer<QuestionInviteEvent> {
}
