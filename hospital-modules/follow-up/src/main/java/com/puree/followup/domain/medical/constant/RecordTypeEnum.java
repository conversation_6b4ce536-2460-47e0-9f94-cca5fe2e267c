package com.puree.followup.domain.medical.constant;

import cn.hutool.core.text.CharSequenceUtil;
import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @ClassName RecordTypeEnum
 * <AUTHOR>
 * @Description 记录类型枚举
 * @Date 2024/5/13 16:50
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum RecordTypeEnum {


    SINGLE_INSPECTION(1, "SINGLE_INSPECTION", "SINGLE_INSPECTION", "单项检查"),
    MEDICAL_REPORT(2, "MEDICAL_REPORT", "MEDICAL_REPORT", "体检报告"),
    ;

    /**
     * @Param code
     * @Return com.puree.followup.domain.medical.constant.RegularRecordEnum
     * @Description 通过 code 获取枚举
     * <AUTHOR>
     * @Date 2024/5/13 15:39
     **/
    @JsonCreator(mode = JsonCreator.Mode.DELEGATING)
    public static RecordTypeEnum getEnumByCode(String code) {
        if (code == null) {
            return null;
        }
        RecordTypeEnum recordTypeEnum = Arrays.stream(RecordTypeEnum.values())
                .filter(i -> CharSequenceUtil.equalsIgnoreCase(i.getCode(), code))
                .findAny()
                .orElse(null);
        return recordTypeEnum;
    }

    /**
     * 编号
     */
    private Integer number;

    /**
     * 代码
     */
    private String code;
    /**
     * 值
     */
    private String value;
    /**
     * 描述
     */
    private String desc;
}
