package com.puree.followup.domain.followup.dto.joinrule;

import lombok.Data;

import java.util.List;

/**
 * @ClassName JoinRuleFive
 * <AUTHOR>
 * @Description 入组规则
 * @Date 2024/4/16 15:57
 * @Version 1.0
 */
@Data
public class JoinRuleDTO {

    /**
     * 入组方式1
     */
    private List<JoinRuleOneDTO> joinRuleOne;

    /**
     * 入组方式2
     */
    private List<JoinRuleTwoDTO> joinRuleTwo;

    /**
     * 入组方式3
     */
    private List<JoinRuleThreeDTO> joinRuleThree;

    /**
     * 入组方式4
     */
    private List<JoinRuleFourDTO> joinRuleFour;

    /**
     * 入组方式5
     */
    private List<JoinRuleFiveDTO> joinRuleFive;


}
