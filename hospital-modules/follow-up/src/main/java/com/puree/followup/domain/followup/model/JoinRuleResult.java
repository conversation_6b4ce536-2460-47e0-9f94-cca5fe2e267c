package com.puree.followup.domain.followup.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者加入入组方式的结果
 * <AUTHOR>
 * @date 2024-06-12 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class JoinRuleResult {

    public JoinRuleResult() {
    }

    public JoinRuleResult(String message, Boolean isSuccess) {
        this.message = message;
        this.isSuccess = isSuccess;
    }

    /**
     * 结果信息
     */
    private String message;

    /**
     * 是否加入成功
     */
    private Boolean isSuccess = false;

}