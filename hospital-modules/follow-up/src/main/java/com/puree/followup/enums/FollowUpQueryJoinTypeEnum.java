package com.puree.followup.enums;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Date 2024/4/11 17:55
 * @Description  入组类型：0.自动添加 1.自动邀请(需患者同意) 2.手动添加 3.手动邀请(需患者同意)
 */
public enum FollowUpQueryJoinTypeEnum {

    AUTO_JOIN_IN(0, "autoJoinIn", "自动添加"),
    AUTO_INVITE(1, "autoInvite", "自动邀请"),
    MANUAL_ADD(2, "manualAdd", "手动添加"),
    MANUAL_INVITE(3, "manualInvite", "手动邀请");

    @Getter
    @Setter
    private Integer index;

    @Getter
    @Setter
    private String name;

    @Getter
    @Setter
    private String desc;

    FollowUpQueryJoinTypeEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }

    public static FollowUpQueryJoinTypeEnum getByName(String name) {
        if (name == null) {
            return null;
        }
        FollowUpQueryJoinTypeEnum[] metaArr = FollowUpQueryJoinTypeEnum.values();
        for (FollowUpQueryJoinTypeEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FollowUpQueryJoinTypeEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FollowUpQueryJoinTypeEnum[] metaArr = FollowUpQueryJoinTypeEnum.values();
        for (FollowUpQueryJoinTypeEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
