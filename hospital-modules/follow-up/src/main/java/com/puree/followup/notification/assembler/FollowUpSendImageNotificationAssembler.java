package com.puree.followup.notification.assembler;

import com.puree.followup.domain.followup.dto.FollowUpEventBusinessDTO;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import com.puree.hospital.business.api.model.BusPartnersVO;
import com.puree.hospital.common.notification.constant.TemplateMessageConstants;
import com.puree.hospital.followup.api.model.event.followup.BaseFollowUpEvent;

import java.util.Map;

/**
 * <p>
 * 随访图片事件通知组装器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 18:41
 */
public class FollowUpSendImageNotificationAssembler extends BaseFollowUpNotificationAssembler {

    public FollowUpSendImageNotificationAssembler(BaseFollowUpEvent event, FollowUpEventBusinessDTO businessDTO) {
        super(event, businessDTO);
    }

    /**
     * 获取微信公众号模板参数
     *
     * @return 微信公众号模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxOfficialAccountTemplateParam() {
        return getDefaultWxOfficialAccountTemplateParam();
    }

    /**
     * 获取微信小程序模板参数
     *
     * @return 小程序模板参数
     */
    @Override
    public Map<String, Map<String, Object>> getWxUniAppTemplateParam() {
        return getDefaultWxUniAppTemplateParam();
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxOfficialAccountMsgKey() {
        return TemplateMessageConstants.WX_OFFICIAL_ACCOUNT_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取模板消息key
     *
     * @return 模板消息key
     */
    @Override
    public String getWxUniAppMsgKey() {
        return TemplateMessageConstants.WX_UNI_APP_FOLLOW_UP_MSG_KEY;
    }

    /**
     * 获取微信公众号重定向页面
     *
     * @param wechatConfig 医院微信公众号配置
     * @param partnersVO   合作机构方信息
     * @return 重定向页面
     */
    @Override
    public String getWxOfficialAccountRedirectPage(BusHospitalWechatConfig wechatConfig, BusPartnersVO partnersVO) {
        return getFollowUpTextOrImageRedirectPage();
    }

    /**
     * 获取微信小程序重定向页面
     *
     * @return 重定向页面
     */
    @Override
    public String getWxUniAppRedirectPage(BusHospitalWechatConfig wechatConfig) {
        return getFollowUpTextOrImageRedirectPage();
    }

}
