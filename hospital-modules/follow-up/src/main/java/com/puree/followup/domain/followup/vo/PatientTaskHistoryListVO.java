package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 患者随访任务执行历史 VO
 * <AUTHOR>
 * @date 2024-04-11 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class PatientTaskHistoryListVO {

    /**
     * 日期格式：yyyy-MM-dd
     */
    private String taskDate;

    /**
     * 任务列表
     */
    private List<PatientTaskHistoryInfoVO> taskList;

}