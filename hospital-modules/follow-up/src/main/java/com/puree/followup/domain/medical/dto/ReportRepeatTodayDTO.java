package com.puree.followup.domain.medical.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName ReportRepeatTodayDTO
 * <AUTHOR>
 * @Description 当天重复数据DTO
 * @Date 2024/6/4 16:32
 * @Version 1.0
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class ReportRepeatTodayDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 身份证
     */
    private String patientIdNumber;
    /**
     * 名称
     */
    private String name;
    /**
     * 项目名称
     */
    private String subject;
    /**
     * 需要排查的时间,不传为当天
     */
    private String time ;
}
