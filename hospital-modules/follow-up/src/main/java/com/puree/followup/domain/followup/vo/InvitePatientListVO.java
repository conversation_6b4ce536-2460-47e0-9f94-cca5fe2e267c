package com.puree.followup.domain.followup.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.puree.hospital.business.api.model.BusPatientFamilyVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 手动添加患者结果封装 VO
 * <AUTHOR>
 * @date 2024-04-08 14:29:33
 */
@Data
@EqualsAndHashCode
@Accessors(chain = true)
public class InvitePatientListVO {

    //1、处方诊断结果
    List<BusPatientFamilyVo> diagnosisResult = null;

    //2、患者报告结果
    List<BusPatientFamilyVo>  pushDataResult = null;

    //3、购买商品结果
    List<BusPatientFamilyVo>  goodsResult = null;

    //4、购买药品结果
    List<BusPatientFamilyVo>  drugResult = null;

    //5、购买服务包结果
    List<BusPatientFamilyVo>  servicePackageResult = null;

    //6、患者资料(健康档案)结果
    List<BusPatientFamilyVo>  healthRecordResult = null;

    //7、所属医生结果
    List<BusPatientFamilyVo>  doctorResult = null;
}