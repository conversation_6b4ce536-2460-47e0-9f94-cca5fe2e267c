package com.puree.followup.question.enums;

import cn.hutool.core.util.StrUtil;

/**
 * @ClassName: FillQuestionStatusEnum
 * @Date 2024/6/6 14:07
 * <AUTHOR> jian
 * @Description:
 * @Version 1.0
 */
public enum FillQuestionStatusEnum {

    INVITING(0, "inviting", "邀请中") ,
    FILLING(1, "filling", "填写中") ,
    FINISHED(2, "finished", "填写完成") ,
    CLOSED(3, "closed", "已关闭") ,


    ;

    private Integer index;
    private String name;
    private String desc;

    FillQuestionStatusEnum(Integer index, String name, String desc) {
        this.index = index;
        this.name = name;
        this.desc = desc;
    }


    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static FillQuestionStatusEnum getByName(String name) {
        if (StrUtil.isBlank(name) ) {
            return null;
        }
        FillQuestionStatusEnum[] metaArr = FillQuestionStatusEnum.values();
        for (FillQuestionStatusEnum type : metaArr) {
            if (type.getName().equals(name)) {
                return type;
            }
        }
        return null;
    }

    public static FillQuestionStatusEnum getByIndex(Integer index) {
        if (index == null) {
            return null;
        }
        FillQuestionStatusEnum[] metaArr = FillQuestionStatusEnum.values();
        for (FillQuestionStatusEnum type : metaArr) {
            if (type.getIndex().equals(index)) {
                return type;
            }
        }
        return null;
    }

}
