package com.puree.followup.listener.question;

import com.puree.followup.queue.producer.event.followup.FollowUpSendImageEventProducer;
import com.puree.followup.queue.producer.event.followup.FollowUpJoinEventProducer;
import com.puree.followup.queue.producer.event.followup.FollowUpSendQuestionEventProducer;
import com.puree.followup.queue.producer.event.followup.FollowUpSendTextEventProducer;
import com.puree.followup.queue.producer.event.tutorial.TutorialArticlePushEventProducer;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendImageEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpJoinEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendQuestionEvent;
import com.puree.hospital.followup.api.model.event.followup.FollowUpSendTextEvent;
import com.puree.hospital.tool.api.model.event.TutorialArticlePushEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <p>
 * 随访事件监听器
 * 注意：此处之所以使用SpringEventListener, 原因是因为代码逻辑上存在事务嵌套问题，
 * 消息发送必须保证事务提交后再发送，因此需要依赖TransactionalEventListener，进行保证事务提交后在发送消息
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/21 14:23
 */
@Slf4j
@Component
public class FollowUpEventListener {

    @Resource @Lazy
    private FollowUpJoinEventProducer followUpJoinEventProducer;
    @Resource @Lazy
    private FollowUpSendTextEventProducer followUpSendTextEventProducer;
    @Resource @Lazy
    private FollowUpSendImageEventProducer followUpSendImageEventProducer;
    @Resource @Lazy
    private FollowUpSendQuestionEventProducer followUpSendQuestionEventProducer;
    @Resource @Lazy
    private TutorialArticlePushEventProducer tutorialArticlePushEventProducer;

    @Async
    @TransactionalEventListener(classes = FollowUpJoinEvent.class)
    public void followUpJoinEvent(FollowUpJoinEvent event) {
        log.info("随访加入事件监听：{}", event);
        followUpJoinEventProducer.send(event);
    }

    @Async
    @TransactionalEventListener(classes = FollowUpSendTextEvent.class)
    public void followUpTextEvent(FollowUpSendTextEvent event) {
        log.info("随访文字事件监听：{}", event);
        followUpSendTextEventProducer.send(event);
    }

    @Async
    @TransactionalEventListener(classes = FollowUpSendImageEvent.class)
    public void followUpImageEvent(FollowUpSendImageEvent event) {
        log.info("随访图片事件监听：{}", event);
        followUpSendImageEventProducer.send(event);
    }

    @Async
    @TransactionalEventListener(classes = FollowUpSendQuestionEvent.class)
    public void followUpQuestionEvent(FollowUpSendQuestionEvent event) {
        log.info("随访问卷事件监听：{}", event);
        followUpSendQuestionEventProducer.send(event);
    }

    @Async
    @TransactionalEventListener(classes = TutorialArticlePushEvent.class)
    public void tutorialPushEvent(TutorialArticlePushEvent event) {
        log.info("患教推送事件监听：{}", event);
        tutorialArticlePushEventProducer.send(event);
    }
}
