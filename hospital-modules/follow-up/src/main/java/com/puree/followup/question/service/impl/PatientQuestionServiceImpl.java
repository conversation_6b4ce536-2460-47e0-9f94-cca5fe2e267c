package com.puree.followup.question.service.impl;

import com.puree.followup.admin.followup.mapper.PatientTaskEventHistoryMapper;
import com.puree.followup.admin.followup.service.IFollowUpAsyncService;
import com.puree.followup.admin.util.JudgeExecuteDateUtil;
import com.puree.followup.client.followup.service.IFollowUpPatientService;
import com.puree.followup.constants.FollowUpConstants;
import com.puree.followup.domain.followup.dto.PatientTaskEventHistoryDTO;
import com.puree.followup.domain.followup.model.PatientTaskEventHistory;
import com.puree.followup.domain.followup.model.TaskAndEventStatus;
import com.puree.followup.question.domain.dto.PatientQuestionDTO;
import com.puree.followup.question.domain.model.PatientQuestionRecord;
import com.puree.followup.question.domain.model.Question;
import com.puree.followup.question.domain.vo.PatientQuestionVO;
import com.puree.followup.question.enums.FillQuestionStatusEnum;
import com.puree.followup.question.enums.QuestionStatusEnum;
import com.puree.followup.question.mapper.PatientQuestionRecordMapper;
import com.puree.followup.question.mapper.QuestionMapper;
import com.puree.followup.question.service.PatientQuestionService;
import com.puree.followup.question.service.SycQuestionStatusService;
import com.puree.hospital.common.core.base.page.PageUtil;
import com.puree.hospital.common.api.enums.YesNoEnum;
import com.puree.hospital.common.core.exception.ServiceException;
import com.puree.hospital.common.core.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: PatientQuestionServiceImpl
 * @Date 2024/6/17 18:00
 * <AUTHOR>
 * @Description:
 * @Version 1.0
 */

@Service
@Slf4j
public class PatientQuestionServiceImpl implements PatientQuestionService {

    @Autowired
    private PatientQuestionRecordMapper patientQuestionRecordMapper ;
    @Autowired
    private QuestionMapper questionMapper ;
    @Autowired
    private IFollowUpPatientService followUpPatientService;
    @Autowired
    private PatientTaskEventHistoryMapper patientTaskEventHistoryMapper;
    @Autowired
    private IFollowUpAsyncService followUpAsyncService;
    @Autowired
    private SycQuestionStatusService sycQuestionStatusService ;



    public List<PatientQuestionRecord> patientPageQuestion(PatientQuestionDTO req) {

        sycQuestionStatusService.sycQuestionStatus();

        PageUtil.startPage();
        List<PatientQuestionRecord> patientQuestionRecords = patientQuestionRecordMapper.patientFindQuestionList(req.getUserId(), Arrays.asList(QuestionStatusEnum.ENABLE.getIndex(), QuestionStatusEnum.PUBLISH.getIndex()));
        return PageUtil.buildPage(patientQuestionRecords, patientQuestionRecords);

    }

    public PatientQuestionVO patientDetailQuestion(Long id) {

        sycQuestionStatusService.sycQuestionStatus();

        PatientQuestionRecord patientQuestionRecord = patientQuestionRecordMapper.selectByPrimaryKey(id);
        if ( null==patientQuestionRecord || YesNoEnum.YES.getCode().equals(patientQuestionRecord.getIsDelete()) ) {
            throw new ServiceException("没有数据") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(patientQuestionRecord.getQuestionId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());
        Question questionDO = questionMapper.selectById(questionQuery);
        if ( null==questionDO ) {
            throw new ServiceException("没有此问卷") ;
        }

        if ( QuestionStatusEnum.CLOSE.getIndex().equals(questionDO.getStatus()) ) {
            throw new ServiceException("该问卷已禁用，无法查看") ;
        }

        if ( null!=patientQuestionRecord.getSubmitTime() && null!=patientQuestionRecord.getFillTime() ){
            String s = JudgeExecuteDateUtil.calcDateGapHms(patientQuestionRecord.getSubmitTime(), patientQuestionRecord.getFillTime());
            patientQuestionRecord.setFillDuration(s) ;
        }

        PatientQuestionVO patientQuestionVO = new PatientQuestionVO();
        patientQuestionVO.setQuestion(questionDO);
        patientQuestionVO.setPatientQuestionRecord(patientQuestionRecord);

        return patientQuestionVO ;

    }

    @Transactional(rollbackFor = Exception.class)
    public void fillQuestion(PatientQuestionDTO req) {

        if ( null==req.getId() ) {
            throw new ServiceException("填写问卷id缺失") ;
        }

        PatientQuestionRecord patientQuestionRecord = patientQuestionRecordMapper.selectByPrimaryKey(req.getId());
        if ( null==patientQuestionRecord || YesNoEnum.YES.getCode().equals(patientQuestionRecord.getIsDelete()) ) {
            throw new ServiceException("没有数据") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(patientQuestionRecord.getQuestionId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());

        Question questionDO = questionMapper.selectById(questionQuery);
        if ( null==questionDO ) {
            throw new ServiceException("没有此问卷") ;
        }

        if ( !QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus()) ) {
            throw new ServiceException("问卷暂不支持被填写") ;
        }

        int i = patientQuestionRecordMapper.countOnePatientFillOneQuestionTimes(questionDO.getHospitalId(), questionDO.getId(), patientQuestionRecord.getPatientId(),
                YesNoEnum.NO.getCode(), Arrays.asList(FillQuestionStatusEnum.FINISHED.getIndex()));

        if ( i>=questionDO.getFillTimes() ) {
            throw new ServiceException(FollowUpConstants.FILL_QUESTION_EXCESS) ;
        }

        sycQuestionStatusService.sycQuestionStatus();

        Date now = new Date();
        PatientQuestionRecord updateDTO = new PatientQuestionRecord() ;
        updateDTO.setId(req.getId()) ;
        updateDTO.setUpdateTime(now) ;
        updateDTO.setFillTime(now) ;
        updateDTO.setFillStatus(FillQuestionStatusEnum.FILLING.getIndex()) ;
        patientQuestionRecordMapper.updateByPrimaryKeySelective(updateDTO) ;

    }

    @Transactional(rollbackFor = Exception.class)
    public void submitQuestion(PatientQuestionDTO req) {

        if ( null==req.getId() ) {
            throw new ServiceException("填写问卷id缺失") ;
        }

        PatientQuestionRecord patientQuestionRecord = patientQuestionRecordMapper.selectByPrimaryKey(req.getId());
        if ( null==patientQuestionRecord || YesNoEnum.YES.getCode().equals(patientQuestionRecord.getIsDelete()) ) {
            throw new ServiceException("没有数据") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(patientQuestionRecord.getQuestionId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());

        Question questionDO = questionMapper.selectById(questionQuery);
        if ( null==questionDO ) {
            throw new ServiceException("没有此问卷") ;
        }

        if ( !QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus()) ) {
            throw new ServiceException("问卷暂不支持被填写") ;
        }

        int i = patientQuestionRecordMapper.countOnePatientFillOneQuestionTimes(questionDO.getHospitalId(), questionDO.getId(), patientQuestionRecord.getPatientId(),
                YesNoEnum.NO.getCode(), Arrays.asList(FillQuestionStatusEnum.FINISHED.getIndex()));

        if ( i>=questionDO.getFillTimes() ) {
            throw new ServiceException(FollowUpConstants.FILL_QUESTION_EXCESS) ;
        }

        sycQuestionStatusService.sycQuestionStatus();

        Date now = new Date();
        PatientQuestionRecord updateDTO = new PatientQuestionRecord() ;
        updateDTO.setId(req.getId()) ;
        updateDTO.setUpdateTime(now) ;
        updateDTO.setSubmitTime(now) ;
        updateDTO.setAnswerContent(req.getAnswerContent()) ;
        updateDTO.setMeasureTime(req.getMeasureTime()) ;
        updateDTO.setFillStatus(FillQuestionStatusEnum.FINISHED.getIndex()) ;
        patientQuestionRecordMapper.updateByPrimaryKeySelective(updateDTO) ;

    }

    @Transactional(rollbackFor = Exception.class)
    public void reviseQuestion(PatientQuestionDTO req) {

        if ( null==req.getId() ) {
            throw new ServiceException("填写问卷id缺失") ;
        }

        PatientQuestionRecord patientQuestionRecord = patientQuestionRecordMapper.selectByPrimaryKey(req.getId());
        if ( null==patientQuestionRecord || YesNoEnum.YES.getCode().equals(patientQuestionRecord.getIsDelete()) ) {
            throw new ServiceException("没有数据") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(patientQuestionRecord.getQuestionId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());

        Question questionDO = questionMapper.selectById(questionQuery);
        if ( null==questionDO ) {
            throw new ServiceException("没有此问卷") ;
        }

        if ( !QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus()) ) {
            throw new ServiceException("问卷暂不支持被填写") ;
        }

        if ( YesNoEnum.NO.getCode().equals(questionDO.getCanRevise()) ) {
            throw new ServiceException("此问卷不可以重新填写") ;
        }

        sycQuestionStatusService.sycQuestionStatus();

        Date now = new Date();
        PatientQuestionRecord updateDTO = new PatientQuestionRecord() ;
        updateDTO.setId(req.getId()) ;
        updateDTO.setUpdateTime(now) ;
        updateDTO.setAnswerContent(req.getAnswerContent()) ;
        updateDTO.setMeasureTime(req.getMeasureTime()) ;
        patientQuestionRecordMapper.updateByPrimaryKeySelective(updateDTO) ;

    }

    /**
     * 患者端 随访患者填写随访问卷：根据事件记录id，查询问卷填写详情
     *
     * @param eventHistoryId 事件记录id
     * @param questionId 问卷id
     * @return 问卷详情
     */
    @Override
    public PatientQuestionVO followUpQuestionInfo(Long eventHistoryId, Long questionId) {

        //查询问卷详情
        Question questionQuery = new Question();
        questionQuery.setId(questionId);
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());
        Question questionDO = questionMapper.selectById(questionQuery);
        if (null == questionDO )
            throw new ServiceException("没有此问卷") ;

        //查询用户填写记录信息
        PatientQuestionRecord query = new PatientQuestionRecord();
        query.setIsDelete(YesNoEnum.NO.getCode());
        query.setPatientEventId(eventHistoryId);
        PatientQuestionRecord record = patientQuestionRecordMapper.selectOne(query);
        if(record == null){
            record = new PatientQuestionRecord();
            record.setFillStatus(FillQuestionStatusEnum.INVITING.getIndex());
        }

        //封装返回结果
        PatientQuestionVO patientQuestionVO = new PatientQuestionVO();
        patientQuestionVO.setQuestion(questionDO);
        patientQuestionVO.setPatientQuestionRecord(record);

        return patientQuestionVO ;
    }




    /**
     * 患者端 随访患者填写随访问卷：开始填写
     *
     * @param dto 问卷及随访信息详情
     * @return 随访事件执行历史
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long followUpFillQuestion(PatientQuestionDTO req) {

        Long eventHistoryId = req.getEventHistoryId();
        //获取用户事件完成历史记录
        PatientTaskEventHistory eventHistory = patientTaskEventHistoryMapper.getById(eventHistoryId);
        if(eventHistory == null){
            //获取随访事件记录id
            PatientTaskEventHistoryDTO eventHistoryDTO = new PatientTaskEventHistoryDTO();
            eventHistoryDTO.setId(req.getEventHistoryId());
            eventHistoryDTO.setItemRecordId(req.getItemRecordId());
            eventHistoryDTO.setTaskId(req.getTaskId());
            eventHistoryDTO.setEventId(req.getEventId());
            eventHistoryDTO.setEventRemindStatus(req.getEventRemindStatus());
            eventHistoryDTO.setEventFinishStatus(req.getEventFinishStatus());
            eventHistoryDTO.setTaskBeginDay(req.getTaskBeginDay());
            eventHistoryDTO.setFillStatus(FillQuestionStatusEnum.FILLING.getIndex());//问卷的填写状态为填写中
            eventHistoryId = followUpPatientService.handleTaskAndEventHistory(eventHistoryDTO);
            eventHistory = patientTaskEventHistoryMapper.getById(eventHistoryId);

            //查询用户填写记录信息
            PatientQuestionRecord query = new PatientQuestionRecord();
            query.setIsDelete(YesNoEnum.NO.getCode());
            query.setPatientEventId(eventHistoryId);
            PatientQuestionRecord record = patientQuestionRecordMapper.selectOne(query);
            if(record != null)
                return eventHistoryId;
        }

        //插入患者随访问卷填写记录
        PatientQuestionRecord patientQuestionRecord = new PatientQuestionRecord();
        patientQuestionRecord.setHospitalId(req.getHospitalId());
        patientQuestionRecord.setUserId(eventHistory.getUserId());
        patientQuestionRecord.setPatientId(eventHistory.getPatientId());
        patientQuestionRecord.setQuestionId(req.getQuestionId());
        patientQuestionRecord.setFollowUpId(eventHistory.getFollowUpId());
        patientQuestionRecord.setPatientEventId(eventHistoryId);
        patientQuestionRecord.setFillStatus(FillQuestionStatusEnum.FILLING.getIndex());
        patientQuestionRecord.setFillTime(new Date());
        patientQuestionRecord.setAnswerContent(req.getAnswerContent());
        patientQuestionRecord.setCreateBy(SecurityUtils.getUsername());
        patientQuestionRecord.setUpdateBy(SecurityUtils.getUsername());
        patientQuestionRecord.setCreateTime(new Date());
        patientQuestionRecord.setUpdateTime(new Date());
        patientQuestionRecord.setIsDelete(YesNoEnum.NO.getCode());
        try{
            patientQuestionRecordMapper.insert(patientQuestionRecord);
        }catch (DuplicateKeyException | SQLIntegrityConstraintViolationException e){
            //更新用户开始填写时间
            PatientQuestionRecord update = new PatientQuestionRecord();
            update.setHospitalId(req.getHospitalId());
            update.setPatientEventId(eventHistoryId);
            update.setFillStatus(FillQuestionStatusEnum.FILLING.getIndex());
            update.setFillTime(new Date());
            patientQuestionRecordMapper.updateFillStatusByPatientEventId(update);
        }
        return eventHistoryId;
    }

    /**
     * 患者端 随访患者填写随访问卷：暂存答案（不是用户最终提交，而是用户在填写的过程中，暂时保存用户所填写的部分答案）
     *
     * @param dto 问卷及随访信息详情
     * @return 随访事件执行历史
     */
    @Override
    public Boolean tempSave(PatientQuestionDTO req) {
        PatientQuestionRecord query = new PatientQuestionRecord();
        query.setIsDelete(YesNoEnum.NO.getCode());
        query.setPatientEventId(req.getEventHistoryId());
        PatientQuestionRecord record = patientQuestionRecordMapper.selectOne(query);
        if(record == null)
            throw new ServiceException("记录不存在") ;

        PatientQuestionRecord updateInfo = new PatientQuestionRecord() ;
        updateInfo.setId(record.getId()) ;
        updateInfo.setAnswerContent(req.getAnswerContent()) ;
        updateInfo.setUpdateTime(new Date()) ;
        patientQuestionRecordMapper.updateByPrimaryKeySelective(updateInfo) ;
        return true;
    }

    /**
     * 患者端 随访患者填写随访问卷：开始提交
     *
     * @param dto 问卷及随访信息详情
     * @return
     */
    @Override
    public void followUpSubmitQuestion(PatientQuestionDTO req) {

        if ( null == req.getEventHistoryId())
            throw new ServiceException("事件不存在") ;

        PatientQuestionRecord query = new PatientQuestionRecord();
        query.setIsDelete(YesNoEnum.NO.getCode());
        query.setPatientEventId(req.getEventHistoryId());
        PatientQuestionRecord record = patientQuestionRecordMapper.selectOne(query);
        if(record == null)
            throw new ServiceException("记录不存在") ;

        PatientQuestionRecord updateDTO = new PatientQuestionRecord() ;
        updateDTO.setId(record.getId()) ;
        updateDTO.setUpdateTime(new Date()) ;
        updateDTO.setSubmitTime(new Date()) ;
        updateDTO.setAnswerContent(req.getAnswerContent()) ;
        updateDTO.setFillStatus(FillQuestionStatusEnum.FINISHED.getIndex());
        patientQuestionRecordMapper.updateByPrimaryKeySelective(updateDTO) ;

        //更新事件的完成状态
        PatientTaskEventHistoryDTO dto = new PatientTaskEventHistoryDTO();
        dto.setId(req.getEventHistoryId());
        dto.setHospitalId(record.getHospitalId());
        followUpPatientService.updateEventStatus(dto, new TaskAndEventStatus());

        //触发智能执行
        PatientTaskEventHistory eventHistory = patientTaskEventHistoryMapper.getById(req.getEventHistoryId());
        if(eventHistory == null || eventHistory.getItemRecordId() < 0) //如果itemRecordId小于0，则表示是智能执行所触发的问卷事件，则时候无需再触发智能执行！！！
            return;
        req.setHospitalId(record.getHospitalId());
        req.setQuestionId(record.getQuestionId());
        req.setUserId(record.getUserId());
        req.setPatientId(record.getPatientId());
        req.setFollowUpId(record.getFollowUpId()); //处方本随访下的且包含该问卷的智能执行，而不是全部随访下的
        req.setFollowUpItemId(eventHistory.getItemId()); //随访下的指定分项
        followUpAsyncService.questionSmartExecutor(req);

    }

    @Transactional(rollbackFor = Exception.class)
    public void tempSaveQuestion(PatientQuestionDTO req) {

        if ( null==req.getId() ) {
            throw new ServiceException("填写问卷id缺失") ;
        }

        PatientQuestionRecord patientQuestionRecord = patientQuestionRecordMapper.selectByPrimaryKey(req.getId());
        if ( null==patientQuestionRecord || YesNoEnum.YES.getCode().equals(patientQuestionRecord.getIsDelete()) ) {
            throw new ServiceException("没有数据") ;
        }

        Question questionQuery = new Question();
        questionQuery.setId(patientQuestionRecord.getQuestionId());
        questionQuery.setIsDelete(YesNoEnum.NO.getCode());

        Question questionDO = questionMapper.selectById(questionQuery);
        if ( null==questionDO ) {
            throw new ServiceException("没有此问卷") ;
        }

        if ( !QuestionStatusEnum.PUBLISH.getIndex().equals(questionDO.getStatus()) ) {
            throw new ServiceException("问卷暂不支持被填写") ;
        }

        sycQuestionStatusService.sycQuestionStatus();

        Date now = new Date();
        PatientQuestionRecord updateDTO = new PatientQuestionRecord() ;
        updateDTO.setId(req.getId()) ;
        updateDTO.setUpdateTime(now) ;
        updateDTO.setAnswerContent(req.getAnswerContent()) ;
        patientQuestionRecordMapper.updateByPrimaryKeySelective(updateDTO) ;

    }


}
