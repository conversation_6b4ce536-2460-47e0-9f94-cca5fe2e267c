package com.puree.followup.queue;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.puree.hospital.common.notification.queue.consumer.INotificationConsumerBuilder;
import com.puree.hospital.common.redis.mq.IConsumerBuilder;
import com.puree.hospital.common.redis.mq.RedisStreamConsumer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 随访相关的消费者注册器
 * </p>
 *
 * <AUTHOR>
 * @date 2025/3/5 18:43
 */
@Component("consumerBuilder")
public class FollowUpConsumerBuilder implements IConsumerBuilder {

    /**
     * 服务通知相关的消费者注册器
     */
    @Resource
    private List<INotificationConsumerBuilder<?>> notificationConsumerBuilders;

    @Override
    public List<RedisStreamConsumer<?>> buildConsumers() {
        List<RedisStreamConsumer<?>> consumers = Lists.newArrayList();
        // 服务通知相关的消费者注册器
        if (CollectionUtil.isNotEmpty(notificationConsumerBuilders)) {
            notificationConsumerBuilders.forEach(builder -> consumers.addAll(builder.buildConsumerList()));
        }
        return consumers;
    }
}
