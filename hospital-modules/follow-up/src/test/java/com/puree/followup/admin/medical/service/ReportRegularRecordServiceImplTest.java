//package com.puree.followup.admin.medical.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.puree.followup.domain.medical.constant.RegularRecordParentEnum;
//import com.puree.followup.domain.medical.vo.ReportRegularRecordVO;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//import java.util.List;
//
//@Slf4j
//@ExtendWith({MockitoExtension.class, SpringExtension.class})
//@SpringBootTest
//public class ReportRegularRecordServiceImplTest {
//
//    @Autowired
//    private ReportRegularRecordService reportRegularRecordService;
//
//
//    /**
//     * @Param
//     * @Return void
//     * @Description 获取患者端首页
//     * <AUTHOR>
//     * @Date 2024/5/14 12:18
//     **/
//    @Test
//    void testPatientHome() {
//        List<ReportRegularRecordVO> page = reportRegularRecordService.getPage("410522198606107258", RegularRecordParentEnum.BM);
//        log.info("page:{}", JSONObject.toJSONString(page));
//
//    }
//
//}
