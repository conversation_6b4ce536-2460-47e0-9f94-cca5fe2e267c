//package com.puree.followup.admin.medical.service;
//
//import com.puree.followup.admin.medical.mapper.ReportRecordMapper;
//import com.puree.followup.admin.medical.service.impl.ReportRecordServiceImpl;
//import com.puree.followup.domain.medical.constant.IsHandleEnum;
//import com.puree.followup.domain.medical.constant.RecordTypeEnum;
//import com.puree.followup.domain.medical.model.ReportRecord;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//import java.util.Date;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//
//@Slf4j
//@ExtendWith({MockitoExtension.class, SpringExtension.class})
//@SpringBootTest
//public class ReportRecordServiceImplTest {
//
//    @Autowired
//    private ReportRecordMapper reportRecordMapper;
//
//    @Autowired
//    private ReportRecordServiceImpl reportRecordService;
//
//    /**
//     * @Param
//     * @Return void
//     * @Description TODO
//     * <AUTHOR>
//     * @Date 2024/5/14 12:18
//     **/
//    @Test
//    void testGetById() {
//
//        ReportRecord actualReportRecord = reportRecordService.getById(1L);
//        log.info("actualReportRecord:{}", actualReportRecord);
//    }
//
//    @Test
//    public void testInsert() {
//        ReportRecord reportRecord = getReportRecordData();
//        // Act
//        ReportRecord result = reportRecordService.insert(reportRecord);
//
//        log.info("result:{}",result);
//    }
//
//
//    public ReportRecord getReportRecordData() {
//        ReportRecord reportRecord = new ReportRecord();
//        reportRecord.setRecordType(RecordTypeEnum.MEDICAL_REPORT);
//        reportRecord.setReportId("123456");
//        reportRecord.setReportJson("{\"data\":\"test report json\"}");
//        reportRecord.setExamDate(new Date());
//        reportRecord.setReportVer(1);
//        reportRecord.setPatientIdNumber("123456");
//        reportRecord.setCreateTime(new Date());
//        reportRecord.setExamId("exam123");
//        reportRecord.setHospitalId(1L);
//        reportRecord.setIsHandle(IsHandleEnum.PROCESSED);
//        reportRecord.setReportDate(new Date());
//        reportRecord.setDeviceId("device123");
//        reportRecord.setOrgId(1L);
////        reportRecord.setId(1L);
//
//        return reportRecord;
//    }
//
//
//}
