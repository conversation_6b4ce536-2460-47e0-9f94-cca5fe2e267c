//package com.puree.followup.admin.drools.service.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.ArrayUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.puree.followup.admin.drools.service.DroolsActuatorService;
//import com.puree.followup.admin.drools.service.DroolsRuleService;
//import com.puree.followup.domain.followup.dto.FollowUpJoinRuleDTO;
//import com.puree.followup.domain.followup.model.joinrule.JoinRule;
//import com.puree.followup.domain.medical.model.ReportDataSummary;
//import com.puree.hospital.followup.api.model.medical.json.InspectItemJson;
//import com.puree.hospital.followup.api.model.medical.json.InspectSummaryJson;
//import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO;
//import com.puree.hospital.app.api.model.BusPatientFamilyVo;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.kie.api.runtime.KieSession;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//import org.springframework.util.Assert;
//
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@Slf4j
//@SpringBootTest
//@ExtendWith(SpringExtension.class)
//public class DroolsGenerateServiceImplTest {
//
//    @Autowired
//    private DroolsGenerateServiceImpl droolsGenerateService;
//    @Autowired
//    private DroolsActuatorService droolsActuatorService;
//    @Autowired
//    private DroolsRuleService droolsRuleService;
//
//
//
//
//    @Test
//    public void testJoinRuleGenerate() {
//        String json = "{\"joinRulePushData\": [{\"settingType\": \"suggestionList\", \"firstLevelOption\": \"体检报告\", \"secondLevelOptions\": \"总检建议\", \"comparisonOperatorEnum\": \"IS_NULL\"}, {\"settingType\": \"textTopic\", \"firstLevelOption\": \"一般检测项目\", \"secondLevelOptions\": \"体温\", \"comparisonOperatorEnum\": \"IS_NULL\"}]}";
//        //String json = "{\"joinRuleHealthRecord\": [{\"settingType\": \"suggestionList\", \"firstLevelOption\": \"患者资料\", \"secondLevelOptions\": \"详细地址\", \"comparisonOperatorEnum\": \"NOT_NULL\"}]}";
//        JoinRule rule = JSONObject.parseObject(json, JoinRule.class);
//        FollowUpJoinRuleDTO dto1 = new FollowUpJoinRuleDTO();
//        dto1.setRule(rule);
//        dto1.setFollowUpId(1L);
//        dto1.setHospitalId(1L);
//        dto1.setBeginDate(null);
//
//
//
//        droolsGenerateService.joinRuleGenerate(dto1);
//
//    }
//
//    /**
//     * @Param
//     * @Return void
//     * @Description 双值
//     * <AUTHOR>
//     * @Date 2024/4/22 14:32
//     **/
//    @Test
//    public void testJoinRuleGenerateThree() {
//
//        ExamReportInfoDTO dto = getExamReportInfoDTO();
//
//        String rule = getRule();
//
//        KieSession kieSession = droolsActuatorService.getKieSession(rule, null);
//        kieSession.insert(dto);
//        int i = droolsActuatorService.fireAllRules(kieSession);
//
//        System.out.println(i);
//
//    }
//
//    public String getItemJson() {
//        return "{\"BMI\": {\"name\": \"BMI\", \"unit\": \"kg/㎡\", \"result\": 19.26, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"19.26\", \"sourceTyep\": \"DATA_PUSH\"}, \"体温\": {\"name\": \"体温\", \"unit\": \"℃\", \"result\": 36.4, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"36.4\", \"sourceTyep\": \"DATA_PUSH\"}, \"体重\": {\"name\": \"体重\", \"unit\": \"kg\", \"result\": 55.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"55.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"尿酸\": {\"name\": \"尿酸\", \"unit\": \"mmol/L\", \"result\": 0.8, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"0.8\", \"sourceTyep\": \"DATA_PUSH\"}, \"心率\": {\"name\": \"心率\", \"unit\": \"次/分\", \"result\": 95.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"95.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"脉搏\": {\"name\": \"脉搏\", \"unit\": \"次/分\", \"result\": 80.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"80.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"身高\": {\"name\": \"身高\", \"unit\": \"cm\", \"result\": 169.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"169.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"总水分\": {\"name\": \"总水分\", \"unit\": \"kg\", \"result\": 38.4, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"38.4\", \"sourceTyep\": \"DATA_PUSH\"}, \"收缩压\": {\"name\": \"收缩压\", \"unit\": \"mmHg\", \"result\": 108.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"108.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"无机盐\": {\"name\": \"无机盐\", \"unit\": \"kg\", \"result\": 5.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"5.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"舒张压\": {\"name\": \"舒张压\", \"unit\": \"mmHg\", \"result\": 145.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"145.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"蛋白质\": {\"name\": \"蛋白质\", \"unit\": \"kg\", \"result\": 10.2, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"10.2\", \"sourceTyep\": \"DATA_PUSH\"}, \"心率(左)\": {\"name\": \"心率(左)\", \"unit\": \"次/分\", \"result\": 100.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"100.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"潜血(BLO)\": {\"name\": \"潜血(BLO)\", \"unit\": \"Cell/µl\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"酮体(KET)\": {\"name\": \"酮体(KET)\", \"unit\": \"mg/dl\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"体脂肪率\": {\"name\": \"体脂肪率\", \"unit\": \"%\", \"result\": 18.8, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"18.8\", \"sourceTyep\": \"DATA_PUSH\"}, \"体脂肪量\": {\"name\": \"体脂肪量\", \"unit\": \"kg\", \"result\": 12.97, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"12.97\", \"sourceTyep\": \"DATA_PUSH\"}, \"基础代谢\": {\"name\": \"基础代谢\", \"unit\": \"Kcal\", \"result\": 1529.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"1529.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"总胆固醇\": {\"name\": \"总胆固醇\", \"unit\": \"mmol/L\", \"result\": 1.8, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 587, \"resultStr\": \"1.8\", \"sourceTyep\": \"DATA_PUSH\"}, \"水分含量\": {\"name\": \"水分含量\", \"unit\": \"%\", \"result\": 55.7, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"55.7\", \"sourceTyep\": \"DATA_PUSH\"}, \"甘油三酯\": {\"name\": \"甘油三酯\", \"unit\": \"mmol/L\", \"result\": 3.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"3.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"空腹血糖\": {\"name\": \"空腹血糖\", \"unit\": \"mmol/L\", \"result\": 1.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"1.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"细胞内液\": {\"name\": \"细胞内液\", \"unit\": \"kg\", \"result\": 22.3, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"22.3\", \"sourceTyep\": \"DATA_PUSH\"}, \"细胞外液\": {\"name\": \"细胞外液\", \"unit\": \"kg\", \"result\": 16.1, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"16.1\", \"sourceTyep\": \"DATA_PUSH\"}, \"随机血糖\": {\"name\": \"随机血糖\", \"unit\": \"mmol/L\", \"result\": 11.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"11.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"静息心率\": {\"name\": \"静息心率\", \"unit\": \"次/分\", \"result\": 84.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"84.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"骨骼肌率\": {\"name\": \"骨骼肌率\", \"unit\": \"%\", \"result\": 31.9, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"31.9\", \"sourceTyep\": \"DATA_PUSH\"}, \"尿比重(SG)\": {\"name\": \"尿比重(SG)\", \"result\": 1.03, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"1.03\", \"sourceTyep\": \"DATA_PUSH\"}, \"酸碱度(PH)\": {\"name\": \"酸碱度(PH)\", \"result\": 5.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"5.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"尿胆原(URO)\": {\"name\": \"尿胆原(URO)\", \"unit\": \"mg/dl\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"尿蛋白(PRO)\": {\"name\": \"尿蛋白(PRO)\", \"unit\": \"mg/dl\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"收缩压(左)\": {\"name\": \"收缩压(左)\", \"unit\": \"mmHg\", \"result\": 90.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"90.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"白细胞(LEU)\": {\"name\": \"白细胞(LEU)\", \"unit\": \"Cell/µl\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"维生素C(VC)\": {\"name\": \"维生素C(VC)\", \"unit\": \"mmol/L\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"胆红素(BIL)\": {\"name\": \"胆红素(BIL)\", \"unit\": \"mg/dl\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"舒张压(左)\": {\"name\": \"舒张压(左)\", \"unit\": \"mmHg\", \"result\": 99.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"99.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"葡萄糖(GLU)\": {\"name\": \"葡萄糖(GLU)\", \"unit\": \"mg/dl\", \"result\": 4.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"+4\", \"sourceTyep\": \"DATA_PUSH\"}, \"餐后2H血糖\": {\"name\": \"餐后2H血糖\", \"unit\": \"mmol/L\", \"result\": 9.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"9.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"血氧饱和度\": {\"name\": \"血氧饱和度\", \"unit\": \"%\", \"result\": 100.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"100.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"亚硝酸盐(NIT)\": {\"name\": \"亚硝酸盐(NIT)\", \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"-\", \"sourceTyep\": \"DATA_PUSH\"}, \"总胆固醇(TCH)\": {\"name\": \"总胆固醇(TCH)\", \"unit\": \"mmol/L\", \"result\": 7.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"7.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"内脏脂肪等级\": {\"name\": \"内脏脂肪等级\", \"result\": 10.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"10.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"中心动脉血压值(CSBP)\": {\"name\": \"中心动脉血压值(CSBP)\", \"unit\": \"mmHg\", \"result\": 52.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"52.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"低密度脂蛋白胆固醇\": {\"name\": \"低密度脂蛋白胆固醇\", \"unit\": \"mmol/L\", \"result\": 7.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"7.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"高密度脂蛋白胆固醇\": {\"name\": \"高密度脂蛋白胆固醇\", \"unit\": \"mmol/L\", \"result\": 5.0, \"abnormal\": \"NORMAL\", \"examDate\": 1721145600000, \"recordId\": 600, \"resultStr\": \"5.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"上肢动脉硬度指标(API)\": {\"name\": \"上肢动脉硬度指标(API)\", \"result\": 69.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"69.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"中心动脉硬度指标(AVI)\": {\"name\": \"中心动脉硬度指标(AVI)\", \"result\": 58.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"58.0\", \"sourceTyep\": \"DATA_PUSH\"}, \"中心动脉脉压值（CAPP）\": {\"name\": \"中心动脉脉压值（CAPP）\", \"unit\": \"mmHg\", \"result\": 74.0, \"abnormal\": \"OTHER_ABNORMALITIES\", \"examDate\": 1721145600000, \"recordId\": 581, \"resultStr\": \"74.0\", \"sourceTyep\": \"DATA_PUSH\"}}";
//    }
//
//
//    /**
//     * @Param itemList
//     * @Param clazz
//     * @Return T
//     * @Description 模拟方法
//     * <AUTHOR>
//     * @Date 2024/8/5 12:32
//     **/
//    private <T> T[] reportDataSummaryToItemJsonArray(List<ReportDataSummary> itemList, Class<T> clazz) {
//        Assert.notNull(itemList, "itemJson 不能为空");
//        //只会存在一个
//        List<T> collect = itemList.stream().map(item -> {
//            String json = item.getJson();
//            return JSONObject.parseObject(json, clazz);
//        }).collect(Collectors.toList());
//        return ArrayUtil.toArray(collect, clazz);
//    }
//
//    /**
//     * @Param
//     * @Return List<ReportDataSummary>
//     * @Description 模拟数据
//     * <AUTHOR>
//     * @Date 2024/8/5 12:32
//     **/
//    private List<ReportDataSummary> getReportDataSummaryList() {
//        ReportDataSummary summary1 = new ReportDataSummary();
//        ReportDataSummary summary2 = new ReportDataSummary();
//
//        summary1.setJson("{\"name\": \"血糖检测\", \"examDate\": 1721145600000, \"recordId\": 600, \"sourceTyep\": \"DATA_PUSH\"}");
//        summary2.setJson("{\"name\": \"心电检测\", \"examDate\": 1721145600000, \"recordId\": 600, \"sourceTyep\": \"DATA_PUSH\"}");
//
//        return CollUtil.toList(summary1, summary2);
//    }
//
//    /**
//     * @Param
//     * @Return void
//     * @Description 测试转换list
//     * <AUTHOR>
//     * @Date 2024/8/5 12:33
//     **/
//    @Test
//    public void testReportDataSummaryToItemJsonArray() {
//        List<ReportDataSummary> list = getReportDataSummaryList();
//        InspectSummaryJson[] inspectSummaryJsons = reportDataSummaryToItemJsonArray(list, InspectSummaryJson.class);
//        log.info("inspectSummaryJsons:{}",inspectSummaryJsons);
//    }
//
//
//    /**
//     * @Param
//     * @Return void
//     * @Description 测试转换map
//     * <AUTHOR>
//     * @Date 2024/8/5 12:33
//     **/
//    @Test
//    public void testReportDataSummaryToItemJsonMap() {
//
//        String str = getItemJson();
//
//        Map<String,JSONObject> map = JSONObject.parseObject(str, Map.class);
//
//        Map<String,InspectItemJson> itemMap = new HashMap<>(map.size()*2);
//
//        map.forEach((k,v)->{
//            itemMap.put(k,JSONObject.toJavaObject(v, InspectItemJson.class));
//        });
//
//        InspectItemJson itemJson = itemMap.get("BMI");
//        log.info("itemJson:{}",itemJson);
//    }
//
//
//
//    /**
//     * @Param
//     * @Return java.lang.String
//     * @Description json
//     * <AUTHOR>
//     * @Date 2024/5/27 15:02
//     **/
//    public String getRule() {
//        String json = "import com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO\n" +
//                "\n" +
//                "global com.puree.followup.domain.drools.ResultGlobal result\n" +
//                "\n" +
//                "rule rule_1928366439557541888\n" +
//                "\tsalience 2\n" +
//                "\tdialect \"java\"\n" +
//                "\twhen\n" +
//                "\t\texists (ExamReportInfoDTO(/conclusions/examSuggestDTOS[suggest   == null ]))\n" +
//                "\n" +
//                "\tthen\n" +
//                "\t\t\tresult.setResult(true);\n" +
//                "\tdrools.halt();\n" +
//                "end\n" +
//                "\n" +
//                "rule rule_1928366439565930496\n" +
//                "\tsalience 3\n" +
//                "\tdialect \"java\"\n" +
//                "\twhen\n" +
//                "\t\texists (ExamReportInfoDTO(/examSummaries/items[name == \"体温\" && desc   == null]))\n" +
//                "\n" +
//                "\tthen\n" +
//                "\t\t\tresult.setResult(true);\n" +
//                "\tdrools.halt();\n" +
//                "end";
//        return json;
//    }
//
//    /**
//     * @Param
//     * @Return com.puree.hospital.followup.api.model.medical.upload.dto.ExamReportInfoDTO
//     * @Description 获取体检推送数据
//     * <AUTHOR>
//     * @Date 2024/5/20 17:30
//     **/
//    public ExamReportInfoDTO getExamReportInfoDTO() {
//        String json = "{\n" +
//                "    \"orgId\": \"123\",\n" +
//                "    \"bizId\": \"240412600014\",\n" +
//                "    \"examId\": \"240412600014\",\n" +
//                "    \"examDate\": \"2024-06-04\",\n" +
//                "    \"reportId\": \"240412600014\",\n" +
//                "    \"reportVer\": \"1\",\n" +
//                "    \"reportDate\": \"2024-06-06\",\n" +
//                "    \"reportFile\": \"\",\n" +
//                "    \"patientId\": \"240412600014\",\n" +
//                "    \"patientInfo\": {\n" +
//                "        \"name\": \"杨燕波\",\n" +
//                "        \"idCard\": \"******************\",\n" +
//                "        \"gender\": \"male\",\n" +
//                "        \"age\": \"37\",\n" +
//                "        \"phoneNum\": \"19358881693\",\n" +
//                "        \"job\": \"无业游民\",\n" +
//                "        \"detailAddress\": \"xxxxx\",\n" +
//                "        \"workplace\": \"个人健康体检\",\n" +
//                "        \"address\": \"广州市黄埔区南岗西路228号\"\n" +
//                "    },\n" +
//                "    \"conclusions\": {\n" +
//                "        \"items\": [\n" +
//                "            {\n" +
//                "                \"name\": \"血压\",\n" +
//                "                \"desc\": \"血压 162/120mmHg : 血压偏高\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"name\": \"身高、体重\",\n" +
//                "                \"desc\": \"体重指数:39.14:肥胖\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"name\": \"肝胆胰脾体检彩超\",\n" +
//                "                \"desc\": \"脂肪肝。\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"name\": \"睾酮\",\n" +
//                "                \"desc\": \"睾酮(T)偏低(233.00 ng/dL)\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"name\": \"血常规5分类\",\n" +
//                "                \"desc\": \"白细胞计数(WBC)偏高(9.87 *10^9/L);中性粒细胞绝对值(GR#)偏高(7.01 *10^9/L);红细胞计数(RBC)偏高(6.02 *10^12/L);平均红细胞体积(MCV)偏低(79.0 fL);平均红细胞血红蛋白含量(MCH)偏低(25.9 pg);血小板计数(PLT)偏高(385 *10^9/L);血小板平均体积(MPV)偏低(7.2 fL);大型血小板比率(P-LCR)偏低(9.6 %)\"\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"name\": \"泌尿系+前列腺体检彩超\",\n" +
//                "                \"desc\": \"双肾结石。前列腺大小正常，可见钙化斑。\"\n" +
//                "            }\n" +
//                "        ],\n" +
//                "        \"suggest\": [\n" +
//                "            \"★  血压偏高\u007F：血压明显偏高,及时到内科降压治疗。适当低盐、低脂、低胆固醇饮食，戒烟酒。\",\n" +
//                "            \"★  肥胖\u007F脂肪肝：低脂低糖饮食，限酒，多运动，适当减少食量，适度减肥，坚持锻炼，定期复查血脂、肝功及肝胆B超。\",\n" +
//                "            \"★  睾酮(T)偏低(233.00 ng/dL)：建议外科咨询就诊。\",\n" +
//                "            \"★  红细胞计数(RBC)偏高(6.02 *10^12/L);平均红细胞体积(MCV)偏低(79.0 fL);平均红细胞血红蛋白含量(MCH)偏低：红系异常，建议内科就诊行地中海贫血基因筛查，针对病因治疗。\",\n" +
//                "            \"★  双肾结石\u007F：(1)定期B超复查。平时适量运动，多饮水。(2)如有腰区疼痛或尿急尿痛血尿等症状，请到泌尿外科就诊治疗。\u007F\",\n" +
//                "            \"★  前列腺钙化斑：建议每年定期复查。\"\n" +
//                "        ]\n" +
//                "    },\n" +
//                "    \"regularExam\": {\n" +
//                "        \"tall\": \"199\",\n" +
//                "        \"weight\": \"66\",\n" +
//                "        \"bmi\": \"39.1\",\n" +
//                "        \"systolic\": \"162\",\n" +
//                "        \"diastolic\": \"120\",\n" +
//                "        \"heartRate\":\"88\",\n" +
//                "        \"pulseRate\": \"99\",\n" +
//                "        \"bloodSugar\": \"3\",\n" +
//                "        \"bodyTemperature\": \"38.2\",\n" +
//                "        \"totalCholesterol\": \"2\",\n" +
//                "        \"triglycerides\": \"1.1\",\n" +
//                "        \"hdl\": \"1.3\",\n" +
//                "        \"ldl\": \"1.6\",\n" +
//                "        \"uricAcid\": \"10\",\n" +
//                "        \"bloodOxygen\": \"80\",\n" +
//                "        \"bmiAbnormal\": \"high\",\n" +
//                "        \"systolicAbnormal\": \"high\",\n" +
//                "        \"diastolicAbnormal\": \"high\",\n" +
//                "        \"summary\": \"\"\n" +
//                "    },\n" +
//                "    \"inspectSummaries\": [\n" +
//                "        {\n" +
//                "            \"subject\": \"血常规5分类\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0243\",\n" +
//                "                    \"name\": \"白细胞计数(WBC)\",\n" +
//                "                    \"result\": \"9.87\",\n" +
//                "                    \"unit\": \"*10^9/L\",\n" +
//                "                    \"range\": \"3.50 - 9.50\",\n" +
//                "                    \"refUpperLimit\": \"9.50\",\n" +
//                "                    \"refLowerLimit\": \"3.50\",\n" +
//                "                    \"abnormal\": \"high\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0231\",\n" +
//                "                    \"name\": \"淋巴细胞比值(LY%)\",\n" +
//                "                    \"result\": \"20.5\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"20.0 - 50.0\",\n" +
//                "                    \"refUpperLimit\": \"50.0\",\n" +
//                "                    \"refLowerLimit\": \"20.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0230\",\n" +
//                "                    \"name\": \"中性粒细胞比值(GR%)\",\n" +
//                "                    \"result\": \"71.0\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"40.0 - 75.0\",\n" +
//                "                    \"refUpperLimit\": \"75.0\",\n" +
//                "                    \"refLowerLimit\": \"40.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0092\",\n" +
//                "                    \"name\": \"单核细胞比值(MONO%)\",\n" +
//                "                    \"result\": \"5.2\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"3.0 - 10.0\",\n" +
//                "                    \"refUpperLimit\": \"10.0\",\n" +
//                "                    \"refLowerLimit\": \"3.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0047\",\n" +
//                "                    \"name\": \"嗜酸性粒细胞比值(EO%)\",\n" +
//                "                    \"result\": \"3.0\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"0.4 - 8.0\",\n" +
//                "                    \"refUpperLimit\": \"8.0\",\n" +
//                "                    \"refLowerLimit\": \"0.4\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0048\",\n" +
//                "                    \"name\": \"嗜碱性粒细胞比值(BA%)\",\n" +
//                "                    \"result\": \"0.3\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"0.0 - 1.0\",\n" +
//                "                    \"refUpperLimit\": \"1.0\",\n" +
//                "                    \"refLowerLimit\": \"0.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0045\",\n" +
//                "                    \"name\": \"淋巴细胞绝对值(LY#)\",\n" +
//                "                    \"result\": \"2.02\",\n" +
//                "                    \"unit\": \"*10^9/L\",\n" +
//                "                    \"range\": \"1.1 - 3.2\",\n" +
//                "                    \"refUpperLimit\": \"3.2\",\n" +
//                "                    \"refLowerLimit\": \"1.1\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0046\",\n" +
//                "                    \"name\": \"中性粒细胞绝对值(GR#)\",\n" +
//                "                    \"result\": \"7.01\",\n" +
//                "                    \"unit\": \"*10^9/L\",\n" +
//                "                    \"range\": \"1.80 - 6.30\",\n" +
//                "                    \"refUpperLimit\": \"6.30\",\n" +
//                "                    \"refLowerLimit\": \"1.80\",\n" +
//                "                    \"abnormal\": \"high\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0049\",\n" +
//                "                    \"name\": \"单核细胞绝对值(MO#)\",\n" +
//                "                    \"result\": \"0.51\",\n" +
//                "                    \"unit\": \"*10^9/L\",\n" +
//                "                    \"range\": \"0.10 - 0.60\",\n" +
//                "                    \"refUpperLimit\": \"0.60\",\n" +
//                "                    \"refLowerLimit\": \"0.10\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0229\",\n" +
//                "                    \"name\": \"嗜酸性粒细胞绝对值(EO#)\",\n" +
//                "                    \"result\": \"0.30\",\n" +
//                "                    \"unit\": \"*10^9/L\",\n" +
//                "                    \"range\": \"0.02 - 0.52\",\n" +
//                "                    \"refUpperLimit\": \"0.52\",\n" +
//                "                    \"refLowerLimit\": \"0.02\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0228\",\n" +
//                "                    \"name\": \"嗜碱性粒细胞绝对值(BA#)\",\n" +
//                "                    \"result\": \"0.03\",\n" +
//                "                    \"unit\": \"*10^9/L\",\n" +
//                "                    \"range\": \"0.00 - 0.06\",\n" +
//                "                    \"refUpperLimit\": \"0.06\",\n" +
//                "                    \"refLowerLimit\": \"0.00\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0242\",\n" +
//                "                    \"name\": \"红细胞计数(RBC)\",\n" +
//                "                    \"result\": \"6.02\",\n" +
//                "                    \"unit\": \"*10^12/L\",\n" +
//                "                    \"range\": \"4.30 - 5.80\",\n" +
//                "                    \"refUpperLimit\": \"5.80\",\n" +
//                "                    \"refLowerLimit\": \"4.30\",\n" +
//                "                    \"abnormal\": \"high\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0220\",\n" +
//                "                    \"name\": \"血红蛋白浓度(HGB)\",\n" +
//                "                    \"result\": \"156\",\n" +
//                "                    \"unit\": \"g/L\",\n" +
//                "                    \"range\": \"130 - 175\",\n" +
//                "                    \"refUpperLimit\": \"175\",\n" +
//                "                    \"refLowerLimit\": \"130\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0221\",\n" +
//                "                    \"name\": \"红细胞压积(HCT)\",\n" +
//                "                    \"result\": \"0.480\",\n" +
//                "                    \"unit\": \"\",\n" +
//                "                    \"range\": \"0.40 - 0.50\",\n" +
//                "                    \"refUpperLimit\": \"0.50\",\n" +
//                "                    \"refLowerLimit\": \"0.40\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0222\",\n" +
//                "                    \"name\": \"平均红细胞体积(MCV)\",\n" +
//                "                    \"result\": \"79.0\",\n" +
//                "                    \"unit\": \"fL\",\n" +
//                "                    \"range\": \"82.0 - 100.0\",\n" +
//                "                    \"refUpperLimit\": \"100.0\",\n" +
//                "                    \"refLowerLimit\": \"82.0\",\n" +
//                "                    \"abnormal\": \"low\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0076\",\n" +
//                "                    \"name\": \"平均红细胞血红蛋白含量(MCH)\",\n" +
//                "                    \"result\": \"25.9\",\n" +
//                "                    \"unit\": \"pg\",\n" +
//                "                    \"range\": \"27.0 - 34.0\",\n" +
//                "                    \"refUpperLimit\": \"34.0\",\n" +
//                "                    \"refLowerLimit\": \"27.0\",\n" +
//                "                    \"abnormal\": \"low\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0223\",\n" +
//                "                    \"name\": \"平均红细胞血红蛋白浓度(MCHC)\",\n" +
//                "                    \"result\": \"328\",\n" +
//                "                    \"unit\": \"g/L\",\n" +
//                "                    \"range\": \"316 - 354\",\n" +
//                "                    \"refUpperLimit\": \"354\",\n" +
//                "                    \"refLowerLimit\": \"316\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0091\",\n" +
//                "                    \"name\": \"红细胞分布宽度变异系数(RDW-CV)\",\n" +
//                "                    \"result\": \"14.2\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"11.5 - 14.5\",\n" +
//                "                    \"refUpperLimit\": \"14.5\",\n" +
//                "                    \"refLowerLimit\": \"11.5\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0224\",\n" +
//                "                    \"name\": \"红细胞分布宽度标准差(RDW-SD)\",\n" +
//                "                    \"result\": \"39.6\",\n" +
//                "                    \"unit\": \"fL\",\n" +
//                "                    \"range\": \"37.1 - 49.2\",\n" +
//                "                    \"refUpperLimit\": \"49.2\",\n" +
//                "                    \"refLowerLimit\": \"37.1\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0225\",\n" +
//                "                    \"name\": \"血小板计数(PLT)\",\n" +
//                "                    \"result\": \"385\",\n" +
//                "                    \"unit\": \"*10^9/L\",\n" +
//                "                    \"range\": \"125 - 350\",\n" +
//                "                    \"refUpperLimit\": \"350\",\n" +
//                "                    \"refLowerLimit\": \"125\",\n" +
//                "                    \"abnormal\": \"high\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0226\",\n" +
//                "                    \"name\": \"血小板平均体积(MPV)\",\n" +
//                "                    \"result\": \"7.2\",\n" +
//                "                    \"unit\": \"fL\",\n" +
//                "                    \"range\": \"7.6 - 13.2\",\n" +
//                "                    \"refUpperLimit\": \"13.2\",\n" +
//                "                    \"refLowerLimit\": \"7.6\",\n" +
//                "                    \"abnormal\": \"low\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0227\",\n" +
//                "                    \"name\": \"血小板分布宽度(PDW)\",\n" +
//                "                    \"result\": \"15.3\",\n" +
//                "                    \"unit\": \"fL\",\n" +
//                "                    \"range\": \"9.6 - 16.1\",\n" +
//                "                    \"refUpperLimit\": \"16.1\",\n" +
//                "                    \"refLowerLimit\": \"9.6\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0050\",\n" +
//                "                    \"name\": \"大型血小板比率(P-LCR)\",\n" +
//                "                    \"result\": \"9.6\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"17.2 - 42.3\",\n" +
//                "                    \"refUpperLimit\": \"42.3\",\n" +
//                "                    \"refLowerLimit\": \"17.2\",\n" +
//                "                    \"abnormal\": \"low\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"白细胞计数(WBC)偏高(9.87 *10^9/L);中性粒细胞绝对值(GR#)偏高(7.01 *10^9/L);红细胞计数(RBC)偏高(6.02 *10^12/L);平均红细胞体积(MCV)偏低(79.0 fL);平均红细胞血红蛋白含量(MCH)偏低(25.9 pg);血小板计数(PLT)偏高(385 *10^9/L);血小板平均体积(MPV)偏低(7.2 fL);大型血小板比率(P-LCR)偏低(9.6 %)\"\n" +
//                "        },\n" +
//                "    \n" +
//                "        {\n" +
//                "            \"subject\": \"胃蛋白酶原\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0767\",\n" +
//                "                    \"name\": \"胃蛋白酶原I\",\n" +
//                "                    \"result\": \"38.9\",\n" +
//                "                    \"unit\": \"ng/mL\",\n" +
//                "                    \"range\": \"30 - 200\",\n" +
//                "                    \"refUpperLimit\": \"200\",\n" +
//                "                    \"refLowerLimit\": \"30\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0768\",\n" +
//                "                    \"name\": \"胃蛋白酶原II\",\n" +
//                "                    \"result\": \"6.5\",\n" +
//                "                    \"unit\": \"ng/mL\",\n" +
//                "                    \"range\": \"\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0584\",\n" +
//                "                    \"name\": \"PGⅠ/PGⅡ\",\n" +
//                "                    \"result\": \"6.0\",\n" +
//                "                    \"unit\": \"\",\n" +
//                "                    \"range\": \"≥3.0\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"≥3.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"肝功8项(新)\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0020\",\n" +
//                "                    \"name\": \"谷丙转氨酶(ALT)\",\n" +
//                "                    \"result\": \"19\",\n" +
//                "                    \"unit\": \"U/L\",\n" +
//                "                    \"range\": \"0 - 50\",\n" +
//                "                    \"refUpperLimit\": \"50\",\n" +
//                "                    \"refLowerLimit\": \"0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0025\",\n" +
//                "                    \"name\": \"谷草转氨酶(AST)\",\n" +
//                "                    \"result\": \"18\",\n" +
//                "                    \"unit\": \"U/L\",\n" +
//                "                    \"range\": \"0 - 40\",\n" +
//                "                    \"refUpperLimit\": \"40\",\n" +
//                "                    \"refLowerLimit\": \"0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0127\",\n" +
//                "                    \"name\": \"谷草/谷丙\",\n" +
//                "                    \"result\": \"0.9\",\n" +
//                "                    \"unit\": \"比值\",\n" +
//                "                    \"range\": \"1.0 - 1.5\",\n" +
//                "                    \"refUpperLimit\": \"1.5\",\n" +
//                "                    \"refLowerLimit\": \"1.0\",\n" +
//                "                    \"abnormal\": \"low\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0027\",\n" +
//                "                    \"name\": \"碱性磷酸酶(ALP)\",\n" +
//                "                    \"result\": \"125\",\n" +
//                "                    \"unit\": \"U/L\",\n" +
//                "                    \"range\": \"45 - 125\",\n" +
//                "                    \"refUpperLimit\": \"125\",\n" +
//                "                    \"refLowerLimit\": \"45\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0026\",\n" +
//                "                    \"name\": \"γ-谷氨酰转肽酶(GGT)\",\n" +
//                "                    \"result\": \"34\",\n" +
//                "                    \"unit\": \"U/L\",\n" +
//                "                    \"range\": \"10 - 60\",\n" +
//                "                    \"refUpperLimit\": \"60\",\n" +
//                "                    \"refLowerLimit\": \"10\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0021\",\n" +
//                "                    \"name\": \"总蛋白(T PROT)\",\n" +
//                "                    \"result\": \"79.8\",\n" +
//                "                    \"unit\": \"g/L\",\n" +
//                "                    \"range\": \"65.0 - 85.0\",\n" +
//                "                    \"refUpperLimit\": \"85.0\",\n" +
//                "                    \"refLowerLimit\": \"65.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0022\",\n" +
//                "                    \"name\": \"白蛋白(ALB)\",\n" +
//                "                    \"result\": \"43.4\",\n" +
//                "                    \"unit\": \"g/L\",\n" +
//                "                    \"range\": \"40.0 - 55.0\",\n" +
//                "                    \"refUpperLimit\": \"55.0\",\n" +
//                "                    \"refLowerLimit\": \"40.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0023\",\n" +
//                "                    \"name\": \"球蛋白(GLB)\",\n" +
//                "                    \"result\": \"36.4\",\n" +
//                "                    \"unit\": \"g/L\",\n" +
//                "                    \"range\": \"19.0 - 40.0\",\n" +
//                "                    \"refUpperLimit\": \"40.0\",\n" +
//                "                    \"refLowerLimit\": \"19.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0024\",\n" +
//                "                    \"name\": \"白蛋白/球蛋白(ALB/GLB)\",\n" +
//                "                    \"result\": \"1.2\",\n" +
//                "                    \"unit\": \"比值\",\n" +
//                "                    \"range\": \"1.2 - 2.5\",\n" +
//                "                    \"refUpperLimit\": \"2.5\",\n" +
//                "                    \"refLowerLimit\": \"1.2\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0645\",\n" +
//                "                    \"name\": \"前白蛋白(PA)\",\n" +
//                "                    \"result\": \"250\",\n" +
//                "                    \"unit\": \"mg/L\",\n" +
//                "                    \"range\": \"200 - 430\",\n" +
//                "                    \"refUpperLimit\": \"430\",\n" +
//                "                    \"refLowerLimit\": \"200\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0644\",\n" +
//                "                    \"name\": \"总胆汁酸(TBA)\",\n" +
//                "                    \"result\": \"6.6\",\n" +
//                "                    \"unit\": \"μmol/L\",\n" +
//                "                    \"range\": \"0.0 - 10.0\",\n" +
//                "                    \"refUpperLimit\": \"10.0\",\n" +
//                "                    \"refLowerLimit\": \"0.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"谷草/谷丙偏低(0.9 )\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"空腹血糖\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0041\",\n" +
//                "                    \"name\": \"空腹葡萄糖(GLU)\",\n" +
//                "                    \"result\": \"4.50\",\n" +
//                "                    \"unit\": \"mmol/L\",\n" +
//                "                    \"range\": \"3.90 - 6.09\",\n" +
//                "                    \"refUpperLimit\": \"6.09\",\n" +
//                "                    \"refLowerLimit\": \"3.90\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"同型半胱氨酸\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0085\",\n" +
//                "                    \"name\": \"同型半胱氨酸\",\n" +
//                "                    \"result\": \"8.3\",\n" +
//                "                    \"unit\": \"μmol/L\",\n" +
//                "                    \"range\": \"<15.0\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"<15.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"糖化血红蛋白\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0097\",\n" +
//                "                    \"name\": \"糖化血红蛋白\",\n" +
//                "                    \"result\": \"5.50\",\n" +
//                "                    \"unit\": \"%\",\n" +
//                "                    \"range\": \"4.8 - 6.0\",\n" +
//                "                    \"refUpperLimit\": \"6.0\",\n" +
//                "                    \"refLowerLimit\": \"4.8\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"肾功能3项\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0065\",\n" +
//                "                    \"name\": \"尿素氮(BUN)\",\n" +
//                "                    \"result\": \"4.04\",\n" +
//                "                    \"unit\": \"mmol/L\",\n" +
//                "                    \"range\": \"3.1 - 8.0\",\n" +
//                "                    \"refUpperLimit\": \"8.0\",\n" +
//                "                    \"refLowerLimit\": \"3.1\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0029\",\n" +
//                "                    \"name\": \"肌酐(CREA)\",\n" +
//                "                    \"result\": \"61\",\n" +
//                "                    \"unit\": \"μmol/L\",\n" +
//                "                    \"range\": \"62 - 115\",\n" +
//                "                    \"refUpperLimit\": \"115\",\n" +
//                "                    \"refLowerLimit\": \"62\",\n" +
//                "                    \"abnormal\": \"low\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0030\",\n" +
//                "                    \"name\": \"尿酸(UA)\",\n" +
//                "                    \"result\": \"359\",\n" +
//                "                    \"unit\": \"μmol/L\",\n" +
//                "                    \"range\": \"208 - 428\",\n" +
//                "                    \"refUpperLimit\": \"428\",\n" +
//                "                    \"refLowerLimit\": \"208\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"肌酐(CREA)偏低(61 μmol/L)\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"胆红素2项\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0062\",\n" +
//                "                    \"name\": \"总胆红素(TBIL)\",\n" +
//                "                    \"result\": \"7.8\",\n" +
//                "                    \"unit\": \"μmol/L\",\n" +
//                "                    \"range\": \"0.0 - 26.0\",\n" +
//                "                    \"refUpperLimit\": \"26.0\",\n" +
//                "                    \"refLowerLimit\": \"0.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0063\",\n" +
//                "                    \"name\": \"直接胆红素(DBIL)\",\n" +
//                "                    \"result\": \"2.5\",\n" +
//                "                    \"unit\": \"μmol/L\",\n" +
//                "                    \"range\": \"0.0 - 6.8\",\n" +
//                "                    \"refUpperLimit\": \"6.8\",\n" +
//                "                    \"refLowerLimit\": \"0.0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0064\",\n" +
//                "                    \"name\": \"间接胆红素(IBIL)\",\n" +
//                "                    \"result\": \"5.3\",\n" +
//                "                    \"unit\": \"μmol/L\",\n" +
//                "                    \"range\": \"\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"血脂4项\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0036\",\n" +
//                "                    \"name\": \"甘油三酯(TG)\",\n" +
//                "                    \"result\": \"1.36\",\n" +
//                "                    \"unit\": \"mmol/L\",\n" +
//                "                    \"range\": \"<1.70\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"<1.70\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0037\",\n" +
//                "                    \"name\": \"总胆固醇(CHOL)\",\n" +
//                "                    \"result\": \"5.06\",\n" +
//                "                    \"unit\": \"mmol/L\",\n" +
//                "                    \"range\": \"<5.20\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"<5.20\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0038\",\n" +
//                "                    \"name\": \"高密度脂蛋白胆固醇(HDL-C)\",\n" +
//                "                    \"result\": \"1.07\",\n" +
//                "                    \"unit\": \"mmol/L\",\n" +
//                "                    \"range\": \">=1.00\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \">=1.00\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0039\",\n" +
//                "                    \"name\": \"低密度脂蛋白胆固醇(LDL-C)\",\n" +
//                "                    \"result\": \"3.31\",\n" +
//                "                    \"unit\": \"mmol/L\",\n" +
//                "                    \"range\": \"<3.40\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"<3.40\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"睾酮\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0177\",\n" +
//                "                    \"name\": \"睾酮(T)\",\n" +
//                "                    \"result\": \"233.00\",\n" +
//                "                    \"unit\": \"ng/dL\",\n" +
//                "                    \"range\": \"280 - 800\",\n" +
//                "                    \"refUpperLimit\": \"800\",\n" +
//                "                    \"refLowerLimit\": \"280\",\n" +
//                "                    \"abnormal\": \"low\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"睾酮(T)偏低(233.00 ng/dL)\"\n" +
//                "        },\n" +
//                "       \n" +
//                "     \n" +
//                "        {\n" +
//                "            \"subject\": \"甲功5项\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0052\",\n" +
//                "                    \"name\": \"促甲状腺激素(TSH)\",\n" +
//                "                    \"result\": \"1.410\",\n" +
//                "                    \"unit\": \"μIU/ml\",\n" +
//                "                    \"range\": \"0.75 - 5.60\",\n" +
//                "                    \"refUpperLimit\": \"5.60\",\n" +
//                "                    \"refLowerLimit\": \"0.75\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0173\",\n" +
//                "                    \"name\": \"游离三碘甲状腺原氨酸(FT3)\",\n" +
//                "                    \"result\": \"5.47\",\n" +
//                "                    \"unit\": \"pmol/L\",\n" +
//                "                    \"range\": \"3.1 - 6.8\",\n" +
//                "                    \"refUpperLimit\": \"6.8\",\n" +
//                "                    \"refLowerLimit\": \"3.1\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0174\",\n" +
//                "                    \"name\": \"游离甲状腺素(FT4)\",\n" +
//                "                    \"result\": \"17.00\",\n" +
//                "                    \"unit\": \"pmol/L\",\n" +
//                "                    \"range\": \"12.80 - 21.30\",\n" +
//                "                    \"refUpperLimit\": \"21.30\",\n" +
//                "                    \"refLowerLimit\": \"12.80\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0105\",\n" +
//                "                    \"name\": \"抗甲状腺球蛋白抗体(A-TG)\",\n" +
//                "                    \"result\": \"17.00\",\n" +
//                "                    \"unit\": \"IU/ml\",\n" +
//                "                    \"range\": \"0 - 115\",\n" +
//                "                    \"refUpperLimit\": \"115\",\n" +
//                "                    \"refLowerLimit\": \"0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0104\",\n" +
//                "                    \"name\": \"甲状腺过氧化物酶抗体(TPOAB)\",\n" +
//                "                    \"result\": \"9.00\",\n" +
//                "                    \"unit\": \"IU/ml\",\n" +
//                "                    \"range\": \"0 - 34\",\n" +
//                "                    \"refUpperLimit\": \"34\",\n" +
//                "                    \"refLowerLimit\": \"0\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"前列腺特异抗原二项\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"id\": \"0209\",\n" +
//                "                    \"name\": \"游离前列腺特异抗原(FPSA)\",\n" +
//                "                    \"result\": \"0.19\",\n" +
//                "                    \"unit\": \"ng/mL\",\n" +
//                "                    \"range\": \"\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0192\",\n" +
//                "                    \"name\": \"总前列腺特异性抗原(tPSA)\",\n" +
//                "                    \"result\": \"0.36\",\n" +
//                "                    \"unit\": \"ng/mL\",\n" +
//                "                    \"range\": \"<1.4\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \"<1.4\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"id\": \"0107\",\n" +
//                "                    \"name\": \"前列腺特异性抗原比值\",\n" +
//                "                    \"result\": \"0.53\",\n" +
//                "                    \"unit\": \"\",\n" +
//                "                    \"range\": \">0.19\",\n" +
//                "                    \"refUpperLimit\": \"\",\n" +
//                "                    \"refLowerLimit\": \">0.19\",\n" +
//                "                    \"abnormal\": \"normal\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"未见异常\"\n" +
//                "        }\n" +
//                "    ],\n" +
//                "    \"examSummaries\": [\n" +
//                "        {\n" +
//                "            \"subject\": \"碳13呼气试验\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"images\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"videos\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"name\": \"碳13呼气试验\",\n" +
//                "                    \"desc\": \"幽门螺杆菌(Hp)阴性(-)\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"碳13呼气试验:幽门螺杆菌(Hp)阴性(-)\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"成人胸部正位片(不出片)\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"images\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"videos\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"name\": \"成人胸部正位片（不出片）\",\n" +
//                "                    \"desc\": \"双肺及心膈未见明显异常。\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"双肺及心膈未见明显异常。\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"心电图\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"images\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"videos\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"name\": \"静态心电图\",\n" +
//                "                    \"desc\": \"1、窦性心律。2、正常心电图。\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"1、窦性心律。2、正常心电图。\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"甲状腺体检彩超\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"images\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"videos\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"name\": \"甲状腺彩色B超\",\n" +
//                "                    \"desc\": \"甲状腺大小形态正常，双侧叶对称，包膜清晰、光滑，实质回声均匀，腺体内未见明显占位病变。CDFI：甲状腺体内血流不丰富。\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"甲状腺大小正常。甲状腺未见明显异常。\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"泌尿系+前列腺体检彩超\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"images\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"videos\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"name\": \"双肾、输尿管、膀胱、前列腺彩色B超\",\n" +
//                "                    \"desc\": \"双肾大小、形态正常，轮廓线清晰，  双肾集合系统内检出2--3个强回声光团，  大小：3mm--4mm不等，有声影。  双侧输尿管未见扩张。  膀胱内膜平整，壁不厚，未见明显占位病变征。  前列腺大小形态正常，包膜光滑，内部回声欠均匀，可见斑片状强回声。\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"双肾结石。双侧输尿管、膀胱未见明显异常。前列腺大小正常，可见钙化斑。\"\n" +
//                "        },\n" +
//                "        {\n" +
//                "            \"subject\": \"肝胆胰脾体检彩超\",\n" +
//                "            \"examDate\": \"2024-04-12\",\n" +
//                "            \"images\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"videos\": [\n" +
//                "\n" +
//                "            ],\n" +
//                "            \"items\": [\n" +
//                "                {\n" +
//                "                    \"name\": \"肝、胆、胰、脾彩色B超\",\n" +
//                "                    \"desc\": \"右肝斜径：125mm。肝缘变钝，包膜光滑，肝内光点前段密集、明亮，后段衰减，肝内血管显示尚清，肝肾对比增强，肝内暂未见明显占位性病变。  门静脉宽径约10mm，胆总管宽径约5mm。  胆囊大小正常,壁不厚，胆囊腔内未见明显占位病变征。  胰腺大小正常，回声均匀，内未见明显占位病变。  脾脏大小形态正常,内回声均匀，未见明显占位性病变。  CDFI：肝、胆、脾、胰内未见明显异常血流信号。\"\n" +
//                "                }\n" +
//                "            ],\n" +
//                "            \"summary\": \"脂肪肝。  胆、脾、胰超声检查未见明显异常。\"\n" +
//                "        }\n" +
//                "    ]\n" +
//                "}";
//        return JSONObject.parseObject(json, ExamReportInfoDTO.class);
//    }
//}
