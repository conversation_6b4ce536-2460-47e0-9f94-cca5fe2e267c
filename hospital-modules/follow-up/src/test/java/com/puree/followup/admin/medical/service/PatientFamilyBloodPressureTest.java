//package com.puree.followup.admin.medical.service;
//
//import com.alibaba.nacos.client.naming.utils.CollectionUtils;
//import com.puree.followup.domain.medical.constant.RegularRecordEnum;
//import com.puree.followup.domain.medical.model.ReportRecord;
//import com.puree.followup.domain.medical.model.ReportRegularRecord;
//import com.puree.hospital.ehr.api.model.event.PatientFamilyBloodPressureUpdateEvent;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.compress.utils.Lists;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//import javax.annotation.Resource;
//import java.util.Date;
//import java.util.List;
//import java.util.Objects;
//
///**
// * <p>
// * 患者血压数据
// * </p>
// *
// * <AUTHOR>
// * @date 2025/2/21 16:54
// */
//@Slf4j
//@ExtendWith({MockitoExtension.class, SpringExtension.class})
//@SpringBootTest
//public class PatientFamilyBloodPressureTest {
//
//    @Resource
//    private ReportRegularRecordService reportRegularRecordService;
//
////    @Test
//    public void testAAA() {
//        PatientFamilyBloodPressureUpdateEvent event = new PatientFamilyBloodPressureUpdateEvent();
//        event.setHospitalId(100L);
//        event.setFamilyId(211L);
//        event.setFamilyIdNumber("360430199906021717");
//        event.setSys(80);
//        event.setDia(70);
//        event.setSys2(87);
//        event.setDia2(92);
//        event.setPlu(100);
//        event.setIad(3);
//        event.setOpTime(1740128733L);
//        List<ReportRegularRecord> reportRegularRecords = buildRecordList(event);
//        if (CollectionUtils.isEmpty(reportRegularRecords)) {
//            return;
//        }
//        reportRegularRecordService.saveBatchAndSummery(reportRegularRecords);
//    }
//
//    /**
//     * 构建健康报告数据
//     *
//     * @param event 血压数据更新事件
//     * @return 构建后的数据
//     */
//    private List<ReportRegularRecord> buildRecordList(PatientFamilyBloodPressureUpdateEvent event) {
//        if (Objects.isNull(event)) {
//            return Lists.newArrayList();
//        }
//        List<ReportRegularRecord> reportRegularRecords = Lists.newArrayList();
//        //操作时间
//        Date date = Objects.nonNull(event.getOpTime()) ? new Date(event.getOpTime() * 1000) : new Date();
//        ReportRecord reportRecord = new ReportRecord();
//        reportRecord.setHospitalId(event.getHospitalId());
//        reportRecord.setPatientId(event.getFamilyId());
//        reportRecord.setPatientIdNumber(event.getFamilyIdNumber());
//        if (Objects.nonNull(event.getSys())) {
//            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getSys() + "", reportRecord, RegularRecordEnum.LEFT_SYSTOLIC, date));
//        }
//        if (Objects.nonNull(event.getDia())) {
//            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getDia() + "", reportRecord, RegularRecordEnum.LEFT_DIASTOLIC, date));
//        }
//        if (Objects.nonNull(event.getSys2())) {
//            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getSys2() + "", reportRecord, RegularRecordEnum.SYSTOLIC, date));
//        }
//        if (Objects.nonNull(event.getDia2())) {
//            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getDia2() + "", reportRecord, RegularRecordEnum.DIASTOLIC, date));
//        }
//        if (Objects.nonNull(event.getPlu())) {
//            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getPlu() + "", reportRecord, RegularRecordEnum.HEART_RATE, date));
//        }
//        if (Objects.nonNull(event.getIad())) {
//            reportRegularRecords.add(reportRegularRecordService.dataPushSplit(event.getIad() + "", reportRecord, RegularRecordEnum.IAD, date));
//        }
//        return reportRegularRecords;
//    }
//}
