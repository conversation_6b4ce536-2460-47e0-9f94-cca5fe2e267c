package com.puree.followup.service;

import com.puree.followup.FollowUpApplication;
import com.puree.hospital.business.api.model.BusHospitalWechatConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = FollowUpApplication.class)
class ImWxMessageServiceTest {

    @Autowired
    private ImWxMessageService imWxMessageService;

    @Test
    void getHospitalWechatConfig() {
        BusHospitalWechatConfig hospitalWechatConfig = imWxMessageService.getHospitalWechatConfig(100L);
        log.info("{}", hospitalWechatConfig);
        Assertions.assertNotNull(hospitalWechatConfig.getOfficialAccountName());
    }
}