//package com.puree.followup.admin.medical.service;
//
//import com.alibaba.fastjson.JSONObject;
//import com.puree.followup.admin.medical.mapper.ReportRecordMapper;
//import com.puree.followup.admin.medical.service.impl.ReportRecordServiceImpl;
//import com.puree.followup.domain.medical.constant.IsHandleEnum;
//import com.puree.followup.domain.medical.constant.RecordTypeEnum;
//import com.puree.followup.domain.medical.model.ReportRecord;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//import java.util.Date;
//
//@Slf4j
//@ExtendWith({MockitoExtension.class, SpringExtension.class})
//@SpringBootTest
//public class ReportRegularRecordSummaryServiceImplTest {
//
//    @Autowired
//    private ReportRegularRecordSummaryService reportRegularRecordSummaryService;
//
////
////    /**
////     * @Param
////     * @Return void
////     * @Description 获取患者端首页
////     * <AUTHOR>
////     * @Date 2024/5/14 12:18
////     **/
////    @Test
////    void testPatientHome() {
////        Object o = reportRegularRecordSummaryService.patientHome(2L, 117L);
////        log.info("o:{}", JSONObject.toJSONString(o));
////
////    }
//
//}
