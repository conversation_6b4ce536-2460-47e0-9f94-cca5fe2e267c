# 医生端病例模板-技术方案

更新时间：2025-08-11
负责人：后端团队

## 1. 背景说明
- 业务需求：为医生端提供“病例常用模板”能力，支持医生创建/编辑/删除/应用模板，以提升录入效率和规范性。
- 现状：系统已有病历字段配置体系（配置驱动），患者真实病历存储于 `bus_ehr_case_diagnosis_history`；已有处方模板的独立模块。
- 痛点：
  - 模板与真实病历混存不便于权限控制、性能优化和安全隔离。
  - 医院配置需要影响模板应用时的字段显示/隐藏，需要可扩展的后端过滤能力。
- 目标：新建“医生病历模板”独立表与模块，实现模板的全生命周期管理，并复用既有配置驱动能力做字段级动态控制。

## 2. 目标与预期效果
- 目标
  - 新增独立的数据表与服务，解耦模板与真实病历。
  - 提供标准化的模板 CRUD 和应用接口，路径不包含 `/stage-api/` 与版本前缀（后续多版本时再引入）。
  - 复用医院配置进行字段级显示/隐藏的后端过滤，保证与医院后台配置一致。
  - 保障性能（热门模板/索引）、安全（权限/医生维度隔离）、可维护性（JSON自定义字段、扩展能力）。
- 预期效果
  - 医生模板可独立管理与快速复用，减少重复录入。
  - 与现有配置体系一致的字段控制体验，避免前后端不一致。
  - 最小化对既有业务的侵入与风险。

## 3. 技术实现方案
### 3.1 总体架构
- 模块：
  - 控制器：`app` 模块新增 `BusDoctorCaseTemplateController`
  - 服务：`IBusDoctorCaseTemplateService` + `BusDoctorCaseTemplateServiceImpl`
  - 数据访问：`BusDoctorCaseTemplateMapper` + `Mapper.xml`
  - 实体/DTO/VO：`BusDoctorCaseTemplate`、`CaseTemplateDto`、`CaseTemplateVo`
- 依赖：
  - 配置服务：`RemoteHospitalSettingApi.getSettingValue(key, hospitalId)` 获取医院字段显示配置
  - 公共：`R` 统一返回结构、`ServiceException` 异常、`SecurityUtils` 获取用户信息
- 关键策略：
  - 分表存储，职责单一，便于权限控制与性能优化。

### 3.2 数据库设计
- 新建表：`bus_doctor_case_template`

```sql
CREATE TABLE `bus_doctor_case_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  `hospital_id` bigint NOT NULL COMMENT '医院ID',
  `chief_complaint` text COMMENT '主诉',
  `history_of_present_illness` text COMMENT '现病史',
  `past_history` text COMMENT '既往史',
  `personal_history` text COMMENT '个人史',
  `diagnosis` text COMMENT '西医诊断',
  `tcm_diagnosis` text COMMENT '中医诊断',
  `management` text COMMENT '处置',
  `allergic_drugs` text COMMENT '过敏史',
  `advice` text COMMENT '医嘱',
  `template_description` varchar(500) COMMENT '模板描述',
  `is_delete` tinyint(1) DEFAULT 0 COMMENT '逻辑删除：0-否 1-是',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_doctor_hospital` (`doctor_id`, `hospital_id`, `is_delete`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='医生病历模板表';
```

- 说明：
  - 逻辑删除：`is_delete`，避免误删与支持恢复策略。

### 3.3 配置驱动的字段可见性
- 思路：模板“应用”时，后端根据医院配置动态过滤不可见字段，仅返回可见字段给前端。
- 配置键（建议，与现有配置键保持一致，实际以运营/医院配置为准）：
  - `case_chief_complaint_show`
  - `case_history_present_illness_show`
  - `case_past_history_show`
  - `case_personal_history_show`
  - `case_diagnosis_show`
  - `case_tcm_diagnosis_show`
  - `case_management_show`
  - `case_allergic_drugs_show`
  - `case_advice_show`
- 获取方式：`RemoteHospitalSettingApi.getSettingValue(key, hospitalId)`
  - 成功且值为 "1" 表示显示，否则为隐藏。
  - 调用失败/配置缺失默认显示（防止误伤）。

### 3.4 与现有流程的关系与差异
- 复用：
  - 病例字段集合与含义沿用真实病历。
  - 继续使用配置中心判断字段显示/隐藏，保持与医院后台配置一致。
- 差异：
  - 路由不再包含 `/stage-api/` 前缀与版本号（如 `/v1`），实际路径见 4.1。
  - 数据表独立为 `bus_doctor_case_template`，避免与真实病历共表存储。
  - 名称唯一性在医生+医院维度约束，便于医生个人管理与检索。

### 3.5 核心流程（时序图）

```mermaid
sequenceDiagram
    autonumber
    participant UI as 医生端（前端）
    participant C as Controller（/app/case-template）
    participant S as Service（模板服务）
    participant CFG as RemoteHospitalSettingApi
    participant DB as DB（bus_doctor_case_template）

    Note over UI: 医生点击“应用模板”
    UI->>C: POST /app/case-template/apply/{templateId} (headers: hospitalId)
    C->>S: applyTemplate(templateId, hospitalId)
    S->>DB: 根据 id+hospitalId 查询模板
    DB-->>S: 模板实体
    S->>CFG: 批量获取字段显示配置
    CFG-->>S: 配置KV（1=显示/0=隐藏）
    S->>S: 根据配置过滤返回字段
    S-->>C: R.ok(data)
    C-->>UI: 返回可见字段的模板内容
```

> 说明：新增/编辑/删除流程类似，均基于医生+医院维度进行权限与隔离控制。

## 4. 接口设计与改动
- 统一返回：`R`（不展开说明）
- 路由前缀：不包含 `/stage-api/` 与版本号。
- 推荐前缀：`/app/case-template`（模块标识 + 资源名），与现有“app模块”命名保持一致。

### 4.1 接口一览

1) 获取模板列表
- 方法：GET
- 路径：`/app/case-template/list`
- Headers：`hospitalId`、`doctorId`
- 响应：`R<List<CaseTemplateVo>>`

2) 获取模板详情
- 方法：GET
- 路径：`/app/case-template/{templateId}`
- Headers：`hospitalId`
- 响应：`R<CaseTemplateVo>`

3) 新增模板
- 方法：POST
- 路径：`/app/case-template`
- Headers：`hospitalId`、`doctorId`
- Body（JSON）示例：
```json
{
  "templateName": "上呼吸道感染常用",
  "chiefComplaint": "发热、咽痛3天",
  "historyOfPresentIllness": "起病急，伴咳嗽",
  "pastHistory": "既往体健",
  "personalHistory": "无",
  "diagnosis": "上呼吸道感染",
  "tcmDiagnosis": "风热感冒",
  "management": "对症治疗",
  "allergicDrugs": "青霉素过敏",
  "advice": "多饮水，多休息",
  "templateDescription": "门诊常用"
}
```
- 响应：`R<Void>`
- 备注：同名校验（医生+医院）

4) 更新模板
- 方法：PUT
- 路径：`/app/case-template`
- Headers：`hospitalId`、`doctorId`
- Body：同新增，需包含 `id`
- 响应：`R<Void>`

5) 删除模板
- 方法：DELETE
- 路径：`/app/case-template/{templateId}`
- Headers：`doctorId`
- 响应：`R<Void>`

6) 应用模板
- 方法：POST
- 路径：`/app/case-template/apply/{templateId}`
- Headers：`hospitalId`
- 响应（示例）：
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "chiefComplaint": "发热、咽痛3天",
    "historyOfPresentIllness": "起病急，伴咳嗽",
    "diagnosis": "上呼吸道感染",
    "advice": "多饮水，多休息"
  }
}
```
- 说明：返回字段已根据医院配置过滤

### 4.2 权限与安全
- 鉴权：沿用现有鉴权体系（如网关JWT），控制器使用权限注解（如：`@RequiresPermissions`）
- 资源隔离：所有查询/修改限定在 `doctorId`、`hospitalId` 维度
- 参数校验：DTO 注解（如 `@NotBlank`、`@NotNull`）
- 异常处理：业务异常使用 `ServiceException`，统一由全局异常处理器转换为 `R` 结构

### 4.3 与既有接口的差异
- 路由差异：本方案接口不包含 `/stage-api/` 与版本号；如与其他模块存在网关前缀差异，需要在网关层路由规则中补足映射，避免冲突。
- 资源命名：采用单数资源名 `case-template`，与现有规范一致（兼容开放型API设计）。

## 5. 兼容性与风险分析
- 兼容性
  - 与真实病历完全解耦，无需迁移老数据。
  - 配置缺失时默认显示字段，避免因配置问题影响医生使用。
- 风险点
  - 配置中心依赖：远端调用失败时默认显示，必要时增加降级与缓存（可引入本地缓存或Redis）。
  - 权限安全：删除/编辑仅限模板所有者（doctorId）。
- 回滚方案
  - 代码层：按 feature toggle 关闭控制器路由。
  - 数据层：逻辑删除保留数据；如需彻底回滚，先备份再执行 `DROP TABLE`（不推荐）。
  - 配置层：保留原有配置，无需回滚。

## 6. 发布与运维
- 上线步骤
  1. 执行 DDL 创建表与索引。
  2. 发布后端服务（含控制器/服务/DAO/XML）。
  3. 网关路由检查：确保不因取消 `/stage-api/`/版本号而导致冲突。
  4. 权限点配置：菜单/按钮/接口权限（如：`app:case-template:list|add|edit|remove|apply|query`）。
- 监控与告警
  - 核心接口 QPS、错误率、RT 监控。
  - 配置服务调用失败告警；必要时添加本地短期缓存（如 Caffeine）。
  - 数据库索引健康与慢查询监控。

## 7. 附录
### 7.1 字段映射对照（主要字段）
- chiefComplaint：主诉
- historyOfPresentIllness：现病史
- pastHistory：既往史
- personalHistory：个人史
- diagnosis：西医诊断
- tcmDiagnosis：中医诊断
- management：处置
- allergicDrugs：过敏史
- advice：医嘱

### 7.2 示例：后端字段过滤伪代码
```java
// 对齐电子病历做法：模板应用不做字段过滤，直接透传
Map<String, Object> out = new HashMap<>();
out.put("chiefComplaint", tpl.getChiefComplaint());
// ... 其他字段同理
return R.ok(out);
```

### 7.3 变更清单
- 新增表：`bus_doctor_case_template`
- 新增接口：`/app/case-template`*（详见 4.1）*
- 新增权限：`app:case-template:*`
- 配置依赖：医院字段显示配置（详见 3.3）

---
备注：统一返回类型使用 `R`。如需联调，请提供 `hospitalId`、`doctorId` 请求头，并按接口文档传参。
