package com.puree.hospital.business.api.factory;

import com.puree.hospital.business.api.RemoteBusTcmDiagnosisService;
import com.puree.hospital.common.api.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 中医诊断远程服务降级处理
 * <AUTHOR>
 * @date 2025-08-11
 */
@Component
public class RemoteBusTcmDiagnosisFallbackFactory implements FallbackFactory<RemoteBusTcmDiagnosisService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteBusTcmDiagnosisFallbackFactory.class);

    @Override
    public RemoteBusTcmDiagnosisService create(Throwable throwable) {
        log.error("中医诊断服务调用失败:{}", throwable.getMessage());
        return new RemoteBusTcmDiagnosisService() {
            @Override
            public R<com.puree.hospital.business.domain.BusTcmDiagnosis> getInfo(Long id) {
                return R.fail("获取中医诊断信息失败");
            }
        };
    }
}
