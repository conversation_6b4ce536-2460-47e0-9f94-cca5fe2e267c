package com.puree.hospital.business.api.factory;

import com.puree.hospital.business.api.RemoteBusDiseaseService;
import com.puree.hospital.common.api.domain.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 西医病种远程服务降级处理
 * <AUTHOR>
 * @date 2025-08-11
 */
@Component
public class RemoteBusDiseaseFallbackFactory implements FallbackFactory<RemoteBusDiseaseService> {

    private static final Logger log = LoggerFactory.getLogger(RemoteBusDiseaseFallbackFactory.class);

    @Override
    public RemoteBusDiseaseService create(Throwable throwable) {
        log.error("西医病种服务调用失败:{}", throwable.getMessage());
        return new RemoteBusDiseaseService() {
            @Override
            public R<com.puree.hospital.business.domain.BusDisease> getInfo(Long id) {
                return R.fail("获取西医病种信息失败");
            }
        };
    }
}
