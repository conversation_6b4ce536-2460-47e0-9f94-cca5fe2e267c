package com.puree.hospital.business.api;

import com.puree.hospital.business.api.factory.RemoteBusTcmDiagnosisFallbackFactory;
import com.puree.hospital.business.domain.BusTcmDiagnosis;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 中医诊断远程服务接口
 * <AUTHOR>
 * @date 2025-08-11
 */
@FeignClient(contextId = "RemoteBusTcmDiagnosisService", value = ServiceNameConstants.BUSINESS_SERVICE, fallbackFactory = RemoteBusTcmDiagnosisFallbackFactory.class)
public interface RemoteBusTcmDiagnosisService {

    /**
     * 根据ID获取中医诊断信息
     * @param id 中医诊断ID
     * @return 中医诊断信息
     */
    @GetMapping("/tcmDiagnosis/{id}")
    R<BusTcmDiagnosis> getInfo(@PathVariable("id") Long id);
}
