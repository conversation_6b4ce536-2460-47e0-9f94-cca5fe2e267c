package com.puree.hospital.business.api;

import com.puree.hospital.business.api.factory.RemoteBusDiseaseFallbackFactory;
import com.puree.hospital.business.domain.BusDisease;
import com.puree.hospital.common.api.constant.ServiceNameConstants;
import com.puree.hospital.common.api.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 西医病种远程服务接口
 * <AUTHOR>
 * @date 2025-08-11
 */
@FeignClient(contextId = "RemoteBusDiseaseService", value = ServiceNameConstants.BUSINESS_SERVICE, fallbackFactory = RemoteBusDiseaseFallbackFactory.class)
public interface RemoteBusDiseaseService {

    /**
     * 根据ID获取西医病种信息
     * @param id 病种ID
     * @return 病种信息
     */
    @GetMapping("/disease/{id}")
    R<BusDisease> getInfo(@PathVariable("id") Long id);
}
